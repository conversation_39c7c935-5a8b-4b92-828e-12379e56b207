{"root": true, "ignorePatterns": ["**/*.d.ts"], "overrides": [{"files": "**/*.+(ts|tsx)", "parser": "@typescript-eslint/parser", "extends": ["plugin:react/recommended", "prettier", "plugin:react/jsx-runtime"]}, {"files": "**/*.+(js|jsx)", "env": {"commonjs": true, "es6": true, "node": true}, "parserOptions": {"ecmaVersion": 2015}, "extends": ["eslint:recommended", "prettier", "plugin:react/jsx-runtime"]}], "settings": {"react": {"version": "detect"}}, "rules": {"react/jsx-uses-react": "off", "react/react-in-jsx-scope": "off", "react/prop-types": [2, {"ignore": ["className"]}]}, "parserOptions": {"sourceType": "module"}, "plugins": ["react", "react-hooks"]}