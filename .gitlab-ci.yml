image: nikolaik/python-nodejs:python3.13-nodejs23-alpine	

variables:
  PRODUCTION_BRANCH: 'zpi_production'
  STAGING_BRANCH: 'staging'
  DEVELOP_BRANCH: 'develop'


stages:
  - schedule_weekly
  - install_dependencies
  - test
  - code_quality_and_build
  - deploy

cache: &global_cache
  key:
    files:
      - package.json.md5
  paths:
    - .yarn/cache
    - node_modules
  policy: pull-push

prepare_cache:
  stage: install_dependencies
  tags:
    - docker-executor-8367
  script:
    # Auth private NPM
    - |
      {
        echo "registry=https://registry.npmjs.org"
        echo "@um:registry=https://repo.zalopay.vn/verdaccio/"
        echo "@zpi:registry=https://repo.zalopay.vn/verdaccio/"
        echo "@ce:registry=https://repo.zalopay.vn/verdaccio/"
        echo "@types:registry=https://repo.zalopay.vn/verdaccio/"
        echo "@mp:registry=https://repo.zalopay.vn/verdaccio/"
        echo "//repo.zalopay.vn/verdaccio/:_authToken=\${VERDACCIO_ZTOOL_TOKEN}"
      } | tee -a .npmrc
    - |
      if [ -d "node_modules" ]; then
        rm -rf .yarn
        rm -rf node_modules
      else
        corepack disable
        npm install -g yarn
        yarn config set proxy http://**********:8088
        yarn config set https-proxy http://**********:8088
        yarn config set strict-ssl false
        yarn install --frozen-lockfile
      fi
  only:
    - /^(releases\/|features\/|bugs\/).+$/
    - merge_requests
    - main
  except:
    - schedules
  cache:
    # inherit all global cache settings
    <<: *global_cache
    # override the policy
    policy: pull-push
jest:
  stage: test
  tags:
    - docker-executor-8367
  retry: 2
  script:
    # Auth private NPM
    - |
      {
        echo "registry=https://registry.npmjs.org"
        echo "@um:registry=https://repo.zalopay.vn/verdaccio/"
        echo "@zpi:registry=https://repo.zalopay.vn/verdaccio/"
        echo "@ce:registry=https://repo.zalopay.vn/verdaccio/"
        echo "//repo.zalopay.vn/verdaccio/:_authToken=\${VERDACCIO_ZTOOL_TOKEN}"
      } | tee -a .npmrc
    - |
      if [ -d "node_modules" ]; then
        echo "set proxy"
        corepack disable
        npm install -g yarn
        yarn config set proxy http://**********:8088
        yarn config set https-proxy http://**********:8088
        yarn config set strict-ssl false
        yarn install --frozen-lockfile
      fi
    - yarn test:ci
  only:
    - main
    - merge_requests
    - /^releases\/.+$/
  except:
    - schedules
  coverage: /All files[^|]*\|[^|]*\s+([\d\.]+)/
  artifacts:
    reports:
      junit:
        - junit.xml
      cobertura: coverage/cobertura-coverage.xml
    paths:
      - coverage/
  needs:
    - prepare_cache
  cache:
    # inherit all global cache settings
    <<: *global_cache
    # override the policy
    policy: pull

sonar_analyze:
  stage: code_quality_and_build
  image:
    name: registry-gitlab.zalopay.vn/docker/images/sonar-scanner-cli:4.8
    entrypoint: ['']
  variables:
    SONAR_USER_HOME: '${CI_PROJECT_DIR}/.sonar'
    GIT_DEPTH: '0'
  cache:
    key: '${CI_JOB_NAME}'
    paths:
      - .sonar/cache
  before_script:
    - sonar_projectKey=$(echo "zlp:$CI_PROJECT_PATH" | tr / :)
  script:
    - sonar-scanner -Dsonar.host.url=${SONAR_HOST_URL} -Dsonar.login=${SONAR_TOKEN} -Dsonar.projectKey=${sonar_projectKey} -Dsonar.projectName=${sonar_projectKey}
  only:
    - main
  except:
    - schedules
  tags:
    - docker-executor-8367
  needs:
    - jest
  artifacts:
    paths:
      - coverage/

deploy_jenkins_prod:
  cache: []
  when: manual
  stage: deploy
  script:
    - sh scripts/deploy-jenkins.sh pro $PRIVATE_TOKEN
  only:
    - /^releases\/.+$/
  needs:
    - prepare_cache
  tags:
    - shell-executor-8367

deploy_zpi_qc:
  retry: 2
  when: manual
  stage: deploy
  script:
    - |
      if [ -d "node_modules" ]; then
            echo "set proxy"
            corepack disable
            npm install -g yarn
            yarn config set proxy http://**********:8088
            yarn config set https-proxy http://**********:8088
            yarn config set strict-ssl false
          fi
    - sh scripts/build-deploy-zpi.sh qc
  artifacts:
    paths:
      - dist/
  only:
    - /^(releases\/|features\/|bugs\/).+$/
  tags:
    - docker-executor-8367
  needs:
    - prepare_cache
  cache:
    # inherit all global cache settings
    <<: *global_cache
    # override the policy
    policy: pull

deploy_zpi_stg:
  retry: 2
  when: manual
  stage: deploy
  script:
    - sh scripts/build-deploy-zpi.sh stg
  artifacts:
    paths:
      - dist/
  only:
    - /^(releases|develop)\/.+$/
  tags:
    - docker-executor-8367
  needs:
    - prepare_cache
  cache:
    # inherit all global cache settings
    <<: *global_cache
    # override the policy
    policy: pull

# deploy_zpi_prod:
#   when: manual
#   stage: deploy
#   script:
#     - sh scripts/build-deploy-zpi.sh prod
#   artifacts:
#     paths:
#       - dist/
#   only:
#     - /^releases\/.+$/
#   tags:
#     - docker-executor-8367
#   needs:
#     - prepare_cache
#   cache:
#     # inherit all global cache settings
#     <<: *global_cache
#     # override the policy
#     policy: pull

mr_review:
  stage: .pre
  tags:
    - shell-executor-8367
  script:
    - chmod +x bin/mr-review.sh
    - chmod +r bin/teams_templates/teams-msg-template.txt
    - curl --request GET --header "PRIVATE-TOKEN:${PRIVATE_TOKEN}" "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/merge_requests/${CI_MERGE_REQUEST_IID}" | bin/mr-review.sh
  only:
    - merge_requests
