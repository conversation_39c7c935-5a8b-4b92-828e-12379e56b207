#!/usr/bin/env bash
# Copyright Son Nguyen <PERSON> (sonnt13). More detail in https://confluence.zalopay.vn/x/GeHABw

read -r mr_details

maintainer_emails=( ${PROJECT_MAINTAINER_EMAILS} )

echo "Commit description ${CI_COMMIT_DESCRIPTION}"
echo "Commit message ${CI_COMMIT_MESSAGE}"
echo "Merge request title ${CI_MERGE_REQUEST_TITLE}"
echo "Project title ${CI_PROJECT_TITLE}"
echo "User email ${GITLAB_USER_EMAIL}"

mr_title=${CI_MERGE_REQUEST_TITLE}
mr_url="${CI_MERGE_REQUEST_PROJECT_URL}/-/merge_requests/${CI_MERGE_REQUEST_IID}"

gitlab_mr_url="${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/merge_requests/${CI_MERGE_REQUEST_IID}"

echo "${PWD}"

function domainOf() {
    email=$1
    echo "$email" | cut -d'@' -f1
}

function mention() {
    domain=$1
    echo "<at>${domain}</at>"
}

function jsonVal() {
  input=$1
  tokens=( ${input//./ } )
  cmd="import sys,json; data=json.loads(sys.stdin.read()); print(data"
  for token in "${tokens[@]}"
  do
    cmd+="[$token]"
  done
  cmd+=")"
  python -c "$cmd"
}

created_at="$(echo "$mr_details" | jsonVal "'created_at'")"
echo "Created At: $created_at"
updated_at="$(echo "$mr_details" | jsonVal "'updated_at'")"
echo "Updated At: $updated_at"
if [[ $created_at != "$updated_at" ]]
then
  echo "Review request sent. Skip running."
  exit 0
fi

mr_creator_email=${GITLAB_USER_EMAIL}
mr_creator_domain=$(domainOf "$mr_creator_email")
requester=$(mention "$mr_creator_domain") # this will be used as environment variable, please don't remove

reviewer_emails=( "${reviewer_emails[@]/$mr_creator_email}" ) # Remove creator from reviewers

mention_entities=""
reviewer_mentions=""
c=0
for maintainer_email in "${maintainer_emails[@]}"
do
  maintainer_domain=$(domainOf "$maintainer_email")

  if [[ $c != 0 ]]
  then
    mention_entities+=","
    reviewer_mentions+=" "
  fi

  if [[ $maintainer_email != "$mr_creator_email" ]]
  then
    reviewer_mentions+=$(mention "$maintainer_domain")
  fi

  mention_entities+="{
                        \"type\": \"mention\",
                        \"text\": \"$(mention "$maintainer_domain")\",
                        \"mentioned\": {
                                          \"id\": \"${maintainer_email}\",
                                          \"name\": \"${maintainer_domain}\"
                                        }
                      }"
  c+=1
done

message="$(while read -r line || [ -n "$line" ];
           do
               eval echo "$line"
           done < ./bin/teams_templates/teams-msg-template.txt)"


curl --request POST "${MR_REVIEW_BOT_URL}" --header "Accept:application/json" \
--header "Content-Type:application/json" --data "${message}"

exit 0