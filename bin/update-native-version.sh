#!/usr/bin/env bash

HOME=$(pwd)

# Check exist and install ripgrep
if ! [ -x "$(command -v rg)" ]; then
  apk add ripgrep
fi

# Check exist and install yq
if ! [ -x "$(command -v yq)" ]; then
  apk add yq
fi

# Check exist and install curl
if ! [ -x "$(command -v curl)" ]; then
  apk add curl
fi

CREATE_DATE=$(date +"%d-%m-%Y")
APP_ID=$1
PRIVATE_TOKEN=$2
MIN_VERSION_STR=
SOURCE_BRANCH="update_native_version_${CREATE_DATE}"
TARGET_BRANCH=$CI_DEFAULT_BRANCH
MR_TITLE="#config: auto update native version ${CREATE_DATE}"
REPO_URL="https://gitlab-ci-token:${PRIVATE_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git"

#replace . with empty
MIN_VERSION=${MIN_VERSION_STR//./}

#get session_id to making fetch native version request
echo "INFO: requesting CPS session_id..."
JSESSIONID=$(curl -s -c - 'https://qccps-cstool.zalopay.com.vn/vnglogin' -d 'username=cdn_admin&password=1' |  rg -o 'JSESSIONID\s+([a-z0-9]+)$' -r '$1')
if [ -z "${JSESSIONID}" ]; then
  echo "ERROR: failed to request CPS session_id"
  exit 1;
else
  echo "SUCCESS: CPS session_id is requested"
fi

#fetch native versions then modify to usable json format
echo "INFO: fetching native version..."
VERSIONS_JSON=$(curl -s -b "JSESSIONID=${JSESSIONID}" 'https://qccps-cstool.zalopay.com.vn/cdn/config/resource/insideapp/selectall' |
jq -r '.data' | #format data field to json object
jq 'map(.appversion) | unique' |  #extract field appversion and remove duplicated
jq --arg min_ver $MIN_VERSION 'map(select(. | split(".") | .[0:2] | join("") | tonumber | if . < 100 then . * 10  else .  end | if . > 1000 then . / 10  else .  end | . >= ($min_ver | tonumber)))' |
jq "{app_id: $APP_ID, zlp_version: { qc: ., staging: ., production: .}}" | jq .) #create usable json object
echo $VERSIONS_JSON

if [ -z "${VERSIONS_JSON}" ]; then
  echo "ERROR: failed to fetch native version"
  exit 1;
else
  echo "SUCCESS: native version is fetched"
fi

#format json file to yaml file then write to file config.yml
echo "INFO: updating config.yml..."
echo $VERSIONS_JSON | yq -P > "${HOME}/config.yml"

if [ -n "$(git diff  "${HOME}/config.yml")" ]; then
  echo "INFO: config.yml has been updated"
  git remote add origin "$REPO_URL" || true
  git remote set-url origin "$REPO_URL" || true
  git checkout -b "$SOURCE_BRANCH"
  git commit --no-verify -a -m "#config: auto update native version ${CREATE_DATE}"
  git push --no-verify --set-upstream origin "$SOURCE_BRANCH"
  sh "${HOME}/bin/create-merge-request.sh" "$PRIVATE_TOKEN" "$CI_PROJECT_ID" "$GITLAB_USER_ID" "$MR_TITLE" "$SOURCE_BRANCH" $TARGET_BRANCH
  exit;
else
  echo "INFO: config.yml has no changes"
  exit;
fi
