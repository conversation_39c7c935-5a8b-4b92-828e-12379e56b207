ENV="qc" # available values: qc, stg (empty for prod)
JSESSIONID=""
APP_ID=""
MIN_VERSION_STR="" # currently only support version format: x.y
MAX_VERSION_STR="" # currently only support version format: x.y

if [ -z $MAX_VERSION_STR ] && [ -z $MIN_VERSION_STR ]; then
  echo "ERROR: MAX_VERSION_STR or MIN_VERSION_STR must be provided"
  exit 1
fi

if [ -z $APP_ID ]; then
  echo "ERROR: APP_ID must be provided"
  exit 1
fi

if [ -z $JSESSIONID ]; then
  echo "ERROR: JSESSIONID must be provided. Login to https://${ENV}cps-cstool.zalopay.com.vn to get JSESSIONID"
  exit 1
fi

echo "INFO: Running script in $ENV environment..."

#replace . with empty
MAX_VERSION=${MAX_VERSION_STR//./}
MIN_VERSION=${MIN_VERSION_STR//./}

#fetch current active versions
VERSIONS=$(curl -s -b "JSESSIONID=${JSESSIONID}" "https://${ENV}cps-cstool.zalopay.com.vn/cdn/config/resource/insideapp/selectall" |
#format data field to json object
jq -r '.data' |
#extract field appversion and remove duplicated
jq 'map(.appversion) | unique')

if [ -z $MIN_VERSION ]; then
  echo "INFO: skip filter min version..."
else
  echo "INFO: filtering only versions greater than ${MIN_VERSION_STR}..."
  VERSIONS=$(echo $VERSIONS | jq --arg min_ver $MIN_VERSION 'map(select(. | split(".") | .[0:2] | join("") | tonumber | if . < 100 then . * 10  else .  end | if . > 1000 then . / 10  else .  end | . >= ($min_ver | tonumber)))')
  echo $VERSIONS
fi

if [ -z $MAX_VERSION ]; then
  echo "INFO: skip filter max version..."
else
  echo "INFO: filtering only versions lower than ${MAX_VERSION_STR}..."
  VERSIONS=$(echo $VERSIONS | jq --arg max_ver $MAX_VERSION 'map(select(. | split(".") | .[0:2] | join("") | tonumber | if . < 100 then . * 10  else .  end | if . > 1000 then . / 10  else .  end | . <= ($max_ver | tonumber)))')
  echo $VERSIONS
fi

#validate versions
if [ ${#VERSIONS[@]} -eq 0  ]; then
  echo "ERROR: failed to fetch native version"
  exit 1;
else
  echo "SUCCESS: native version is fetched"
  echo $VERSIONS
fi

echo $VERSIONS |  jq -r '.[]'  |
while read -r version; do
   echo "INFO: add mini-app version to native version ${version}..."
    RESPONSE=$(curl -sw "%{http_code}" "https://${ENV}cps-cstool.zalopay.com.vn/cdn/config/resource/insideapp/insert" \
      -H 'content-type: application/x-www-form-urlencoded; charset=UTF-8' \
      -H "cookie: JSESSIONID=${JSESSIONID}" \
      --data-raw "appversion=${version}&appid=${APP_ID}&insideorder=0&insidehighlight=0&insideneeddownload=1&releasestatus=1&iosstatus=1&andstatus=1&apptype=1&maintenancefrom=&maintenanceto=&weburl=&displaytype=1"
      )

    HTTP_CODE="${RESPONSE:${#RESPONSE}-3}"
    BODY="${RESPONSE:0:${#RESPONSE}-3}"

    if [ $HTTP_CODE -eq 200  ]; then
       echo "SUCCESSFUL: add mini-app version successfully"
    else
      echo "ERROR: failed when add mini-app version (code: ${HTTP_CODE}, body: ${BODY})"
      exit 1
    fi
done
