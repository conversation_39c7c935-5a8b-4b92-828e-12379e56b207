#!/usr/bin/env bash

function file_content_md5 {
  cat "$1" | openssl dgst -md5 -binary | xxd -p
}

CREATE_DATE=$(date +"%d-%m-%Y")
HOME=$(pwd)
MD5="
root: $(file_content_md5 "package.json"),
zpa: $(file_content_md5 "packages/zpa/package.json"),
shared: $(file_content_md5 "packages/shared/package.json"),
zpi: $(file_content_md5 "packages/zpi/package.json")
"

#clear file content before write
echo "" > "${HOME}/package.json.md5"

echo $MD5 > "${HOME}/package.json.md5"

if [ -n "$(git diff  "${HOME}/package.json.md5")" ]; then
  echo "INFO:  package.json.md5 has been updated"
  git add "${HOME}/package.json.md5"
  git commit --no-verify -m "#config: auto update package.json md5 file ${CREATE_DATE}"
  echo "INFO: new md5 hash file is committed"
fi
