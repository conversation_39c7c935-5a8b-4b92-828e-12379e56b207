#!/usr/bin/env zx

$.verbose = false;

const APP_ID = 1841;

//#region
const step = async (title, callback) => {
  if (!callback) {
    console.log(chalk.yellow(`===== TODO - ${title}`));
    return;
  }
  console.log(chalk.blue(`====== ${title}`));
  await callback();
};

const panic = message => {
  console.error(chalk.red(message));
  process.exit(1);
};

const stdout = output => {
  return output.stdout.trim();
};

const git = stdout(await $`git rev-parse --show-toplevel`);
const root = async dir => {
  return path.join(git, dir);
};

const hash = content => {
  const crypto = require('crypto');
  const hashSum = crypto.createHash('sha256');
  hashSum.update(content);
  return hashSum.digest('hex');
};
//#endregion

let domain, jenkins_token, branch, version, tag, jenkinsBuild, zip;

//#region
const buildConfigYml = versions => ({
  app_id: APP_ID,
  zlp_version: { qc: versions, staging: versions, production: versions },
});

const jenkinsApi = async buildNumber => {
  if (buildNumber) {
    return await $`curl -s --user "${domain}:${jenkins_token}" "https://new-jenkins.zalopay.vn/job/fin/job/fixed-deposit/job/fd-app/job/system/job/create-ticket/${buildNumber}/api/xml"`;
  } else {
    return await $`curl -s --user "${domain}:${jenkins_token}" "https://new-jenkins.zalopay.vn/job/fin/job/fixed-deposit/job/fd-app/job/system/job/create-ticket/api/xml"`;
  }
};

const jenkinsFetchLogApi = async buildNumber => {
  return await $`curl -s --user "${domain}:${jenkins_token}" "https://new-jenkins.zalopay.vn/job/fin/job/fixed-deposit/job/fd-app/job/system/job/create-ticket/${buildNumber}/logText/progressiveText?start=0"`;
};

let cpsCookie;
const cpsCookieApi = async () => {
  if (!cpsCookie) {
    cpsCookie =
      await $`curl -s -c - 'https://qccps-cstool.zalopay.com.vn/vnglogin' -d 'username=cdn_admin&password=1' | rg -o 'JSESSIONID\\s+([a-z0-9]+)$' -r '$1'`;
  }
  return cpsCookie;
};

const cpsReloadResourceApi = async () => {
  await $`curl -s -b "JSESSIONID=${await cpsCookieApi()}" 'https://qccps-cstool.zalopay.com.vn/cdn/config/statics' -d 'action=reload&configcode=5&subconfigcode=0' | htmlq -t '#response pre' | jq '.returncode'`;
};

const versionComparer = (a, b) => {
  const [a1, a2, a3] = a.split('.').map(c => parseInt(c));
  const [b1, b2, b3] = b.split('.').map(c => parseInt(c));
  if (a1 < b1) {
    return -1;
  } else if (a1 > b1) {
    return 1;
  }
  if (a2 < b2) {
    return -1;
  } else if (a2 > b2) {
    return 1;
  }
  if (a3 < b3) {
    return -1;
  } else if (a3 > b3) {
    return 1;
  }
  return 0;
};
const cpsGetNativeVersionsApi = async () => {
  const response = stdout(
    await $`curl -s -b "JSESSIONID=${await cpsCookieApi()}" 'https://qccps-cstool.zalopay.com.vn/cdn/config/resource/insideappresource/selectall'`,
  );
  const versions = JSON.parse(JSON.parse(response).data)
    .filter(app => app.appid.startsWith(`${APP_ID} - `))
    .map(app => app.appversion);
  return [...new Set(versions)].sort(versionComparer);
};
//#endregion

if (argv.h || argv.help) {
  await step('Print help');
  process.exit();
}

if (argv.c) {
  await step('Update config.yml', async () => {
    const versions = await cpsGetNativeVersionsApi();
    const config = buildConfigYml(versions);
    const output = stdout(await $`echo ${JSON.stringify(config)} | yq -p j -o y`);
    fs.writeFileSync(await root('./config.yml'), output);
  });
  process.exit();
}

await step('Ensure MacOS', async () => {
  const os = stdout(await $`uname -s`);
  os !== 'Darwin' && panic('MacOS is required!');
});

await step('Ensure dependencies', async () => {
  try {
    await Promise.all([
      $`rg --version > /dev/null`,
      $`git --version > /dev/null`,
      $`yq --version > /dev/null`,
      $`htmlq --version > /dev/null`,
    ]);
  } catch {
    panic('rg, git, yq and htmlq are required, install with:\nbrew install rg, git, yq, htmlq');
  }
});

await step('Ensure tokens', async () => {
  domain = process.env.JENKINS_DOMAIN;
  jenkins_token = process.env.JENKINS_TOKEN;
  if (!domain || !jenkins_token) {
    panic('JENKIN_DOMAIN and JENKIN_TOKEN are required, export them then re-run this script.');
  }
});

await step('Ensure version branch', async () => {
  try {
    branch = stdout(await $`git branch --show-current | rg '^version/\\d+\\.\\d+\\.\\d+$'`);
  } catch (e) {
    console.error(e);
    panic('The current git branch is not a version branch!');
  }
  version = stdout(await $`echo ${branch} | rg 'version/' -r ''`);
});

await step('Ensure no other tags');

await step('Ensure latest native versions', async () => {
  const versions = await cpsGetNativeVersionsApi();
  const config = buildConfigYml(versions);
  const output = stdout(await $`echo ${JSON.stringify(config)} | yq -p j -o y`);
  if (hash(fs.readFileSync(await root('./config.yml'))) !== hash(output)) {
    panic('config.yml is outdated, update it by running:\n./bin/trigger-build.mjs -c');
  }
});

await step('Create & push the new tag', async () => {
  tag = `${version}-${stdout(await $`date '+%d%m%Y.%H%M'`)}`;
  await $`git tag ${tag}`;
  try {
    await $`git push origin ${tag}`;
  } catch {
    panic('Unable to push the tag!');
  }
});

await step('Find Jenkins build number', async () => {
  while (1) {
    const lastBuild = stdout(await $`echo ${await jenkinsApi()} | yq -p xml '.workflowJob.lastBuild.number'`);
    jenkinsBuild = stdout(
      await $`echo ${await jenkinsApi(
        lastBuild,
      )} | yq -p xml '.workflowRun.action.[] | select(.+_class == "hudson.plugins.git.util.BuildData") | .buildsByBranchName | select(. | has("tags'"${tag}"'")) | ."tags'"${tag}"'".buildNumber'`,
    );
    if (!jenkinsBuild) {
      echo('Unable to find the build number, will retry in 5 seconds...');
      await sleep(5000);
    } else {
      echo(chalk.green(`Found the build number ${jenkinsBuild}!`));
      break;
    }
  }
});

await step('Watch Jenkins build', async () => {
  while (1) {
    const building = stdout(await $`echo ${await jenkinsApi(jenkinsBuild)} | yq -p xml '.workflowRun.building'`);
    if (building === 'false') {
      break;
    } else {
      echo(`Build ${jenkinsBuild} is running, will retry in 5 seconds...`);
      await sleep(5000);
    }
  }
});

await step('Parse the zip string', async () => {
  const result = stdout(await $`echo ${await jenkinsApi(jenkinsBuild)} | yq -p xml '.workflowRun.result'`);
  result !== 'SUCCESS' && panic('The build failed!');

  const log = stdout(await jenkinsFetchLogApi(jenkinsBuild));
  zip = log.match(/[a-z0-9\._]+\.zip/)[0];
  zip || panic('Unable to find the zip string!');
  echo(chalk.green(`Found the zip string: ${zip}`));
});

await step('Trigger reload resource', async () => {
  try {
    await cpsReloadResourceApi();
  } catch {
    panic('Unable to reload the config!');
  }
});

await step('Verify using CDN API');
