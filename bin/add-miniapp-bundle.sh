ENV="qc" # available values: qc, stg (empty for prod)
JSESSIONID=""
APP_ID=""
MIN_VERSION_STR="" # currently only support version format: x.y.z
MAX_VERSION_STR="" # currently only support version format: x.y.z
BUNDLE_SOURCE="" #example: 1841.2.4.0.20230702_878f9c60.zip
APP_ICON_NAME=""

if [ -z $MAX_VERSION_STR ] && [ -z $MIN_VERSION_STR ]; then
  echo "ERROR: MAX_VERSION_STR or MIN_VERSION_STR must be provided"
  exit 1
fi

if [ -z $APP_ID ]; then
  echo "ERROR: APP_ID must be provided"
  exit 1
fi

if [ -z $JSESSIONID ]; then
  echo "ERROR: JSESSIONID must be provided. Login to https://${ENV}cps-cstool.zalopay.com.vn to get JSESSIONID"
  exit 1
fi

if [ -z $BUNDLE_SOURCE ]; then
  echo "ERROR: BUNDLE_SOURCE must be provided"
  exit 1
fi

echo "INFO: Running script in $ENV environment..."

#replace . with empty
MAX_VERSION=${MAX_VERSION_STR//./}
MIN_VERSION=${MIN_VERSION_STR//./}

#fetch current active versions
VERSIONS=$(curl -s -b "JSESSIONID=${JSESSIONID}" "https://${ENV}cps-cstool.zalopay.com.vn/cdn/config/resource/insideapp/selectall" |
#format data field to json object
jq -r '.data' |
#extract field appversion and remove duplicated
jq 'map(.appversion) | unique')

if [ -z $MIN_VERSION ]; then
  echo "INFO: skip filter min version..."
else
  echo "INFO: filtering only versions greater than ${MIN_VERSION_STR}..."
  VERSIONS=$(echo $VERSIONS | jq --arg min_ver $MIN_VERSION 'map(select(. | split(".") | .[0:2] | join("") | tonumber | if . < 100 then . * 10  else .  end | if . > 1000 then . / 10  else .  end | . >= ($min_ver | tonumber)))')
  echo $VERSIONS
fi

if [ -z $MAX_VERSION ]; then
  echo "INFO: skip filter max version..."
else
  echo "INFO: filtering only versions lower than ${MAX_VERSION_STR}..."
  VERSIONS=$(echo $VERSIONS | jq --arg max_ver $MAX_VERSION 'map(select(. | split(".") | .[0:2] | join("") | tonumber | if . < 100 then . * 10  else .  end | if . > 1000 then . / 10  else .  end | . <= ($max_ver | tonumber)))')
  echo $VERSIONS
fi

#validate versions
if [ ${#VERSIONS[@]} -eq 0  ]; then
  echo "ERROR: failed to fetch native version"
  exit 1;
else
  echo "SUCCESS: native version is fetched"
fi

echo "INFO: add mini-app bundle source to Android"
echo $VERSIONS |  jq -r '.[]'  |
while read -r version; do
    ANDROID_RESPONSE=$(curl -sw "%{http_code}" "https://${ENV}cps-cstool.zalopay.com.vn/cdn/config/resource/insideappresource/insert" \
      -H 'content-type: application/x-www-form-urlencoded; charset=UTF-8' \
      -H "cookie: JSESSIONID=${JSESSIONID}" \
      --data-raw "appversion=${version}&appid=${APP_ID}&platformcode=android&status=1&imagebundleversion=${BUNDLE_SOURCE}&jsbundleversion=${BUNDLE_SOURCE}&iconname=${APP_ICON_NAME}&iconcolor=&icontype=1")
    ANDROID_HTTP_CODE="${ANDROID_RESPONSE:${#ANDROID_RESPONSE}-3}"
    ANDROID_BODY="${ANDROID_RESPONSE:0:${#ANDROID_RESPONSE}-3}"
    if [ $ANDROID_HTTP_CODE -eq 200  ]; then
       echo "SUCCESSFUL: [Android] add mini-app bundle to version: ${version}"
        echo $ANDROID_BODY
    else
      echo "ERROR: [Android] failed when add mini-app bundle (code: ${ANDROID_HTTP_CODE}, body: ${ANDROID_BODY})"
      exit 1
    fi
done

echo "INFO: add mini-app bundle source to iOS"
echo $VERSIONS |  jq -r '.[]'  |
while read -r version; do
    IOS_RESPONSE=$(curl -sw "%{http_code}" "https://${ENV}cps-cstool.zalopay.com.vn/cdn/config/resource/insideappresource/insert" \
      -H 'content-type: application/x-www-form-urlencoded; charset=UTF-8' \
      -H "cookie: JSESSIONID=${JSESSIONID}" \
      --data-raw "appversion=${version}&appid=${APP_ID}&platformcode=ios&status=1&imagebundleversion=${BUNDLE_SOURCE}&jsbundleversion=${BUNDLE_SOURCE}&iconname=${APP_ICON_NAME}&iconcolor=&icontype=1")
    IOS_HTTP_CODE="${IOS_RESPONSE:${#IOS_RESPONSE}-3}"
    IOS_BODY="${IOS_RESPONSE:0:${#IOS_RESPONSE}-3}"
    if [ $IOS_HTTP_CODE -eq 200  ]; then
        echo $IOS_BODY
       echo "SUCCESSFUL: [iOS] add mini-app bundle to version: ${version}"
    else
      echo "ERROR: [iOS] failed when add mini-app bundle (code: ${IOS_HTTP_CODE}, body: ${IOS_BODY})"
      exit 1
    fi
done
