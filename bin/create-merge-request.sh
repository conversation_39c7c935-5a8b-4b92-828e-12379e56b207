#!/usr/bin/env bash

HOST="https://gitlab.zalopay.vn/api/v4/projects/"

PRIVATE_TOKEN=$1
PROJECT_ID=$2
GITLAB_USER_ID=$3
TITLE=$4
SOURCE_BRANCH=$5
TARGET_BRANCH=$6

BODY="{
    \"id\": ${PROJECT_ID},
    \"source_branch\": \"${SOURCE_BRANCH}\",
    \"target_branch\": \"${TARGET_BRANCH}\",
    \"remove_source_branch\": true,
    \"title\": \"${TITLE}\",
    \"assignee_id\":\"${GITLAB_USER_ID}\",
    \"description\": \"This merge request is auto created weekly to update new native app version into config.yml file.\"
}";

# Require a list of all the merge request and take a look if there is already
# one with the same source branch
LISTMR=`curl --silent "${HOST}${PROJECT_ID}/merge_requests?state=opened" --header "PRIVATE-TOKEN:${PRIVATE_TOKEN}"`;
COUNTBRANCHES=`echo ${LISTMR} | grep -o "\"source_branch\":\"${SOURCE_BRANCH}\"" | wc -l`;

# No MR found, let's create a new one
if [ ${COUNTBRANCHES} -eq "0" ]; then
    curl -X POST "${HOST}${PROJECT_ID}/merge_requests" \
        --header "PRIVATE-TOKEN:${PRIVATE_TOKEN}" \
        --header "Content-Type: application/json" \
        --data "${BODY}";

    echo "\nOpened a new merge request: ${SOURCE_BRANCH} and assigned to you";
    exit;
fi

echo "\nNo new merge request opened";