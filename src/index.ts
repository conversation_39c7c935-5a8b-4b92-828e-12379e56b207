// A fake export to make this file a module
export const _do_not_use_this_value_ = null;

export enum ZPI_ENV {
  SANDBOX_QC = 'sandboxqc',
  STAGING = 'staging',
  PRODUCTION = 'production',
}

declare global {
  interface Window {
    // add you custom properties and methods
    __BASE_NAME__: string;
    __USER_INFO__: any;
    __APP_INFO__: {
      env: string;
      ver: string;
    };
    UM_PIN: any;
    ZPI_TRACKING_SDK: {
      logAction(eventID: string, metadata: string, callback?: (payload: any) => void): void;
    };
    ZPI_SPA_SDK: {
      navigateTo: (url: string, option?: any) => void;
      navigateWithClear: (path: string, state?: object) => void;
    };
    __ZPI_ZMP_SDK__: {
      getZlpToken(): { zlp_token: string };
    };
    Sentry: {
      captureMessage: (message: string, context?: any) => void;
      captureException: (error: any, context?: any) => void;
      configureScope: (callback: (scope: any) => void) => void;
    };
    Cashier: any;
    zlpSdk: any;
  }
}
export const __DEV__: boolean = false;
export const __APP_NAME__: string = '';