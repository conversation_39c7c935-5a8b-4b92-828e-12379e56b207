export interface StatementListPayload {
    account_id: string;
    from_time: string;
    to_time: string;
  }
  
  export interface StatementList {
    statements: Statement[];
  }

  export enum StatementStatus {
    PAID_STATUS_UNSPECIFIED = "PAID_STATUS_UNSPECIFIED",
    PAID_STATUS_PAID = "PAID_STATUS_PAID",
    PAID_STATUS_UNPAID = "PAID_STATUS_UNPAID",
    PAID_STATUS_PARTIALLY_PAID = "PAID_STATUS_PARTIALLY_PAID",
  }
  export interface Statement {
    account_id: string;
    due_date: string;
    id: string;
    incurred_date: string;
    installment_fee_amount?: string;
    installment_fee_repaid?: string;
    outstanding_amount?: string;
    penalty_amount?: string;
    outstanding_repaid?: string;
    paid_status: string;
    total_due_amount: string;
    total_due_remaining: string;
    total_due_repaid: string;
  }
  
  export enum StatementPaidStatus {
    PAID_STATUS_UNSPECIFIED = "PAID_STATUS_UNSPECIFIED",
    PAID_STATUS_PAID = "PAID_STATUS_PAID",
    PAID_STATUS_UNPAID = "PAID_STATUS_UNPAID",
    PAID_STATUS_PARTIALLY_PAID = "PAID_STATUS_PARTIALLY_PAID",
  }