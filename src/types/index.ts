export enum ApiStatus {
  success = 0,
  fail = 1,
}

export enum HttpRequestVerbs {
  GET = "GET",
  POST = "POST",
  PUT = "PUT",
  PATCH = "PATCH",
  DELETE = "DELETE",
}

export enum HttpResponseCode {
  AUTHEN_FAILED = 401,
  SUCCESS = 200,
  SERVICE_UNAVAILABLE = 503,
  TOO_MANY_REQUEST = 429,
}

export enum ServiceResponseSubcode {
  SUCCESS = 0,
  INVALID_USER_PROFILE = 105,
  TO0_MANY_REQUESTS = 401,
  INVALID_CORP_BALANCE = 508,
}

export interface BasicUserProfile {
  full_name: string;
  phone_number: string;
  birth_day: string;
  id_number: string;
}

export interface BaseResponse<T> {
  code: number | string;
  message: string;
  data: T;
}

export enum INTERNAL_ERROR {
  NON_WHITELIST = "user-not-in-whitelist",
  NON_EKYC = "user-not-e-kyc",
  CALL_FUNC_OPEN_AUTH_CHALLENGE_FAILED = "CALL_FUNC_OPEN_AUTH_CHALLENGE_FAILED",
}

export enum KYC_SOURCE {
  CREATE = 67,
  UPDATE = 68,
}

export interface AppInfo {
  appVersion: string;
  environment: string;
  isIframe: boolean;
  os: string;
  platform: string;
  userAgent: string;
}

export interface ResourceTypes {
  type: string;
  data: { code: string; vietnamese: string; english: string }[];
}

export interface OnboardingAction {
  code: OnboardingActionCode;
  title: string;
  variant: ActionVariant | undefined;
  zpaActionUrl: string;
  zpiActionUrl: string;
  metadata?: JSON;
}

export const enum OnboardingActionCode {
  CONTACT_CS = "CONTACT_CS",
  DO_KYC = "DO_KYC",
  UPDATE_KYC = "UPDATE_KYC",
  CONTACT_PARTNER = "CONTACT_PARTNER",
  NAVIGATE_TO_HOME = "NAVIGATE_TO_HOME",
  REGISTER_ONBOARDING = "REGISTER_ONBOARDING",
  UPDATE_KYC_NFC = "UPDATE_KYC_NFC",
  UPDATE_NFC = "UPDATE_NFC",
  RETRY = "RETRY",
  CLOSE_NOTICE = "CLOSE_NOTICE",
  UPDATE_PHONE = "UPDATE_PHONE",
  RE_REGISTER_ONBOARDING = "RE_REGISTER_ONBOARDING",
  RESET_NFC = "RESET_NFC",
  LINK_ACCOUNT = "LINK_ACCOUNT",
}

export const enum ActionVariant {
  PRIMARY = "primary",
  SECONDARY = "secondary",
  OUTLINED = "outlined",
  GHOST = "ghost",
  LINK = "link",
  TEXT = "text",
}

export interface ProfileIssue {
  code: string;
  notice?: {
    content: { title: string; message: string };
    actions?: OnboardingAction[];
  };
}

export interface OTPInfo {
  wait_time: "string";
  resend_time: "string";
  status: boolean;
  error_message: "string";
  phone_number: "string";
  otp_request_id: "string";
  is_send_sms: boolean;
}

export interface CurrentOnboardindStateInfo {
  step: number;
  card_cta: string;
  color_card_cta?: string;
  card_title: string;
  card_description: string;
  dialog_content: {
    title: string;
    description: string;
    icon: string;
  };
}

export enum Gender {
  MALE = "Nam",
  FEMALE = "Nữ",
}

export enum AppAction {
  OPEN_EARLY_DISCHARGE_BOTTOM_SHEET = "open_early_discharge_bottom_sheet",
}
