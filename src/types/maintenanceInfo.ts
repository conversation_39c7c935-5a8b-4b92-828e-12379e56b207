import { images } from "@/res";

export enum MaintenanceType {
    PAGE,
    BOTTOM_SHEET,
}

export enum MaintenanceService {
    PERMISSION = 'permission', // all services
    ONBOARDING = 'onboarding',
    PAYMENT = 'payment',
    STATEMENT = 'statement', //repayment
}

export interface MaintenanceInfo {
    type?: MaintenanceType,
    title?: string,
    description?: string,
    images?: string,
    cta_title?: string,
    cta_variant?: string,
}


export const MAINTENANCE_INFO = {
    permission: {
        type: MaintenanceType.PAGE,
        title: 'Tính năng trả góp đang bảo trì',
        description: 'Tính năng trả góp đang được bảo trì nhằm đảm bảo trải nghiệm tốt nhất cho người dùng. Mong bạn thông cảm và quay trở lại sau.',
        cta_title: 'Về trang chủ',
        cta_variant: "outlined",
        images: images.ImgMaintenanceAll
    },
    onboarding: {
        type: MaintenanceType.BOTTOM_SHEET,
        title: 'Tính năng đăng ký trả góp đang bảo trì',
        description: 'Tính năng đăng ký trả góp đang được bảo trì nhằm đảm bảo trải nghiệm tốt nhất cho người dùng. Mong bạn thông cảm và quay trở lại sau.',
        cta_title: 'Đã hiểu',
    },
    payment: {
        type: MaintenanceType.BOTTOM_SHEET,
        title: 'Tính năng thanh toán đang bảo trì',
        description: 'Tính năng thanh toán đang được bảo trì nhằm đảm bảo trải nghiệm tốt nhất cho người dùng. Mong bạn thông cảm và quay trở lại sau.',
        cta_title: 'Đã hiểu',
    },
    statement: {
        type: MaintenanceType.BOTTOM_SHEET,
        title: 'Tính năng thanh toán trả góp đang bảo trì',
        description: 'Tính năng thanh toán trả góp đang được bảo trì nhằm đảm bảo trải nghiệm tốt nhất cho người dùng. Mong bạn thông cảm và quay trở lại sau.',
        cta_title: 'Đã hiểu',
    }
}