import { OnboardingAction } from ".";
import { BindStatus } from "./bindStatus";
import { KycStatus } from "./kycStatus";

export interface PermissionInfo {
  is_whitelisted: boolean;
  bind_status: BindStatus;
  kyc_status: {
    status: KycStatus;
    notice?: {
      content: { title: string; message: string };
      actions?: OnboardingAction[];
    };
  } & { [key: string]: any }; // Allow for additional properties
  risk_info?: {
    code: string;
    level: "LOW" | "MEDIUM" | "HIGH";
    notice?: {
      content: { title: string; message: string };
      actions?: OnboardingAction[];
    };
  } & { [key: string]: any }; // Allow for additional properties
  bind_info?: {
    account_id: string;
    onboarding_id: string;
    partner_code: string;
  };
}
