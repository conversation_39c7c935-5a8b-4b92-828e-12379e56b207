import { FC } from "react";

export type BaseProductPageData = {
    adsList: AdsItem[];
    productTemplate?: ProductPageTemplates;
    extra_info?: ExtraInfoAds;
};

interface AdsItem {
    id: number;
    redirect_url: string;
    banner_url: string;
    title: string;
    description: string;
    icon_url?: string;
    tracking_on_click: boolean | null;
    main_cta_text: string | null;
    icon?: string;
    end_time?: number;
}

export enum Templates {
    LIST21 = "LIST21",
    LIST11 = "LIST11",
    BANNER31 = "BANNER31",
    BANNER41 = "BANNER41",
    SLIDER11 = "SLIDER11",
    SLIDER21 = "SLIDER21"
}
declare enum ProductPageTemplates {
    PRODUCT_PAGE_BOX_IMAGE_SLIDER = "PRODUCT_PAGE_BOX_IMAGE_SLIDER",
    PRODUCT_PAGE_SUGGESTION_BOX = "PRODUCT_PAGE_SUGGESTION_BOX",
    PRODUCT_PAGE_MULTI_IMAGE_SLIDER = "PRODUCT_PAGE_MULTI_IMAGE_SLIDER",
    PRODUCT_PAGE_MULTI_IMAGE_LIST = "PRODUCT_PAGE_MULTI_IMAGE_LIST",
    PRODUCT_PAGE_SINGLE_BANNER = "PRODUCT_PAGE_SINGLE_BANNER",
    PRODUCT_PAGE_FULL_IMAGE_SLIDER = "PRODUCT_PAGE_FULL_IMAGE_SLIDER",
    PRODUCT_PAGE_SINGLE_BANNER_41 = "PRODUCT_PAGE_SINGLE_BANNER_41",
    PRODUCT_PAGE_FULL_IMAGE_SLIDER_41 = "PRODUCT_PAGE_FULL_IMAGE_SLIDER_41"
}
type AdsProductRequest = {
    inventory_id: string;
    extra_infos?: {
        position?: string;
        mapping_card_status?: string;
    };
};
interface ExtraInfoAds {
    title: string;
    action_text?: string;
    action_url?: string;
}

export interface ProductPageSliderProps {
    request: AdsProductRequest;
    template: Templates;
    onAdsBannerClick?: (adsItem: AdsItem, meta?: {
        index: number;
    }, event?: React.MouseEvent<HTMLImageElement>) => void;
    onForceClick?: (adsItem: AdsItem, meta?: {
        index: number;
    }, event?: React.MouseEvent<HTMLImageElement>) => void;
    onMoreForceClick?: (inventoryId: string, event?: React.MouseEvent<HTMLImageElement>) => void;
    onLoaded?: (data: AdsItem[], template?: ProductPageTemplates, extra_info?: ExtraInfoAds) => void;
    onError?: (error: any) => void;
    containerPadding?: number;
    hasSkeleton?: boolean;
    ignoreInitSDK?: boolean;
    paginationClassName?: string;
    demo?: BaseProductPageData & {
        width: string;
    };
    data?: BaseProductPageData;
}
declare const ProductPageSlider: FC<ProductPageSliderProps>;

type SkeletonProps = React.AllHTMLAttributes<HTMLDivElement> & {
    type: 'text' | 'button' | 'card';
    rows?: number;
    theme?: 'dark' | 'light';
};
declare const Skeleton: React.FC<SkeletonProps>;

type ImageProps = {
    theme?: 'dark' | 'light';
    loading?: boolean;
    banner_url?: string;
} & React.HTMLAttributes<HTMLDivElement>;
declare const Image: FC<ImageProps>;
