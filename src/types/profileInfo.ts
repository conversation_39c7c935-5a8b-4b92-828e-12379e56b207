export interface ProfileInfo {
    kyc_level: number;
    profile_level: number;
    full_name: string;
    date_of_birth: string;
    id_issue_date: string;
    id_issue_place: string;
    id_number: string;
    permanent_address: string;
    phone_number: string;
    gender: string;
    email?: string; 
    profile_issues: any[];
    id_type?: IdType;
  }


export enum IdType {
  "CMND" = 1,
  "Passport" = 2,
  "CCCD" = 3,
  "CMSQ" = 4,
  "CCCD_CHIP" = 5,
}
