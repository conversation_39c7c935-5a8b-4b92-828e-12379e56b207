import { HttpRequestVerbs } from '../types';
import createZlpRequest from './core/createZlpRequest';
import { ContentType } from './core/requestBuilder';
import { withBaseUrl } from './core/withBaseUrl';

const PATH = '/onboarding/v1/users/ekyc-nfc/reset';

export const postResetNFC = () =>
  createZlpRequest<any>(withBaseUrl(PATH), HttpRequestVerbs.POST)
    .setHeaders({ 'Content-Type': ContentType.JSON })
    .build();
