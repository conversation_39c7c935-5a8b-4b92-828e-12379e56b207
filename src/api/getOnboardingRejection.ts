import { HttpRequestVerbs } from "../types";
import createZlpRequest from "./core/createZlpRequest";
import { withBaseUrl } from "./core/withBaseUrl";

const PATH = "/onboarding/v1/rejection";

export interface RejectionAction {
  code: string;
  title: string;
  variant: string;
  zpa_action_url?: string;
  zpi_action_url?: string;
  metadata: any;
}

export interface RejectionContent {
  title: string;
  message: string;
}

export interface IRejection {
  code: string;
  content: RejectionContent;
  actions: RejectionAction[];
}

export const getOnboardingRejection = (onboarding_id: string) =>
  createZlpRequest<IRejection>(withBaseUrl(PATH), HttpRequestVerbs.GET).setQueryParams({ onboarding_id }).build();
