import { HttpRequestVerbs } from '../types';
import createZlpRequest from './core/createZlpRequest';
import { ContentType } from './core/requestBuilder';
import { withBaseUrl } from './core/withBaseUrl';

const PATH = '/onboarding/v1/verify-otp';

export interface OTPVerifyForm {
  otp_code: string;
  otp_type: string;
  onboarding_id: string;
  partner_code: string;
}

export const postVerifyOTP = (payload: OTPVerifyForm) =>
  createZlpRequest<any>(withBaseUrl(PATH), HttpRequestVerbs.POST)
    .setHeaders({ 'Content-Type': ContentType.JSON })
    .setDataBody(JSON.stringify(payload))
    .build();
