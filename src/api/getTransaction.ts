import { HttpRequestVerbs, ResourceTypes } from "../types";
import createZlpRequest from "./core/createZlpRequest";
import { withBaseUrl } from "./core/withBaseUrl";

const PATH_LIST = "/payment/v1/transactions";
const PATH_SINGLE = "/payment/v1/transaction";

export interface TransactionRequest {
  account_id: string;
  from_date?: string;
  to_date: string;
  trans_types?: string;
  ["pagination.limit"]?: number;
  ["pagination.offset"]?: number;
  ["pagination.cursor"]?: string;
  ["pagination.direction"]?: string;
  //   pagination: {
  //     limit: number;
  //     offset?: number;
  //     cursor?: string;
  //     direction?: string;
  //   };
}

export interface TransactionList {
  transactions: TransactionItem[];
  pagination: {
    prev_cursor: string;
    next_cursor: string;
    has_prev: boolean;
    has_next: boolean;
  };
}

export interface TransactionItem {
  zpTransId: string;
  type: TransactionType;
  amount: string;
  status: TransactionStatus;
  remark: string;
  productIcon: string;
  createdAt: string;
  updatedAt: string;
  partnerTransId?: string;
}

export enum TransactionStatus {
  TRANS_STATUS_UNSPECIFIED = "TRANS_STATUS_UNSPECIFIED",
  TRANS_STATUS_SUCCESS = "TRANS_STATUS_SUCCESS",
  TRANS_STATUS_FAILED = "TRANS_STATUS_FAILED",
  TRANS_STATUS_PENDING = "TRANS_STATUS_PENDING",
  TRANS_STATUS_PROCESSING = "TRANS_STATUS_PROCESSING",
}

export enum TransactionType {
  TRANS_TYPE_UNSPECIFIED = "TRANS_TYPE_UNSPECIFIED",
  TRANS_TYPE_PAYMENT = "TRANS_TYPE_PAYMENT",
  TRANS_TYPE_REPAYMENT = "TRANS_TYPE_REPAYMENT",
}

export const getTransactionList = (params: TransactionRequest) =>
  createZlpRequest<TransactionList>(withBaseUrl(PATH_LIST), HttpRequestVerbs.GET)
    .setQueryParams({ ...params })
    .build();

export const getTransaction = (zpTransId: string) =>
  createZlpRequest<{ transaction: TransactionItem }>(withBaseUrl(PATH_SINGLE), HttpRequestVerbs.GET)
    .setQueryParams({ zp_trans_id: zpTransId })
    .build();
