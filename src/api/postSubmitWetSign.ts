import { HttpRequestVerbs, ResourceTypes } from '../types';
import createZlpRequest from './core/createZlpRequest';
import { ContentType } from './core/requestBuilder';
import { withBaseUrl } from './core/withBaseUrl';

const PATH = '/onboarding/v1/wet-sign';

export interface WetSignForm {
  onboarding_id: string;
  sign_image_data: {
    image_data: string;
    image_type: string;
  };
}

export const postSubmitWetSign = (payload: WetSignForm) =>
  createZlpRequest<any>(withBaseUrl(PATH), HttpRequestVerbs.POST)
    .setHeaders({ 'Content-Type': ContentType.JSON })
    .setDataBody(JSON.stringify(payload))
    .build();
