import { HttpRequestVerbs, ResourceTypes } from '../types';
import createZlpRequest from './core/createZlpRequest';
import { withBaseUrl } from './core/withBaseUrl';

const PATH = '/onboarding/v1/resources';

const defaulParams = { resource_types: ['OCCUPATION', 'JOB_TITLE',  'CITY', 'INCOME', 'SOURCE_OF_FUND', 'EDUCATION', 'EMPLOYMENT_STATUS', 'FUND_PURPOSE']};
export const getResource = (params: { resource_types: string[] } = defaulParams) =>
  createZlpRequest<{ resources: ResourceTypes[]}>(withBaseUrl(PATH), HttpRequestVerbs.GET).setQueryParams(params).build();
