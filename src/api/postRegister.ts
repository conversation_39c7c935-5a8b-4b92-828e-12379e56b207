import { HttpRequestVerbs } from "../types";
import createZlpRequest from "./core/createZlpRequest";
import { ContentType } from "./core/requestBuilder";
import { withBaseUrl } from "./core/withBaseUrl";

const PATH = '/onboarding/v1/register';

export const postRegister = (payload: { partner_code: string }) =>
  createZlpRequest<any>(withBaseUrl(PATH), HttpRequestVerbs.POST)
    .setHeaders({ 'Content-Type': `${ContentType.JSON}` })
    .setDataBody(JSON.stringify(payload))
    .build();
