import { HttpRequestVerbs, ResourceTypes } from '../types';
import createZlpRequest from './core/createZlpRequest';
import { ContentType } from './core/requestBuilder';
import { withBaseUrl } from './core/withBaseUrl';

const PATH = '/onboarding/v1/contract';

export interface ContractRequest {
  onboarding_id: string;
}

export interface ContractResponse {
  unsigned_contract_url: string;
  signed_contract_url: string;
}

export const getContract = (payload: ContractRequest) =>
  createZlpRequest<any>(withBaseUrl(PATH), HttpRequestVerbs.GET)
    .setHeaders({ 'Content-Type': ContentType.JSON })
    .setQueryParams(payload)
    .build();
