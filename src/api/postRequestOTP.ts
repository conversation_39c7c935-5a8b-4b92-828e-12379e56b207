import { HttpRequestVerbs, ResourceTypes } from '../types';
import createZlpRequest from './core/createZlpRequest';
import { ContentType } from './core/requestBuilder';
import { withBaseUrl } from './core/withBaseUrl';

const PATH = '/onboarding/v1/request-otp';

export interface OTPRequestForm {
  otp_type: string;
  onboarding_id: string;
  partner_code: string;
}

export const postRequestOTP = (payload: OTPRequestForm) =>
  createZlpRequest<any>(withBaseUrl(PATH), HttpRequestVerbs.POST)
    .setHeaders({ 'Content-Type': ContentType.JSON })
    .setDataBody(JSON.stringify(payload))
    .build();
