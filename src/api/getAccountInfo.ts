import { HttpRequestVerbs } from "../types";
import createZlpRequest from "./core/createZlpRequest";
import { withBaseUrl } from "./core/withBaseUrl";

const PATH = "/account/v1/account";

export type AccountInfoRequest = {
  account_id?: string;
  partner_code?: string;
};

export type AccountInfo = {
  account_id: string;
  partner_code: string;
  partner_account_name: string;
  partner_account_number: string;
  installment_limit: string;
  installment_balance: string;
  status: string;
  source: string;
  installment_term: {
    fee_explanation: string;
    stmt_due_date_text: string;
    stmt_incur_date_text: string;
  };
  created_at: string;
  repayment_balance?: string;
};

export const getAccountInfo = ({ account_id, partner_code }: AccountInfoRequest) =>
  createZlpRequest<{ account: AccountInfo }>(withBaseUrl(PATH), HttpRequestVerbs.GET)
    .setQueryParams({ account_id, partner_code })
    .build();
