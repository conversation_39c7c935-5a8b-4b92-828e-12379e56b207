import { HttpRequestVerbs } from "../types";
import createZlpRequest from "./core/createZlpRequest";
import { ContentType } from "./core/requestBuilder";
import { withBaseUrl } from "./core/withBaseUrl";

const PATH = "/payment/v1/repay/create-order";

export interface OrderRequestForm {
  amount: string;
  partner_code: string;
  statement_id: string;
  statement_date: string;
  extra?: {
    additionalProp1: string;
    additionalProp2: string;
    additionalProp3: string;
  };
}

export interface Order {
  app_trans_id: string;
  app_id: number;
  zp_trans_token: string;
  trans_id: string;
  order_no: string;
}

export const postCreateOrder = (payload: OrderRequestForm) =>
  createZlpRequest<Order>(withBaseUrl(PATH), HttpRequestVerbs.POST)
    .setHeaders({ "Content-Type": ContentType.JSON })
    .setDataBody(JSON.stringify(payload))
    .build();
