import { StatementList } from "@/types/statement";
import { HttpRequestVerbs } from "../types";
import createZlpRequest from "./core/createZlpRequest";
import { withBaseUrl } from "./core/withBaseUrl";

const PATH = "/statement/v1/list";

export interface StatementListPayload {
  account_id: string;
  from_time: string;
  to_time: string;
}

export const getStatementList = ({ account_id, from_time, to_time }: StatementListPayload) =>
  createZlpRequest<StatementList>(withBaseUrl(PATH), HttpRequestVerbs.GET)
    .setQueryParams({ account_id, from_time, to_time })
    .build();
