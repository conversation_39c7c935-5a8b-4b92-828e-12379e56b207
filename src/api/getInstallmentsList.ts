import { HttpRequestVerbs } from "../types";
import createZlpRequest from "./core/createZlpRequest";
import { withBaseUrl } from "./core/withBaseUrl";

const PATH = "/statement/v1/installments";

export interface Installment {
  id: string;
  statement_id: string;
  transaction_desc: string;
  outstanding_amount: string;
  zp_trans_id: string,
  installment_id: string,
  outstanding_details: {
    total_due_amount: string,
    total_overdue_amount: string,
    total_penalty_amount: string
  }
}

export const getInstallmentList = (account_id: string, statement_id: string) =>
  createZlpRequest<{ installments: Installment[] }>(withBaseUrl(PATH), HttpRequestVerbs.GET)
    .setQueryParams({ account_id, statement_id })
    .build();
