import { Statement } from "@/types/statement";
import { HttpRequestVerbs } from "../types";
import createZlpRequest from "./core/createZlpRequest";
import { withBaseUrl } from "./core/withBaseUrl";

const PATH = "/statement/v1/latest";

export const getLastestStatement = (account_id: string) =>
  createZlpRequest<{ statement: Statement }>(withBaseUrl(PATH), HttpRequestVerbs.GET)
    .setQueryParams({ account_id })
    .build();
