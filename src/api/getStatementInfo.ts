import { Statement } from "@/types/statement";
import { HttpRequestVerbs } from "../types";
import createZlpRequest from "./core/createZlpRequest";
import { withBaseUrl } from "./core/withBaseUrl";

const PATH = "/statement/v1/info";

export const getStatementInfo = ({ statementId, accountId }: { statementId: string, accountId: string }) =>
  createZlpRequest<{ statement: Statement }>(withBaseUrl(PATH), HttpRequestVerbs.GET)
    .setQueryParams({ statement_id: statementId, account_id: accountId })
    .build();
