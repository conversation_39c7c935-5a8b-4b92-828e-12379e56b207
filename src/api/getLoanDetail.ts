import { HttpRequestVerbs } from "../types";
import createZlpRequest from "./core/createZlpRequest";
import { withBaseUrl } from "./core/withBaseUrl";
import { EarlyDischargeInfo } from "./getEarlyDischarge";

const PATH = "/installment/v1/info";

export enum InstallmentInfoStatus {
  INSTALLMENT_STATUS_UNSPECIFIED = "INSTALLMENT_STATUS_UNSPECIFIED",
  INSTALLMENT_STATUS_INIT = "INSTALLMENT_STATUS_INIT",
  INSTALLMENT_STATUS_OPEN = "INSTALLMENT_STATUS_OPEN",
  INSTALLMENT_STATUS_CLOSED = "INSTALLMENT_STATUS_CLOSED",
}

export interface InstallmentInfo {
  id: string;
  tenure: number;
  status: InstallmentInfoStatus;
  principal_amount: string;
  interest_amount: string;
  penalty_amount: string;
  total_amount_due: string;
  total_paid_amount: string;
  total_remaining_amount: string;
}

export enum RepaymentScheduleStatus {
  REPAY_STATUS_UNSPECIFIED = "REPAY_STATUS_UNSPECIFIED",
  REPAY_STATUS_PENDING = "REPAY_STATUS_PENDING",
  REPAY_STATUS_DUE = "REPAY_STATUS_DUE",
  REPAY_STATUS_PAID = "REPAY_STATUS_PAID",
  REPAY_STATUS_OVERDUE = "REPAY_STATUS_OVERDUE",
}

export interface RepaymentSchedule {
  seq_no: number;
  status: RepaymentScheduleStatus;
  due_date: string;
  due_amount: string;
  penalty_amount: string;
  total_due_amount: string;
  total_paid_amount: string;
  total_remaining_amount: string;
}

export interface LoanDetail {
  installment: InstallmentInfo;
  early_discharge: EarlyDischargeInfo;
  repayment_schedules: RepaymentSchedule[];
}

export const getLoanDetail = (zpTransId: string) =>
  createZlpRequest<LoanDetail>(withBaseUrl(PATH), HttpRequestVerbs.GET)
    .setQueryParams({ zp_trans_id: zpTransId })
    .build();
