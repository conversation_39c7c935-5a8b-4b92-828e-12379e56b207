import { HttpRequestVerbs, ResourceTypes } from "../types";
import createZlpRequest from "./core/createZlpRequest";
import { withBaseUrl } from "./core/withBaseUrl";

const PATH = "/account/v1/content/proposed-services";

export interface ProposedServices {
  services: Service[];
}

export interface Service {
  code: string;
  name: string;
  content_image: ContentImage;
  interaction: Interaction;
}

export interface ContentImage {
  icon_url: string;
  cover_url: string;
  thumbnail_url: string;
}

export interface Interaction {
  zpa_url: string;
  zpi_url: string;
}

export const getproposedServices = () =>
  createZlpRequest<any>(withBaseUrl(PATH), HttpRequestVerbs.GET).setQueryParams({ partnerCode: "CIMB" }).build();
