import { HttpRequestVerbs } from "../types";
import createZlpRequest from "./core/createZlpRequest";
import { withBaseUrl } from "./core/withBaseUrl";

const PATH = "/installment/v1/early-discharge";

export enum EarlyDischargeInfoStatus {
  EARLY_DISCHARGE_STATUS_UNSPECIFIED = "EARLY_DISCHARGE_STATUS_UNSPECIFIED",
  EARLY_DISCHARGE_STATUS_ELIGIBLE = "EARLY_DISCHARGE_STATUS_ELIGIBLE",
  EARLY_DISCHARGE_STATUS_INELIGIBLE = "EARLY_DISCHARGE_STATUS_INELIGIBLE",
  EARLY_DISCHARGE_STATUS_PROCESSING = "EARLY_DISCHARGE_STATUS_PROCESSING",
  EARLY_DISCHARGE_STATUS_CLOSED = "EARLY_DISCHARGE_STATUS_CLOSED",
}

export enum EarlyDischargeInfoKind {
  EARLY_DISCHARGE_KIND_UNSPECIFIED = "EARLY_DISCHARGE_KIND_UNSPECIFIED",
  EARLY_DISCHARGE_KIND_NORMAL = "EARLY_DISCHARGE_KIND_NORMAL",
  EARLY_DISCHARGE_KIND_REFUND = "EARLY_DISCHARGE_KIND_REFUND",
}

export interface EarlyDischargeInfo {
  info: {
    kind: EarlyDischargeInfoKind;
    status: EarlyDischargeInfoStatus;
    allowed: boolean;
  };
  detail?: {
    total_discharge_amount: string;
    early_discharge_amount: string;
    early_discharge_fee: string;
    total_outstanding_amount: string;
    outstanding_principal_amount: string;
    outstanding_interest_amount: string;
    outstanding_penalty_amount: string;
  };
}

export const getEarlyDischarge = ({ zpTransId }: { zpTransId: string }) =>
  createZlpRequest<{ early_discharge: EarlyDischargeInfo }>(withBaseUrl(PATH), HttpRequestVerbs.GET)
    .setQueryParams({ zpTransId })
    .build();
