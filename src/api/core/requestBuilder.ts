/* eslint-disable no-undef */
import qs from 'query-string';
import { HttpRequestVerbs } from '../../types';

//#region
export type SuccessfulResponse<T> = {
  code: number;
  message: string;
  data: T;
};

export enum ContentType {
  JSON = 'application/json',
  FORM_DATA = 'multipart/form-data',
}
type RequestInterceptor = (context: RequestInit) => RequestInit | Promise<RequestInit>;
type ResponseInterceptor = ({
  response,
  RequestBuilder,
}: {
  response: Response;
  RequestBuilder: RequestBuilder<ResponseType>;
}) => Promise<{ response: Response; RequestBuilder: RequestBuilder<ResponseType> }> | Promise<Response>;

class RequestBuilder<ResponseType> {
  private requestInterceptors?: RequestInterceptor[];
  private responseInterceptors?: ResponseInterceptor[];
  public url: string;
  private readonly requestMethod: HttpRequestVerbs;
  private dataBody?: any | FormData;
  public queryParams?: any;
  private headers?: { [key: string]: string };
  private credentials?: RequestCredentials = 'include';
  private parseJson: boolean = true;

  constructor(url: string, requestMethod: HttpRequestVerbs) {
    this.url = url;
    this.requestMethod = requestMethod;
  }

  addRequestInterceptors(interceptors: RequestInterceptor[]) {
    if (!this.requestInterceptors) {
      this.requestInterceptors = [];
    }
    this.requestInterceptors = this.requestInterceptors.concat(interceptors);
    return this;
  }

  addResponseInterceptors(interceptors: ResponseInterceptor[]) {
    if (!this.responseInterceptors) {
      this.responseInterceptors = [];
    }
    this.responseInterceptors = this.responseInterceptors?.concat(interceptors);
    return this;
  }

  setDataBody(data: any) {
    this.dataBody = data;
    return this;
  }

  setQueryParams(params: any) {
    this.queryParams = params;
    return this;
  }

  setCredentials(credentials: RequestCredentials) {
    this.credentials = credentials;
    return this;
  }

  setHeaders(headers: { [key: string]: string }) {
    this.headers = headers;
    return this;
  }

  setParseJson(parseJson: boolean) {
    this.parseJson = parseJson;
    return this;
  }

  async build(): Promise<ResponseType> {
    const responseInterceptorChain = flow(this.responseInterceptors || []);
    const fetchResponse = await this.onFetch();
    const { response } = await responseInterceptorChain({ response: fetchResponse, RequestBuilder: this });
    if (response.ok) {
      if (this.parseJson) {
        return response.json() as ResponseType;
      } else {
        return response;
      }
    } else {
      if (this.parseJson) {
        return Promise.reject({ ...(await response.json()), http_code: response.status });
      } else {
        return Promise.reject(response);
      }
    }
  }

  async onFetch() {
    if (this.queryParams) {
      this.url = qs.stringifyUrl({ url: this.url, query: this.queryParams }, { arrayFormat: 'bracket' });
    }

    const requestInterceptorChain = flow(this.requestInterceptors || []);
    const requestMethod = this.requestMethod;
    let headers = { ...this.headers };
    const options = await requestInterceptorChain({
      method: requestMethod,
      headers,
      credentials: this.credentials,
      mode: 'cors',
      body: this.dataBody,
    });
    return fetch(this.url, options);
  }
}

/**
 * Feed `value` to the `fns[0]`, get the result and then feed to `fns[1]`,... until all functions are executed.
 */
const flow = (fns: any[]) => {
  return async (value: any) => {
    let result = value;
    for (let i = 0; i < fns.length; i++) {
      result = await fns[i](result);
    }
    return result;
  };
};

export default RequestBuilder;
