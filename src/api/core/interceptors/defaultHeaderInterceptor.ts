// /* eslint-disable no-undef */
import getDeviceInfo from '@/utils/getDeviceInfo';
import zpiPackageJson from '../../../../package.json';
const {  deviceOS } = getDeviceInfo();

export const defaultHeaderInterceptor = async (request: RequestInit) => {
  return {
    ...request,
    headers: {
      ...request.headers,
      mini_app_version: zpiPackageJson?.version,
      device_os: deviceOS,
    },
  };
};
