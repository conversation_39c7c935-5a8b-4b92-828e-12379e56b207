import { HttpRequestVerbs } from '../../types';
import {
  defaultHeaderInterceptor,
  maintenanceInterceptor,
  zlpAuthorizeInterceptor,
  zlpResponseUnwrapInterceptor,
} from './interceptors';
import RequestBuilder from './requestBuilder';

export type SuppressError = {
  ignoreErrorCodes?: number[];
};

const createZlpRequest = <ResponseType>(getUrl: () => string, method: HttpRequestVerbs) => {
  return new RequestBuilder<ResponseType>(getUrl(), method)
    .addRequestInterceptors([defaultHeaderInterceptor, zlpAuthorizeInterceptor])
    .addResponseInterceptors([maintenanceInterceptor, zlpResponseUnwrapInterceptor]);
};

export default createZlpRequest;
