import { Environment, environment } from "../../shared/environment";

/**
 * To enforce getting the latest environment.
 * TODO: Refactor to remove this function in function pattern.
 */
export const withBaseUrl = (path: string) => () => `${buildBaseUrl()}${path}`;

export const getSurveyWrapperResourceUrlWithEnv = (env: Environment = environment) => {
  switch (env) {
    case Environment.STAGING:
      return "https://stg-support.zalopay.vn";
    case Environment.QC_SANDBOX:
      return "https://qc-support.zalopay.vn";
    default:
    case Environment.PRODUCTION:
      return "https://support.zalopay.vn";
  }
};

export const getSupportedServiceConfigUrlWithEnv = (env: Environment = environment) => {
  switch (env) {
    case Environment.STAGING:
      return "https://simg.zalopay.com.vn/fs/Installment/data/supported_service_merchant.staging.json";
    case Environment.QC_SANDBOX:
      return "https://simg.zalopay.com.vn/fs/Installment/data/supported_service_merchant.sandbox.json";
    default:
    case Environment.PRODUCTION:
      return "https://simg.zalopay.com.vn/fs/Installment/data/supported_service_merchant.prod.json";
  }
};

export const getSurveyIdWithEnv = (env: Environment = environment) => {
  switch (env) {
    case Environment.STAGING:
      return "paylater-feedback-stg";
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
    case Environment.MC_SANDBOX:
      return "paylater-feedback-qc";
    default:
    case Environment.PRODUCTION:
      return "paylater-feedback";
  }
};

export const getAdResourceUrlWithEnv = (env: Environment = environment) => {
  let domain = "";
  switch (env) {
    case Environment.STAGING:
      domain = "https://stguudai.zalopay.vn/advertising/gateway";
      break;
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
    case Environment.MC_SANDBOX:
      domain = "https://qcuudai.zalopay.vn/advertising/gateway";
      break;
    case Environment.PRODUCTION:
      domain = "https://uudai.zalopay.vn/advertising/gateway";
      break;
    default:
      domain = "https://uudai.zalopay.vn/advertising/gateway";
  }

  return domain;
};

//#region
export const buildZlpPublicBaseUrl = (env: Environment = environment) => {
  let domain = "";
  switch (env) {
    case Environment.STAGING:
      domain = "https://socialstg.zalopay.vn";
      break;
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
    case Environment.MC_SANDBOX:
      domain = "https://socialdev.zalopay.vn";
      break;
    case Environment.PRODUCTION:
      domain = "https://sapi.zalopay.vn";
      break;
    default:
      domain = "https://sapi.zalopay.vn";
  }

  return domain;
};
//#endregion

//#region
export const buildBaseUrl = (env: Environment = environment) => {
  let domain = "";
  switch (env) {
    case Environment.STAGING:
      domain = "https://stgfin-installment.zalopay.vn";
      break;
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
    case Environment.MC_SANDBOX:
      domain = "https://devfin-installment.zalopay.vn";
      break;
    case Environment.PRODUCTION:
    default:
      domain = "https://fin-installment.zalopay.vn";
  }

  return `${domain}`;
};
//#endregion

//#region
export const buildZpiUrl = (env: Environment = environment) => {
  let domain = "";
  switch (env) {
    case Environment.STAGING:
      domain = "https://socialstg.zalopay.vn";
      break;
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
      domain = "https://socialdev.zalopay.vn";
      break;
    case Environment.PRODUCTION:
    default:
      domain = "https://social.zalopay.vn";
  }
  return domain;
};
//#endregion

//#region
export const buildSupportUrl = (env: Environment = environment): string => {
  let domain = "";
  switch (env) {
    case Environment.STAGING:
      domain = "https://stg-support.zalopay.vn";
      break;
    case Environment.DEV_SANDBOX:
    case Environment.QC_SANDBOX:
    case Environment.MC_SANDBOX:
      domain = "https://qc-support.zalopay.vn";
      break;
    case Environment.PRODUCTION:
    default:
      domain = "https://support.zalopay.vn";
  }

  return domain;
};
//#endregion

export const withSupportUrl = (path: string) => () => `${buildZpiUrl()}${path}`;
