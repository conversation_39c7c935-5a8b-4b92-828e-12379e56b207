import { HttpRequestVerbs } from "../types";
import createZlpRequest from "./core/createZlpRequest";
import { ContentType } from "./core/requestBuilder";
import { withBaseUrl } from "./core/withBaseUrl";

const PATH = "/payment/v1/refund/topups";

export interface TopupOrderRequestForm {
  amount: string;
  zp_trans_id: string;
}

export interface TopupOrder {
  trans_id: string;
  app_id: number;
  app_trans_id: string;
  zp_trans_token: string;
}

export const postCreateTopupOrder = (payload: TopupOrderRequestForm) =>
  createZlpRequest<TopupOrder>(withBaseUrl(PATH), HttpRequestVerbs.POST)
    .setHeaders({ "Content-Type": ContentType.JSON })
    .setDataBody(JSON.stringify(payload))
    .build();
