import { Environment, environment } from "@/shared/environment";

export const getFinSDKURL = (env: Environment = environment) => {
    let domain = '';
    switch (env) {
      case Environment.STAGING:
        domain = 'https://socialstg.zalopay.vn/mfpublic/share-module/micro-app/2e6e082f-7ec5-4315-9f17-6a05916ba1e5';
        break;
      case Environment.DEV_SANDBOX:
      case Environment.QC_SANDBOX:
      case Environment.MC_SANDBOX:
        domain = "https://sjs.zalopay.com.vn/zst/spa/v2/micro-apps/fin_sdk/1.0.3/remoteEntry.js"
        // domain = ' https://simg.zalopay.com.vn/fs/fin-sdk/1.0.3/remoteEntry.js';

        //domain = 'http://localhost:3001/remoteEntry.js';
        // domain = 'https://socialdev.zalopay.vn/mfpublic/share-module/micro-app/bd211552-0808-4858-b5e1-fee2f9c677f7';
        break;
      case Environment.PRODUCTION:
        domain = 'https://sapi.zalopay.vn/mfpublic/share-module/micro-app/b199cccd-3285-4d15-8641-16cd46aaa4a2';
        break;
      default:
        domain = 'https://sapi.zalopay.vn/mfpublic/share-module/micro-app/b199cccd-3285-4d15-8641-16cd46aaa4a2';
    }
  
    return domain;
  };