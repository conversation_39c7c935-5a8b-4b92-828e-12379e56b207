import { HttpRequestVerbs } from "../types";
import createZlpRequest from "./core/createZlpRequest";
import { withSupportUrl } from "./core/withBaseUrl";

// adjust the PATH to request FAQ questions
const PATH = "/faq/api/get-solutions-list";
const DIRECTION_PATH = "/faq/web/faq-entry";

export type FAQQuestion = {
  id: number;
  status: number;
  title: string;
  description: string;
  tags: string[];
  group: [];
  category_id: number;
  folder_id: number;
  created_at: string;
  updated_at: string;
  description_text: string;
  support_type: number;
  form_url: string;
  is_faq_trans: boolean;
  schedules: [];
  current_time: number;
};

export const buildQuestionDirectionUrl = (questionId: string, params?: string) => {
  return `${DIRECTION_PATH}/${questionId}${params ? `?${params}` : ""}`;
};

export const getFAQQuestions = ({ tags }: { tags: string }) =>
  createZlpRequest<{ data: FAQQuestion[] }>(withSupportUrl(PATH), HttpRequestVerbs.GET)
    .setQueryParams({ tags })
    .build();
