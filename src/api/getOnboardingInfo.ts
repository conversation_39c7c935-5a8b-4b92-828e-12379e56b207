import { HttpRequestVerbs } from "../types";
import { IdType } from "../types/profileInfo";
import createZlpRequest from "./core/createZlpRequest";
import { withBaseUrl } from "./core/withBaseUrl";

const PATH = "/onboarding/v1/onboardings";

export type OnboardingInfoRequest = {
  onboarding_id?: string;
};

export type OnboardingInfo = {
  status: string;
  partner_code: string;
  current_step: string;
  full_name: string;
  gender: string;
  phone_number: string;
  id_number: string;
  id_issue_date: string;
  id_issue_place: string;
  date_of_birth: string;
  permanent_address: string;
  temp_residence_address: string;
  id_type?: IdType;
};

export const getOnboardingInfo = ({ onboarding_id }: OnboardingInfoRequest) =>
  createZlpRequest<OnboardingInfo>(withBaseUrl(`${PATH}/${onboarding_id}`), HttpRequestVerbs.GET).build();
