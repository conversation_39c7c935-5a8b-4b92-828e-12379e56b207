import { HttpRequestVerbs, ResourceTypes } from '../types';
import createZlpRequest from './core/createZlpRequest';
import { ContentType } from './core/requestBuilder';
import { withBaseUrl } from './core/withBaseUrl';

const PATH = '/onboarding/v1/application';

export interface ApplicationForm {
  temp_residence_address: string;
  occupation: string;
  job_title: string;
  monthly_income: string;
  living_city: string;
  source_of_fund: string;
  education: string;
  employment_status: string;
  fund_purpose: string;
  onboarding_id: string;
}
export const postSubmitApplication = (payload: ApplicationForm) =>
  createZlpRequest<{ resources: ResourceTypes[] }>(withBaseUrl(PATH), HttpRequestVerbs.POST)
    .setHeaders({ 'Content-Type': ContentType.JSON })
    .setDataBody(JSON.stringify(payload))
    .build();
