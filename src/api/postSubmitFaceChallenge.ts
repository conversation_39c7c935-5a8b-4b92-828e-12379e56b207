import { HttpRequestVerbs, ResourceTypes } from '../types';
import createZlpRequest from './core/createZlpRequest';
import { ContentType } from './core/requestBuilder';
import { withBaseUrl } from './core/withBaseUrl';

const PATH = '/onboarding/v1/face-challenge';

export interface FaceChallengeForm {
  onboarding_id: string;
  face_request_id: string;
}

export const postSubmitFaceChallenge = (payload: FaceChallengeForm) =>
  createZlpRequest<any>(withBaseUrl(PATH), HttpRequestVerbs.POST)
    .setHeaders({ 'Content-Type': ContentType.JSON })
    .setDataBody(JSON.stringify(payload))
    .build();
