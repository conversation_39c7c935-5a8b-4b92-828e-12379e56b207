const { createProxyMiddleware } = require("http-proxy-middleware");

module.exports = function (app) {
    app.use(
        createProxyMiddleware(['/customer/form/api', '/customer/surveys'], {
            target: 'https://qc-support.zalopay.vn/',
            changeOrigin: true,
            cookieDomainRewrite: 'localhost',
        }),
        createProxyMiddleware('/v2/user/profile', {
            target: 'https://socialdev.zalopay.vn/',
            changeOrigin: true,
            cookieDomainRewrite: 'localhost',
        }),
        createProxyMiddleware(['/advertising/gateway'], {
            target: 'https://qcuudai.zalopay.vn/',
            changeOrigin: true,
            cookieDomainRewrite: 'localhost',
        }),
        createProxyMiddleware(['/onboarding/v1'], {
            target: 'https://devfin-installment.zalopay.vn/',
            changeOrigin: true,
            cookieDomainRewrite: 'localhost',
        })
    );
};
