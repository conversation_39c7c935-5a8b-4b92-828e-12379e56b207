import { SVGProps } from 'react';
import { colors } from '../../constants/colors';

export const ProcessingIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 16}
      height={props?.height ? props?.height : 16}
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <circle
        fill={props?.color ? props?.color : "#FF8D00"}
        cx="8.125"
        cy="8.5"
        r="6.75"
        stroke="white"
        strokeWidth="0.5"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.14878 3.93302V4.76626L8.14936 4.76683C9.13745 4.76653 10.0852 5.15891 10.7838 5.85759C11.4825 6.55627 11.8749 7.50398 11.8746 8.49207C11.8756 8.96267 11.7855 9.42901 11.6092 9.86535C11.4829 10.1771 11.0831 10.2611 10.8455 10.0235C10.7155 9.89859 10.6733 9.70785 10.7383 9.5397C10.8754 9.20753 10.9451 8.85142 10.9434 8.49207C10.9425 6.94912 9.69174 5.69864 8.14878 5.698V6.53066C8.14878 6.74042 7.89731 6.84298 7.74839 6.6987L6.44928 5.39901C6.35657 5.3063 6.35657 5.16201 6.44928 5.06872L7.75302 3.76961C7.81882 3.70211 7.91906 3.68144 8.00619 3.71741C8.09332 3.75339 8.14978 3.83876 8.14878 3.93302ZM5.35598 8.49088C5.35726 10.0332 6.6072 11.2831 8.14947 11.2844V10.4511C8.14888 10.3571 8.20484 10.272 8.29136 10.2353C8.37789 10.1985 8.47801 10.2174 8.54523 10.2831L9.84434 11.5828C9.93705 11.6755 9.93705 11.8204 9.84434 11.9131L8.54523 13.2116C8.40095 13.3611 8.14947 13.2585 8.14947 13.0494V12.2161C7.16138 12.2163 6.21374 11.8237 5.51516 11.1249C4.81659 10.4262 4.42435 9.47839 4.42481 8.4903C4.42379 8.0197 4.51391 7.55335 4.6902 7.11702C4.74806 6.97111 4.87543 6.86409 5.02912 6.83223C5.18282 6.80037 5.34223 6.84795 5.45332 6.95883C5.58353 7.08388 5.62581 7.27494 5.56052 7.44325C5.42551 7.76426 5.35598 8.12293 5.35598 8.49088Z"
        fill="#FDFDFD"
      />
    </svg>
  );
};

