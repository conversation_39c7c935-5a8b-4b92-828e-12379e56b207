import { SVGProps } from 'react';
import { colors } from '../../constants/colors';

export const GeneralCheckLineIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 24}
      height={props?.height ? props?.height : 24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        d="M21.9505 5.52479C22.5363 6.11058 22.5363 7.06032 21.9505 7.64611L10.5925 19.0041C10.3111 19.2855 9.92962 19.4435 9.53179 19.4435C9.13397 19.4435 8.75244 19.2855 8.47113 19.0041L2.56866 13.1017C1.98288 12.5159 2.00632 11.5427 2.59211 10.9569C3.17789 10.3711 4.10419 10.3946 4.68997 10.9803L9.53179 15.8222L19.8292 5.52479C20.415 4.939 21.3647 4.939 21.9505 5.52479Z"
        fill={props?.color ? props?.color : colors.blue[500]}
      />
    </svg>
  );
};

