import { SVGProps } from "react";

/**
 * @component @name WalletIcon
 *
 * @preview ![img](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAMCAYAAABSgIzaAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADVSURBVHgBnZHRDcIwDEQTxD8dIRtQJqDdACaATWADRqiYoDBBxQRkg7YTUCYId8KWQqT0A0tPrew724qN+TOs/oQQKnxKsJaUAxMYwAhu1trhxwjTDp8GXIGXGkWFNGDDLahjM409KM1MoH4GTZwowCsjduBCA2ipA0fWFrLOlBnUgTd4gLto2cixawmemWl9kmN0fMilTCtSIx8BAtIlJQd81ijmWs6kBrJBftIVOP5gZgL1U/yqekeeojXf25FR6ivZRk+11zvapGsVraRBoYfBx9oPakOHqv7Vw88AAAAASUVORK5CYII=)
 *
 * @param {Object} props - size, with, height
 * @returns {JSX.Element} JSX Element
 *
 */
export const WalletIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 20}
      height={props?.height ? props?.height : 20}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_2844_50430)">
        <path
          d="M19.6064 5.72451C20.1312 8.53906 20.1312 11.4013 19.6064 14.2636C19.1294 16.9827 16.9827 19.1294 14.2636 19.6064C11.449 20.1312 8.58676 20.1312 5.72451 19.6064C3.00537 19.1294 0.90638 17.0304 0.429338 14.3113C-0.143113 11.4967 -0.143113 8.58676 0.429338 5.72451C0.90638 3.00537 3.00537 0.90638 5.72451 0.429338C8.53906 -0.143113 11.449 -0.143113 14.3113 0.429338C17.0304 0.90638 19.1294 3.00537 19.6064 5.72451Z"
          fill="#F0F5FE"
          fillOpacity="0.12"
        />
        <path
          d="M4.54517 12.9204C4.70753 13.8423 5.44484 14.5831 6.37039 14.7236C7.28941 14.863 8.11928 14.9999 9.39385 14.9999C10.6684 14.9999 11.4983 14.863 12.4173 14.7236C13.3429 14.5832 14.0802 13.8423 14.2425 12.9204C14.3678 12.209 14.482 11.5913 14.482 10.3963C14.482 9.20135 14.3678 8.58362 14.2425 7.87229C14.0802 6.95033 13.3429 6.20949 12.4173 6.06907C11.4983 5.92963 10.6684 5.79272 9.39385 5.79272C8.11928 5.79272 7.28941 5.92963 6.37039 6.06907C5.44484 6.20949 4.70752 6.95033 4.54517 7.87229C4.41991 8.58362 4.30566 9.20135 4.30566 10.3963C4.30566 11.5913 4.41991 12.209 4.54517 12.9204Z"
          stroke="white"
        />
        <path
          d="M14.6938 11.8016H11.817C11.0142 11.8016 10.3633 11.1507 10.3633 10.3478C10.3633 9.54492 11.0142 8.89404 11.817 8.89404H14.6938C15.246 8.89404 15.6938 9.34176 15.6938 9.89404V10.8016C15.6938 11.3539 15.246 11.8016 14.6938 11.8016Z"
          stroke="white"
          strokeLinecap="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_2844_50430">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
