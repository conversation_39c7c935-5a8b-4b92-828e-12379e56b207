import { SVGProps } from 'react';

export const ClockIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 36}
      height={props?.height ? props?.height : 36}
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <ellipse cx="18.3911" cy="18.3911" rx="12.913" ry="12.913" fill="url(#paint0_linear_1826_13886)" />
      <circle cx="17.9618" cy="18.8122" r="12.017" transform="rotate(-5.79202 17.9618 18.8122)" fill="#E58D08" />
      <path
        d="M29.7367 15.8187C30.4065 22.4216 25.5967 28.3173 18.9938 28.9871C12.3909 29.6568 6.49528 24.8471 5.82551 18.2442C5.15575 11.6413 9.9655 5.74562 16.5684 5.07585C23.1713 4.40608 29.0669 9.21583 29.7367 15.8187Z"
        fill="url(#paint1_linear_1826_13886)"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18.754 26.6222C24.0508 26.0849 27.9092 21.3554 27.3719 16.0586C26.8346 10.7618 22.1051 6.90341 16.8083 7.4407C11.5114 7.97798 7.65307 12.7075 8.19036 18.0043C8.72764 23.3011 13.4571 27.1595 18.754 26.6222ZM18.9938 28.9871C25.5967 28.3173 30.4065 22.4216 29.7367 15.8187C29.0669 9.21583 23.1713 4.40608 16.5684 5.07585C9.9655 5.74562 5.15575 11.6413 5.82551 18.2442C6.49528 24.8471 12.3909 29.6568 18.9938 28.9871Z"
        fill="url(#paint2_linear_1826_13886)"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M27.3716 16.0586C27.9089 21.3555 24.0506 26.085 18.7537 26.6222C13.4569 27.1595 8.72741 23.3012 8.19013 18.0043C7.65284 12.7075 11.5112 7.97802 16.808 7.44073C22.1049 6.90344 26.8344 10.7618 27.3716 16.0586Z"
        fill="url(#paint3_linear_1826_13886)"
      />
      <g filter="url(#filter0_d_1826_13886)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M20.1687 11.2432C19.725 11.0867 19.2385 11.3196 19.082 11.7634L17.4398 16.4192L11.5686 16.478C11.0981 16.4827 10.7205 16.8679 10.725 17.3384L10.7284 17.6788C10.733 18.1493 11.1181 18.5269 11.5885 18.5222L18.1777 18.4562C18.4114 18.4539 18.6221 18.3577 18.7745 18.2038C18.9369 18.1053 19.0675 17.9521 19.1356 17.759L21.0105 12.4434C21.167 11.9996 20.9342 11.5131 20.4906 11.3566L20.1687 11.2432Z"
          fill="#AC0B0D"
        />
      </g>
      <circle cx="31.73" cy="13.2607" r="1.24226" transform="rotate(15 31.73 13.2607)" fill="#FFA300" />
      <ellipse
        cx="23.9491"
        cy="2.34929"
        rx="1.91819"
        ry="1.91819"
        transform="rotate(15 23.9491 2.34929)"
        fill="#FFA300"
      />
      <defs>
        <filter
          id="filter0_d_1826_13886"
          x="10.7251"
          y="11.1943"
          width="10.334"
          height="8.87198"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1.54386" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0" />
          <feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_1826_13886" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1826_13886" result="shape" />
        </filter>
        <linearGradient
          id="paint0_linear_1826_13886"
          x1="18.3911"
          y1="5.47803"
          x2="18.7824"
          y2="59.478"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#B9FFEA" stopOpacity="0.4" />
          <stop offset="0.104783" stopColor="#AAF7EF" stopOpacity="0.153032" />
          <stop offset="0.948923" stopColor="#FFE600" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_1826_13886"
          x1="16.5684"
          y1="5.07585"
          x2="18.9938"
          y2="28.9871"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#FEA51E" />
          <stop offset="1" stopColor="#F08100" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_1826_13886"
          x1="10.3443"
          y1="7.25486"
          x2="18.9938"
          y2="28.9871"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#FFE090" />
          <stop offset="0.291158" stopColor="#FFC329" />
          <stop offset="1" stopColor="#FFB800" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_1826_13886"
          x1="16.5682"
          y1="5.07588"
          x2="18.9936"
          y2="28.9871"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#FEA51E" />
          <stop offset="1" stopColor="#F08100" />
        </linearGradient>
      </defs>
    </svg>
  );
};
