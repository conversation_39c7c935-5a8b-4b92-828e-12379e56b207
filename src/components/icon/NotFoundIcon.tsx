import { SVGProps } from 'react';

export const NotFoundIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 120}
      height={props?.height ? props?.height : 120}
      viewBox="0 0 120 121"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        d="M107.3 90.1647C106.197 102.046 91.8548 108.358 59.4199 100.561C17.2766 107.244 9.77393 97.2193 12.643 86.8231C15.1332 77.8001 33.5857 71.6001 59.4199 71.6001C85.2541 71.6001 102.7 75.4001 107.3 90.1647Z"
        fill="url(#paint0_linear_1450_131568)"
      />
      <path
        d="M33.9719 32.7082C34.156 30.9871 35.4678 29.691 37.0256 29.691H68.6143L73.9916 24.4004H86.2247C88.0534 24.4004 89.4781 26.1653 89.2823 28.1883L84.2673 80.0004H32.3759C30.5335 80.0004 29.1045 78.2101 29.3223 76.1745L33.9719 32.7082Z"
        fill="url(#paint1_linear_1450_131568)"
      />
      <path
        d="M24.9064 35.0057C24.8377 34.2534 25.3705 33.6001 26.0527 33.6001H78.5291C79.5148 33.6001 80.3404 34.4273 80.4396 35.5143L84.5004 80.0001H31.7909C30.2137 80.0001 28.8927 78.6766 28.734 76.9374L24.9064 35.0057Z"
        fill="url(#paint2_linear_1450_131568)"
      />
      <path
        d="M19.8047 41.5022C19.2871 39.3295 20.758 37.2002 22.7765 37.2002H70.7566C72.8483 37.2002 74.678 38.7678 75.2144 41.0193L84.5002 80.0002H31.3408C29.9463 80.0002 28.7265 78.9551 28.369 77.4542L19.8047 41.5022Z"
        fill="url(#paint3_linear_1450_131568)"
      />
      <path
        d="M109.566 73.9864L112.152 71.6193C113.073 70.7756 113.616 69.5951 113.658 68.345C113.699 67.0949 113.237 65.8807 112.374 64.977L98.8882 50.8684C97.1479 49.0482 94.2774 48.9521 92.4198 50.6516L90.5368 52.3628C88.6816 54.0637 88.5175 56.9375 90.167 58.8396L102.956 73.6157C103.777 74.5547 104.94 75.123 106.183 75.1928C107.427 75.2625 108.646 74.8278 109.566 73.9864Z"
        fill="url(#paint4_linear_1450_131568)"
      />
      <path
        d="M78.1307 53.6843C67.4205 53.6843 58.738 45.0019 58.738 34.2916C58.738 23.5813 67.4205 14.8989 78.1307 14.8989C88.841 14.8989 97.5234 23.5813 97.5234 34.2916C97.5234 45.0019 88.841 53.6843 78.1307 53.6843Z"
        fill="url(#paint5_linear_1450_131568)"
        fillOpacity="0.5"
      />
      <path
        d="M78.1303 14.0493C87.0046 14.0493 95.005 19.3607 98.4008 27.5067C101.797 35.6527 99.9189 45.029 93.6435 51.2631C87.368 57.4973 77.9306 59.3616 69.7322 55.9867C61.5337 52.6118 56.1889 44.6623 56.19 35.8452C56.19 30.0641 58.5017 24.5198 62.6164 20.4322C66.731 16.3446 72.3117 14.0486 78.1303 14.0493ZM78.1303 52.9533C87.6402 52.9533 95.3495 45.2938 95.3495 35.8452C95.3495 26.3967 87.6402 18.7372 78.1303 18.7372C68.6205 18.7372 60.9112 26.3967 60.9112 35.8452C60.9112 45.2938 68.6205 52.9533 78.1303 52.9533Z"
        fill="url(#paint6_linear_1450_131568)"
      />
      <path
        d="M78.9795 13.2002C87.8537 13.2002 95.8542 18.5116 99.2499 26.6576C102.646 34.8036 100.768 44.1798 94.4926 50.414C88.2171 56.6482 78.7797 58.5125 70.5813 55.1376C62.3828 51.7626 57.038 43.8131 57.0391 34.9961C57.0407 22.9581 66.8633 13.2002 78.9795 13.2002ZM78.9795 52.107C88.489 52.107 96.1981 44.4481 96.1986 34.9999C96.1991 25.5518 88.4909 17.8919 78.9814 17.8909C69.4719 17.8898 61.7619 25.548 61.7603 34.9961C61.7572 39.5357 63.57 43.8904 66.7994 47.1017C70.0289 50.313 74.4104 52.1177 78.9795 52.1184V52.107Z"
        fill="url(#paint7_linear_1450_131568)"
      />
      <path
        opacity="0.47"
        d="M96.0519 36.1317C96.0519 36.1317 97.2874 26.8904 88.0985 20.5178C78.9096 14.1451 68.9294 21.116 68.9294 21.116C68.9294 21.116 76.453 13.6466 88.4088 20.0221C97.0426 25.7765 96.0519 36.1317 96.0519 36.1317Z"
        fill="url(#paint8_linear_1450_131568)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_1450_131568"
          x1="59.42"
          y1="73.0853"
          x2="59.23"
          y2="96.8933"
          gradientUnits="userSpaceOnUse">
          <stop offset="0.253438" stopColor="#EFF2F7" />
          <stop offset="1" stopColor="white" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_1450_131568"
          x1="48.3253"
          y1="11.5696"
          x2="58.6958"
          y2="57.3267"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#D3DCE9" />
          <stop offset="1" stopColor="#CFD9E8" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_1450_131568"
          x1="42.6372"
          y1="37.4313"
          x2="58.307"
          y2="108.542"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#EFF5FF" />
          <stop offset="1" stopColor="#F9FBFE" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_1450_131568"
          x1="42.6083"
          y1="37.2002"
          x2="58.431"
          y2="108.666"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#DCE6F7" />
          <stop offset="1" stopColor="#DAE0E9" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_1450_131568"
          x1="105.558"
          y1="57.6727"
          x2="86.699"
          y2="75.3957"
          gradientUnits="userSpaceOnUse">
          <stop offset="0.07" stopColor="#C7DEFF" />
          <stop offset="0.08" stopColor="#C2DBFF" />
          <stop offset="0.19" stopColor="#95C2FD" />
          <stop offset="0.27" stopColor="#78B2FB" />
          <stop offset="0.32" stopColor="#6EACFB" />
          <stop offset="0.43" stopColor="#60A5F2" />
          <stop offset="0.49" stopColor="#58A0ED" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_1450_131568"
          x1="95.5904"
          y1="28.4812"
          x2="58.778"
          y2="40.7292"
          gradientUnits="userSpaceOnUse">
          <stop offset="0.18" stopColor="#D7E9FF" />
          <stop offset="0.95" stopColor="#EEF5FF" />
          <stop offset="0.96" stopColor="white" stopOpacity="0.6" />
        </linearGradient>
        <linearGradient
          id="paint6_linear_1450_131568"
          x1="94.7705"
          y1="48.6933"
          x2="60.4603"
          y2="21.8599"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#52A1F7" />
          <stop offset="0.48" stopColor="#74BCFF" />
          <stop offset="1" stopColor="#52A1F7" />
        </linearGradient>
        <linearGradient
          id="paint7_linear_1450_131568"
          x1="100.324"
          y1="18.8222"
          x2="61.8158"
          y2="48.3826"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#C7DEFF" />
          <stop offset="0.07" stopColor="#AFD0FE" />
          <stop offset="0.16" stopColor="#97C3FD" />
          <stop offset="0.26" stopColor="#85B9FC" />
          <stop offset="0.38" stopColor="#78B1FB" />
          <stop offset="0.53" stopColor="#70ADFB" />
          <stop offset="0.85" stopColor="#6EACFB" />
          <stop offset="0.87" stopColor="#6EACFB" />
          <stop offset="0.99" stopColor="#6EACFB" />
        </linearGradient>
        <linearGradient
          id="paint8_linear_1450_131568"
          x1="92.5905"
          y1="31.0232"
          x2="80.1999"
          y2="17.1538"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#C8DFFF" stopOpacity="0" />
          <stop offset="0.49" stopColor="#B3DFFF" />
          <stop offset="0.67" stopColor="#BEDFFF" stopOpacity="0.48" />
          <stop offset="0.83" stopColor="#C8DFFF" stopOpacity="0" />
        </linearGradient>
      </defs>
    </svg>
  );
};
