import { SVGProps } from "react";
/**
 * @component @name SupportIcon
 *
 * @preview ![img](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACkAAAAoCAMAAABU4iNhAAAAh1BMVEX////////x9v7v9//2+f7+/v/5/P////8AM8ny9v4Az2r2+f79/v/5+//7/f/09/5ggN0gTdC/zPHf5fifs+sAlo2AmeQQQM3v8vxwjOBAZtcASLzP2fWQpuh/meQwWdMAWrFwjOFQc9oAPcMAcaMAtXqvv++vv+4AgZoAi5QAi5MAqIIAqIEqbaoSAAAAB3RSTlMg398g39/fRD9eEAAAAdBJREFUOMuVldlyszAMhfmXtjLygoPDmqVtku7v/3yVS5AMdMpUF5kc+0OCY1vOsjsJaxDVGA7RWJn7I6R1SqExWuuotKbH4oidk1Y5Q8g8tFGoJyQqLjQPo0xCopN8y7zKMInue0bQK2mUvltB9UCugbHoF2lwMrrZF49dUW2mSW0kXfrZ+xqu4UOaFInUSgbKjog2PG9C6wGOpayeItI41o0Hz1UDiYan1E2WIbIswJdJAQ8PLByRzoxqBxBBQWvgCvifSCspD1NzKuhSkt1sJCWP1OObmkiykfTZ3WNgSa56eBrJv9k/JbW+opqo/WhTSj4AvGy3p+ErShLvZ4DiO9LDa57nZ6iGlGcSr3Ac1zMhSzjlFNshTQHbqE5QLskN3C/J+9FR+wtSXNpBvyR72DEpztfwTnNv0A5GvJG4gE+dt+xn/5FfeghRPEF/yT966Hg10x1yhBg+EeKnu53suoOH+tBc17yowdcQEtIwuYgge0bdyOmYxnOoKClvQ+sWJ47NjVHvJyeOfpdk0xZt2IlWOpKafFoJg2O3WQG523D9tQ5Gfx3+CKL0T41Kr3faq+aOPu/ejrs3P+uUQ2NsciPg7EbgiJPOpbeMTm6ZT275O7KT0t2cAAAAAElFTkSuQmCC) 
 *
 * @param {Object} props - size, with, height
 * @returns {JSX.Element} JSX Element
 *
 */
export const SupportIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 40}
      height={props?.height ? props?.height : 40}
      viewBox="0 0 41 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_2838_42418)">
        <rect width="40" height="40" transform="translate(0.833984)" fill="white" />
        <g clipPath="url(#clip1_2838_42418)">
          <path
            d="M39.5544 11.5354L39.5544 11.5354L39.5553 11.5407C40.5934 17.1087 40.5937 22.7716 39.555 28.437L39.5544 28.4407C38.6367 33.6717 34.5057 37.8027 29.2747 38.7204L29.2747 38.7204L29.2695 38.7213C23.7014 39.7595 18.0386 39.7597 12.3732 38.7211L12.3694 38.7204C7.13847 37.8027 3.10285 33.7671 2.18514 28.5361L2.18532 28.5361L2.18263 28.5229C1.05121 22.9601 1.05088 17.2074 2.18295 11.5471L2.18309 11.5471L2.18514 11.5354C3.10285 6.30448 7.13847 2.26886 12.3694 1.35115L12.3694 1.35133L12.3827 1.34864C17.9454 0.217229 23.6981 0.216901 29.3585 1.34897L29.3584 1.3491L29.3701 1.35115C34.6011 2.26886 38.6367 6.30448 39.5544 11.5354Z"
            stroke="#F0F5FE"
          />
        </g>
        <path
          d="M11.3809 19.2628C11.5241 14.537 13.9282 9.28564 21.0003 9.28564C28.0593 9.28564 30.4676 14.5178 30.6188 19.2369"
          stroke="#0033C9"
          strokeWidth="2"
          strokeLinecap="round"
        />
        <path
          d="M10.7139 22.1428C10.7139 18.2245 13.3261 17.2449 14.6322 17.2449C16.9833 17.2449 17.571 19.2041 17.571 20.1836V24.102C17.571 26.453 15.6118 27.0408 14.6322 27.0408C13.3261 27.0408 10.7139 26.0612 10.7139 22.1428Z"
          fill="#00CF6A"
          stroke="#0033C9"
          strokeWidth="2"
        />
        <path
          d="M31.2861 22.1428C31.2861 18.2245 28.6739 17.2449 27.3678 17.2449C25.0167 17.2449 24.429 19.2041 24.429 20.1836V24.102C24.429 26.453 26.3882 27.0408 27.3678 27.0408C28.6739 27.0408 31.2861 26.0612 31.2861 22.1428Z"
          fill="#00CF6A"
          stroke="#0033C9"
          strokeWidth="2"
        />
        <path
          d="M30.7229 24.9019C30.7229 24.9019 30.8569 27.9822 29.2765 29.3215C27.6961 30.6608 23.7051 30.634 23.7051 30.634"
          stroke="#0033C9"
          strokeWidth="2"
          strokeLinecap="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_2838_42418">
          <rect width="40" height="40" fill="white" transform="translate(0.833984)" />
        </clipPath>
        <clipPath id="clip1_2838_42418">
          <rect width="40" height="40" fill="white" transform="translate(0.833984)" />
        </clipPath>
      </defs>
    </svg>
  );
};
