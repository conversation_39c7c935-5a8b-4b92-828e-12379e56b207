import { SVGProps } from "react";

export const LoadingFill = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 24}
      height={props?.height ? props?.height : 24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#paint0_angular_19095_192743_clip_path)" data-figma-skip-parse="true">
        <g transform="matrix(-0.00357143 0.000714286 -0.000714286 -0.00357143 12 12)">
          <foreignObject x="-3715.38" y="-3715.38" width="7430.77" height="7430.77">
            <div
              style={{
                background:
                  "conic-gradient(from 90deg, rgba(242, 244, 245, 0) 0deg, rgba(153, 165, 178, 1) 241.875deg, rgba(153, 165, 178, 1) 360deg)",
                height: "100%",
                width: "100%",
                opacity: 1,
              }}></div>{" "}
          </foreignObject>
        </g>
      </g>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 4.85714C8.05511 4.85714 4.85714 8.05511 4.85714 12C4.85714 12.789 4.21755 13.4286 3.42857 13.4286C2.63959 13.4286 2 12.789 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C11.211 22 10.5714 21.3604 10.5714 20.5714C10.5714 19.7825 11.211 19.1429 12 19.1429C15.9449 19.1429 19.1429 15.9449 19.1429 12C19.1429 8.05511 15.9449 4.85714 12 4.85714Z"
        data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.94901961088180542,&#34;g&#34;:0.95686274766921997,&#34;b&#34;:0.96078431606292725,&#34;a&#34;:0.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:0.60000002384185791,&#34;g&#34;:0.64705884456634521,&#34;b&#34;:0.69803923368453979,&#34;a&#34;:1.0},&#34;position&#34;:0.6718750},{&#34;color&#34;:{&#34;r&#34;:0.60000002384185791,&#34;g&#34;:0.64705884456634521,&#34;b&#34;:0.69803923368453979,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:-7.1428570747375488,&#34;m01&#34;:-1.4285714626312256,&#34;m02&#34;:16.285715103149414,&#34;m10&#34;:1.4285714626312256,&#34;m11&#34;:-7.1428570747375488,&#34;m12&#34;:14.857142448425293},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"
      />
      <defs>
        <clipPath id="paint0_angular_19095_192743_clip_path">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M12 4.85714C8.05511 4.85714 4.85714 8.05511 4.85714 12C4.85714 12.789 4.21755 13.4286 3.42857 13.4286C2.63959 13.4286 2 12.789 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C11.211 22 10.5714 21.3604 10.5714 20.5714C10.5714 19.7825 11.211 19.1429 12 19.1429C15.9449 19.1429 19.1429 15.9449 19.1429 12C19.1429 8.05511 15.9449 4.85714 12 4.85714Z"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
