import { SVGProps } from 'react';
import { colors } from '../../constants/colors';

export const FillXIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 16}
      height={props?.height ? props?.height : 16}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8 14C11.3137 14 14 11.3137 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8C2 11.3137 4.68629 14 8 14ZM4.95986 4.95986C5.23967 4.68005 5.69332 4.68005 5.97313 4.95986L8.00002 6.98675L10.0269 4.95986C10.3067 4.68005 10.7604 4.68005 11.0402 4.95986C11.32 5.23967 11.32 5.69332 11.0402 5.97313L9.0133 8.00002L11.0395 10.0262C11.3193 10.306 11.3193 10.7597 11.0395 11.0395C10.7597 11.3193 10.306 11.3193 10.0262 11.0395L8.00002 9.0133L5.97381 11.0395C5.694 11.3193 5.24034 11.3193 4.96053 11.0395C4.68072 10.7597 4.68072 10.306 4.96053 10.0262L6.98675 8.00002L4.95986 5.97313C4.68005 5.69333 4.68005 5.23967 4.95986 4.95986Z"
        fill={props?.color ? props?.color : colors.dark[200]}>
        </path>
    </svg>
  );
};
