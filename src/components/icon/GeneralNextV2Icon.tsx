import { SVGProps } from "react";
/**
 * @component @name GeneralNextIconV2
 *
 * @preview ![img](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFESURBVHgBpVM9SwNBEH07cFYWIrbCgT8gucZCLDywNgGb/ASJgldY2CXpLCOopbWNoOmsYmWhRWJlIxiwFbGwE7LO7K7c5dwLF/Jg72P2vZnZ2RmFPAYHWyBdw1jX+S+0RjXkB6+gg6g7ytJVKkyWgJ8WoBNMheo6R1+pAyvus7iKUpCMglickDWYyCXFAuGKRjIY7IUAvXl5D2vA5SbQeAS2XzwEigmKWsWRXImu1oH3Zc/+uE7Qqjj1jVdg59l+n8fAx2KeUeMjNPWE6XjXR7RY+QZOridMhDkhGUgBw0JGr8KraqMf3dl3iqFkcFsollvouRLt9/NiiS8O6AZTEjRoPAGrn579oOM6scntiUPMhlNEF4kr4kLbDUxJCFc0f7dgBoN7W7yWiezmwLj6t21aW7XZdyUzHyOYYnO9orP7LP0XYb9V8MMV+3sAAAAASUVORK5CYII=) 
 *
 * @param {Object} props - size, with, height
 * @returns {JSX.Element} JSX Element
 *
 */
export const GeneralNextIconV2 = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 16}
      height={props?.height ? props?.height : 16}
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <rect width="16" height="16" rx="8" fill="#00CF6A" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.18308 4.46557C7.95793 4.65776 7.93765 4.98885 8.13778 5.20507L10.7248 7.99992L8.13778 10.7948C7.93765 11.011 7.95793 11.3421 8.18308 11.5343C8.40823 11.7265 8.753 11.707 8.95314 11.4908L11.8622 8.34792C12.0459 8.14945 12.0459 7.85038 11.8622 7.65192L8.95314 4.50907C8.753 4.29285 8.40823 4.27337 8.18308 4.46557Z"
        fill="#99A5B2"
      />
      <path
        d="M11 8H5M11 8L8.5 5M11 8L8.5 11"
        stroke="#0033C9"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
