import { SVGProps } from 'react';
import { colors } from '../../constants/colors';

export const GeneralNextIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 12}
      height={props?.height ? props?.height : 12}
      viewBox="0 0 6 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.27462 0.698472C-0.0631111 0.986765 -0.0935316 1.48339 0.206674 1.80772L4.08713 6L0.206674 10.1923C-0.0935312 10.5166 -0.0631106 11.0132 0.27462 11.3015C0.612351 11.5898 1.1295 11.5606 1.42971 11.2363L5.79334 6.522C6.06889 6.2243 6.06889 5.7757 5.79334 5.478L1.42971 0.763722C1.1295 0.439393 0.612351 0.41018 0.27462 0.698472Z"
        fill={props?.color ? props?.color : colors.blue[500]}

      />
    </svg>
  );
};
