import { SVGProps } from "react";

export const CardIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 24}
      height={props?.height ? props?.height : 24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="12" fill="#D3EEFF" />
      <path
        d="M5.14307 15.4568C5.14307 16.2499 5.76236 16.9001 6.54504 17.0284C7.80509 17.2351 8.88198 17.5372 12.0002 17.5372C15.1184 17.5372 16.1953 17.2351 17.4554 17.0284C18.2381 16.9001 18.8574 16.2499 18.8574 15.4568V8.30551C18.8574 7.61598 18.3872 7.02032 17.707 6.9072C16.5387 6.7129 14.5416 6.46289 12.0002 6.46289C8.88198 6.46289 7.80509 6.76509 6.54504 6.97172C5.76236 7.10007 5.14307 7.75025 5.14307 8.54338L5.14307 15.4568Z"
        fill="white"
        stroke="#0033C9"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M18.8574 10.3925H5.14307V8.68052C5.14307 7.81034 5.82255 7.09528 6.68036 6.94908C6.83656 6.92246 6.99115 6.89457 7.14847 6.86619C8.2046 6.67564 9.38383 6.46289 12.0002 6.46289C14.461 6.46289 16.4114 6.69728 17.5933 6.88854C18.3401 7.00939 18.8574 7.66302 18.8574 8.41954V10.3925Z"
        fill="#00CF6A"
        stroke="#0033C9"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.7773 14.5356H16.1002"
        stroke="#0033C9"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
