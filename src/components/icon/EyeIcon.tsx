import { SVGProps } from 'react';
import { colors } from '../../constants/colors';

export const EyeIcon = ({ open = true , ...props}: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement> & { open?: boolean }) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 16}
      height={props?.height ? props?.height : 16}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      {open ? (
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0.915119 8.69006C0.753402 8.48776 0.666667 8.24685 0.666667 7.99998C0.666667 7.7531 0.753402 7.5122 0.915119 7.3099C1.96382 6.01081 4.5459 3.33331 8 3.33331C11.4541 3.33331 14.0362 6.01081 15.0849 7.3099C15.2466 7.5122 15.3333 7.7531 15.3333 7.99998C15.3333 8.24685 15.2466 8.48776 15.0849 8.69006C14.0362 9.98915 11.4541 12.6666 8 12.6666C4.5459 12.6666 1.96382 9.98915 0.915119 8.69006ZM7.99994 9.33331C8.73632 9.33331 9.33327 8.73636 9.33327 7.99998C9.33327 7.93735 9.32895 7.87573 9.3206 7.8154C9.27109 7.82712 9.21944 7.83333 9.16634 7.83333C8.79815 7.83333 8.49967 7.53486 8.49967 7.16667C8.49967 7.03564 8.53748 6.91344 8.60276 6.81038C8.42172 6.71846 8.21688 6.66665 7.99994 6.66665C7.26356 6.66665 6.66661 7.2636 6.66661 7.99998C6.66661 8.73636 7.26356 9.33331 7.99994 9.33331ZM11.3333 7.99998C11.3333 9.84093 9.84089 11.3333 7.99994 11.3333C6.15899 11.3333 4.66661 9.84093 4.66661 7.99998C4.66661 6.15903 6.15899 4.66665 7.99994 4.66665C9.84089 4.66665 11.3333 6.15903 11.3333 7.99998Z"
          fill={props?.color ? props?.color : colors.dark[300]}
        />
      ) : (
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M1.95989 4.04109C1.56936 3.65057 1.56936 3.0174 1.95989 2.62688C2.35041 2.23635 2.98357 2.23635 3.3741 2.62688L13.3741 12.6269C13.7646 13.0174 13.7646 13.6506 13.3741 14.0411C12.9836 14.4316 12.3504 14.4316 11.9599 14.0411L10.2172 12.2984C9.52873 12.5284 8.78715 12.6673 8.00033 12.6673C4.54622 12.6673 1.96414 9.98982 0.915444 8.69073C0.753727 8.48843 0.666992 8.24753 0.666992 8.00065C0.666992 7.75378 0.753727 7.51287 0.915444 7.31057C1.37035 6.74705 2.11378 5.92417 3.09176 5.17296L1.95989 4.04109ZM4.84421 6.92542C4.72932 7.26276 4.66699 7.62443 4.66699 8.00065C4.66699 9.8416 6.15938 11.334 8.00033 11.334C8.37655 11.334 8.73821 11.2717 9.07556 11.1568L4.84421 6.92542ZM6.0887 3.60801L7.23603 4.75535C7.48146 4.69777 7.73734 4.66732 8.00033 4.66732C9.84127 4.66732 11.3337 6.1597 11.3337 8.00065C11.3337 8.26364 11.3032 8.51951 11.2456 8.76494L13.1327 10.652C14.0007 9.95077 14.6657 9.21044 15.0852 8.69073C15.2469 8.48843 15.3337 8.24753 15.3337 8.00065C15.3337 7.75378 15.2469 7.51287 15.0852 7.31057C14.0365 6.01148 11.4544 3.33398 8.00033 3.33398C7.32851 3.33398 6.68967 3.43527 6.0887 3.60801Z"
          fill={props?.color ? props?.color : colors.dark[300]}
        />
      )}
    </svg>
  );
};
