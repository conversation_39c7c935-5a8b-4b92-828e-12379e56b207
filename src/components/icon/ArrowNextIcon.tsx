import { SVGProps } from 'react';
import { colors } from '../../constants/colors';

export const ArrowNextIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 16}
      height={props?.height ? props?.height : 16}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.781 5.64298L12.9024 7.7643C13.0325 7.89448 13.0325 8.10553 12.9024 8.2357L10.781 10.357C10.6509 10.4872 10.4398 10.4872 10.3096 10.357C10.1795 10.2269 10.1795 10.0158 10.3096 9.88562L11.8619 8.33334L3.33333 8.33334C3.14924 8.33334 3 8.1841 3 8C3 7.81591 3.14924 7.66667 3.33333 7.66667L11.8619 7.66667L10.3096 6.11438C10.1795 5.98421 10.1795 5.77316 10.3096 5.64298C10.4398 5.51281 10.6509 5.51281 10.781 5.64298Z"
        fill={props?.color ? props?.color : colors.white}
      />
    </svg>
  );
};
