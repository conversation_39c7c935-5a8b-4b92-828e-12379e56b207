import { SVGProps } from "react";
import { colors } from "../../constants/colors";

export const GeneralDocinsurIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} width={props?.width ? props?.width : 12} height={props?.height ? props?.height : 12} viewBox="0 0 11 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.754 1.49658C2.08992 1.49658 1.55157 2.03493 1.55157 2.69901V9.67895C1.55157 10.343 2.08992 10.8814 2.754 10.8814H6.18786C5.7951 10.3986 5.55966 9.78279 5.55966 9.11194C5.55966 7.56242 6.8158 6.30628 8.36532 6.30628C8.79563 6.30628 9.20331 6.40316 9.56774 6.57628V2.69901C9.56774 2.03493 9.0294 1.49658 8.36532 1.49658H2.754ZM3.35521 3.09982C3.02317 3.09982 2.754 3.36899 2.754 3.70103C2.754 4.03307 3.02317 4.30224 3.35521 4.30224H7.76411C8.09615 4.30224 8.36532 4.03307 8.36532 3.70103C8.36532 3.36899 8.09615 3.09982 7.76411 3.09982H3.35521ZM2.754 6.10588C2.754 5.77384 3.02317 5.50467 3.35521 5.50467H4.95845C5.29049 5.50467 5.55966 5.77384 5.55966 6.10588C5.55966 6.43792 5.29049 6.70709 4.95845 6.70709H3.35521C3.02317 6.70709 2.754 6.43792 2.754 6.10588ZM3.35521 7.90952C3.02317 7.90952 2.754 8.17869 2.754 8.51073C2.754 8.84277 3.02317 9.11194 3.35521 9.11194H3.75602C4.08806 9.11194 4.35723 8.84277 4.35723 8.51073C4.35723 8.17869 4.08806 7.90952 3.75602 7.90952H3.35521ZM8.6744 7.50871C8.64828 7.50871 8.62274 7.51622 8.60099 7.53029C8.10074 7.85817 7.5305 8.07145 6.93319 8.15408C6.90158 8.15842 6.87264 8.17372 6.85171 8.19715C6.83077 8.22058 6.81924 8.25058 6.81923 8.28162V9.44108C6.81923 10.4434 7.43091 11.1751 8.6267 11.6227C8.6574 11.6342 8.69141 11.6342 8.72211 11.6227C9.91816 11.1751 10.5296 10.4436 10.5296 9.44108V8.28162C10.5296 8.25062 10.5181 8.22066 10.4972 8.19723C10.4763 8.1738 10.4474 8.15848 10.4159 8.15408C9.81848 8.07149 9.24814 7.85821 8.74781 7.53029C8.72607 7.51622 8.70053 7.50871 8.6744 7.50871ZM9.72296 9.49814C9.91095 9.32884 9.9261 9.0392 9.75681 8.85122C9.58752 8.66323 9.29788 8.64807 9.10989 8.81737L8.49564 9.37054L8.23297 9.14199C8.04212 8.97593 7.75278 8.99603 7.58672 9.18688C7.42066 9.37773 7.44076 9.66707 7.63161 9.83313L8.20018 10.3278C8.37482 10.4798 8.63537 10.4776 8.80739 10.3227L9.72296 9.49814Z"
        fill={props?.color ? props?.color : colors.white}
      />
    </svg>
  );
};
