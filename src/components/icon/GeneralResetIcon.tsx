import { SVGProps } from 'react';
import { colors } from '../../constants/colors';

export const GeneralResetIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 16}
      height={props?.height ? props?.height : 16}
      viewBox="0 0 16 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.00788 3.33337C5.98258 3.33337 4.25692 4.62732 3.61739 6.4367C3.43334 6.95742 2.86202 7.23034 2.3413 7.04629C1.82059 6.86225 1.54766 6.29093 1.73171 5.77021C2.64484 3.18673 5.10864 1.33337 8.00788 1.33337C10.0558 1.33337 11.8865 2.25813 13.1069 3.71176L14.3594 3.06858C14.8877 2.79727 15.4803 3.30019 15.2986 3.86564L14.162 7.40151C14.1057 7.57677 13.918 7.67318 13.7427 7.61685L10.2068 6.48031C9.64137 6.29856 9.57794 5.52389 10.1063 5.25258L11.2617 4.65924C10.4221 3.83865 9.27349 3.33337 8.00788 3.33337ZM4.911 11.4643L5.89564 10.9609C6.42447 10.6905 6.36243 9.91575 5.79732 9.73299L2.26349 8.59009C2.08833 8.53344 1.90041 8.62952 1.84376 8.80468L0.700867 12.3385C0.5181 12.9036 1.10982 13.4076 1.63865 13.1372L3.04251 12.4195C4.26081 13.784 6.03336 14.6436 8.00788 14.6436C10.9071 14.6436 13.3709 12.7903 14.2841 10.2068C14.4681 9.68607 14.1952 9.11474 13.6745 8.9307C13.1537 8.74665 12.5824 9.01957 12.3984 9.54029C11.7588 11.3497 10.0332 12.6436 8.00788 12.6436C6.81944 12.6436 5.73418 12.1981 4.911 11.4643Z"
        fill={props?.color ? props?.color : colors.blue[500]}
      />
    </svg>
  );
};
