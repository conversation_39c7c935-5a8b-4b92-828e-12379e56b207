import { SVGProps } from "react";
/**
 * @component @name NoteIcon
 *
 * @preview ![img](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACkAAAAoCAMAAABU4iNhAAAAbFBMVEX////////2+f/y9v/v9//7/f////8AM8ny9v77/f/2+f/4+/+/zPFAZtcAz2r09/7f5figs+tggN0ASL3v8vwgTc8QQMwAqIHP2fUgTdAAXLCAmeSPpugwWdMAlY2wv+9vjOEAPcNQc9kAu3a4gHLBAAAABnRSTlMg39/fIN8ZpdoKAAABcklEQVQ4y5XVi3KDIBAFUJs2LMtCIYlajXm2//+PXVIEpEk0dyYG9LiMD7B6EzGIpKwMsVYhipQqSlR8VBGGw/zvz7JUSrRSeVKGrKSJJKnEg6BUSTJE8ThKBjkHmaogkeHzWLpJf8pMUN5kXrJ2Tv+lLsdnSbGkNpAyFEVZWhwhQAuw5+x8w+RlJbKUY6+HSwe7T84XmK6B3iWpqKrWNnQ6aISLUrgGjqkqqUxqMLn0dJukraqVui+ZtnBIl8SSQmcD26kUZ2jj+F7iQykMbDL5XsrT/meU20x+/JMn4N8SefW3/zov6wZgcLw5zEnhBs2bA4M5maeUq2Uy3fl5mZ7mGb5LaUCHFhbP/blcy9Bx0Jayhy6+dfmb3MAwcfUFmjQ7i9kB/dGE9H0LoCezI03i7giTGD2dcUJmA7pOhzhX59No8cqAC1cbSa+uYNxcuir6qjS70qbVm4q6cf/9LwJi8NwgVX4RYhCVtXKMVZNRql/eLDK6w6yzowAAAABJRU5ErkJggg==) 
 *
 * @param {Object} props - size, with, height
 * @returns {JSX.Element} JSX Element
 *
 */
export const NoteIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} width={props?.width ? props?.width : 40} height={props?.height ? props?.height : 40} viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_2838_42395)">
        <g clipPath="url(#clip1_2838_42395)">
          <path
            d="M38.8874 11.5354L38.8874 11.5354L38.8883 11.5407C39.9264 17.1087 39.9267 22.7716 38.8881 28.437L38.8874 28.4407C37.9697 33.6717 33.8387 37.8027 28.6077 38.7204L28.6077 38.7204L28.6025 38.7213C23.0345 39.7595 17.3716 39.7597 11.7062 38.7211L11.7024 38.7204C6.47147 37.8027 2.43585 33.7671 1.51815 28.5361L1.51833 28.5361L1.51564 28.5229C0.384221 22.9601 0.383893 17.2074 1.51596 11.5471L1.5161 11.5471L1.51815 11.5354C2.43585 6.30448 6.47147 2.26886 11.7024 1.35115L11.7024 1.35133L11.7157 1.34864C17.2785 0.217229 23.0311 0.216901 28.6915 1.34897L28.6914 1.3491L28.7031 1.35115C33.9341 2.26886 37.9697 6.30448 38.8874 11.5354Z"
            stroke="#F0F5FE"
          />
        </g>
        <path
          d="M29.212 27.0009C29.3539 24.7412 29.4284 22.3995 29.4284 20.0001C29.4284 19.0283 29.4162 18.066 29.3922 17.1147C29.3754 16.4465 29.1587 15.7971 28.7631 15.2582C27.2555 13.2043 26.0543 11.9274 24.0749 10.3997C23.5309 9.97985 22.8634 9.75207 22.1765 9.73705C21.4876 9.722 20.7683 9.71436 19.9999 9.71436C17.6737 9.71436 15.7972 9.78442 13.8554 9.91793C12.2034 10.0315 10.8914 11.3466 10.7877 12.9992C10.6458 15.2589 10.5713 17.6006 10.5713 20.0001C10.5713 22.3995 10.6458 24.7412 10.7877 27.0009C10.8914 28.6535 12.2034 29.9686 13.8554 30.0822C15.7972 30.2157 17.6737 30.2858 19.9999 30.2858C22.3261 30.2858 24.2025 30.2157 26.1444 30.0822C27.7963 29.9686 29.1083 28.6535 29.212 27.0009Z"
          stroke="#0033C9"
          strokeWidth="2"
        />
        <path
          d="M14.8574 9.85454V19.645C14.8574 20.4087 15.7807 20.7911 16.3207 20.2511L18.286 18.2858L20.2513 20.2511C20.7913 20.7911 21.7146 20.4087 21.7146 19.645V9.72815C21.167 9.71899 20.5985 9.71436 20.0003 9.71436C18.0753 9.71436 16.4583 9.76234 14.8574 9.85454Z"
          fill="#00CF6A"
          stroke="#0033C9"
          strokeWidth="2"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_2838_42395">
          <rect width="40" height="40" fill="white" transform="translate(0.166992)" />
        </clipPath>
        <clipPath id="clip1_2838_42395">
          <rect width="40" height="40" fill="white" transform="translate(0.166992)" />
        </clipPath>
      </defs>
    </svg>
  );
};
