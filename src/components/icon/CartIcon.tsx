import { SVGProps } from "react";

/**
 * @component @name CartIcon
 *
 * @preview ![img](data:image/svg+xml;base64,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)
 *
 * @param {Object} props - size, with, height
 * @returns {JSX.Element} JSX Element
 *
 */
export const CartIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 40}
      height={props?.height ? props?.height : 40}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.81664 4.59837C2.67752 4.21261 2.87403 3.78664 3.25778 3.64208L4.9367 3.00964C5.25519 2.88967 5.61454 2.99919 5.81198 3.2764C5.86758 3.35447 6.00667 3.49567 6.18937 3.58258C6.35271 3.66028 6.53071 3.68804 6.74121 3.60875C6.9517 3.52945 7.06715 3.39116 7.13863 3.225C7.21859 3.03915 7.22995 2.84127 7.22022 2.74593C7.1857 2.40734 7.38348 2.08795 7.70197 1.96798L9.47965 1.29834C9.86449 1.15337 10.2943 1.34535 10.4432 1.7287L10.8451 2.76378C11.0214 2.74182 11.1514 2.66005 11.2582 2.54793C11.3977 2.40146 11.4772 2.21986 11.5012 2.12708C11.5865 1.79747 11.8832 1.56669 12.2236 1.56505L14.3225 1.55497C14.522 1.55401 14.7137 1.6326 14.8551 1.77336C14.9966 1.91411 15.0761 2.10542 15.0761 2.30496V9.00085H16.4245L17.1368 5.7464C17.4954 4.10794 18.9464 2.94019 20.6237 2.94019H21.3638C21.784 2.94019 22.1247 3.28084 22.1247 3.70105C22.1247 4.12127 21.784 4.46192 21.3638 4.46192H20.6237C19.6615 4.46192 18.8291 5.13183 18.6234 6.07176C18.1458 8.25372 16.568 15.0061 16.568 15.0061C16.506 15.2994 16.445 15.5725 16.3831 15.8224C16.0755 17.0653 15.0465 17.892 13.8133 18.0204C11.9954 18.2096 9.23776 18.1932 7.26307 18.0081C5.8765 17.8782 4.74758 16.9807 4.31625 15.6528C3.92921 14.4613 3.48984 12.8939 3.16906 11.7107C2.87055 10.6096 3.47883 9.53453 4.458 9.14969L2.81664 4.59837ZM5.9513 9.00085H11.658L9.31364 2.96377L8.69301 3.19755C8.66257 3.39588 8.60687 3.60779 8.51653 3.81779C8.31443 4.28754 7.92856 4.76437 7.26998 5.01246C6.61139 5.26054 6.00682 5.1568 5.54503 4.93713C5.33858 4.83893 5.15692 4.71644 5.00319 4.58749L4.47855 4.78512L5.90662 8.745C5.9371 8.82952 5.95147 8.91592 5.9513 9.00085ZM13.2671 9.00085H13.5761V3.05857L12.7251 3.06266C12.6276 3.23797 12.5018 3.41721 12.3442 3.58265C12.1066 3.83194 11.7899 4.05529 11.3934 4.17575L13.2671 9.00085ZM5.24999 10.5226C4.82758 10.5226 4.53058 10.9171 4.63778 11.3125C4.95768 12.4925 5.38886 14.0292 5.76355 15.1827C6.00617 15.9296 6.61959 16.4194 7.40507 16.493C9.29748 16.6704 11.9534 16.6841 13.6557 16.5069C14.2919 16.4406 14.762 16.0384 14.9059 15.4569C15.0381 14.9229 15.1669 14.2725 15.3209 13.4951C15.3527 13.3345 15.3856 13.1685 15.4198 12.9969C15.5675 12.2563 15.7372 11.4272 15.9468 10.5226H5.24999Z"
        fill="currentColor"
      />
      <path
        d="M3.78809 20.8157C3.78809 19.9933 4.45479 19.3266 5.27721 19.3266C6.09963 19.3266 6.76634 19.9933 6.76634 20.8157C6.76634 21.6381 6.09963 22.3049 5.27721 22.3049C4.45479 22.3049 3.78809 21.6381 3.78809 20.8157Z"
        fill="currentColor"
      />
      <path
        d="M14.7445 19.3266C13.9221 19.3266 13.2554 19.9933 13.2554 20.8157C13.2554 21.6381 13.9221 22.3049 14.7445 22.3049C15.5669 22.3049 16.2336 21.6381 16.2336 20.8157C16.2336 19.9933 15.5669 19.3266 14.7445 19.3266Z"
        fill="currentColor"
      />
    </svg>
  );
};
