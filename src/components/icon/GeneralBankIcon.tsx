import { SVGProps } from 'react';
import { colors } from '../../constants/colors';

export const GeneralBankIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 12}
      height={props?.height ? props?.height : 12}
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.3554 3.98872L5.18566 1.8608C5.65782 1.59848 6.23194 1.59848 6.7041 1.8608L10.5344 3.98872C10.5964 4.02319 10.6349 4.08858 10.6349 4.15955V4.37026C10.6349 4.47818 10.5474 4.56567 10.4395 4.56567H1.4503C1.34237 4.56567 1.25488 4.47818 1.25488 4.37026V4.15955C1.25488 4.08858 1.29336 4.02319 1.3554 3.98872ZM1.25488 10.0373C1.25488 9.82148 1.42986 9.64649 1.64572 9.64649H10.244C10.4599 9.64649 10.6349 9.82148 10.6349 10.0373V10.4282C10.6349 10.644 10.4599 10.819 10.244 10.819H1.64572C1.42987 10.819 1.25488 10.644 1.25488 10.4282V10.0373ZM2.6228 5.54274C2.51487 5.54274 2.42738 5.63024 2.42738 5.73816V8.47399C2.42738 8.58192 2.51487 8.66941 2.6228 8.66941H3.7953C3.90323 8.66941 3.99072 8.58192 3.99072 8.47399V5.73816C3.99072 5.63024 3.90323 5.54274 3.7953 5.54274H2.6228ZM5.16322 5.73816C5.16322 5.63024 5.25071 5.54274 5.35863 5.54274H6.53113C6.63906 5.54274 6.72655 5.63024 6.72655 5.73816V8.47399C6.72655 8.58192 6.63906 8.66941 6.53113 8.66941H5.35863C5.25071 8.66941 5.16322 8.58192 5.16322 8.47399V5.73816ZM8.09447 5.54274C7.98654 5.54274 7.89905 5.63024 7.89905 5.73816V8.47399C7.89905 8.58192 7.98654 8.66941 8.09447 8.66941H9.26697C9.37489 8.66941 9.46238 8.58192 9.46238 8.47399V5.73816C9.46238 5.63024 9.37489 5.54274 9.26697 5.54274H8.09447Z"
        fill={props?.color ? props?.color : colors.white}
      />
    </svg>
  );
};
