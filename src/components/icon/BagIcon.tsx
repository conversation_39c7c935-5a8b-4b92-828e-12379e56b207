import { SVGProps } from "react";

/**
 * @component @name BagIcon
 *
 * @preview ![img](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACkAAAAoCAMAAABU4iNhAAAAbFBMVEX////y9/7///8AM8nx9v72+f78/f+/zPH4+/9AZtfz9/76/P9ggN0D0Gzv8vwgTdAQQMzf5fiAmeSvv++fs+uPpudwjOHf+e1Qc9owWdPP2fXP9uO/89pA24+gs+uQpuhQc9mv8NFQ3pgg1X1SXpS+AAAAAnRSTlOAgKCo1lMAAAGsSURBVDjLlZXZmoMgDIVbQyIEXKrWtrMv7/+OY2Eaw6jtN+dGlt8QQgi7XTGLLCKbJGa0pOZ2M2nZTJOe0jQRWZxGhBbSGqRiKUJjM5KYhVuyNJMUf9ySNZRIAe+iicRNUDabSG+KR2IbSc5NluPZufdDNub5SlJmsj5DUpehxk+kRTVyaKAd+7IPAEGTiBOJXo0EaNK6T01m1V9JHfNyAqXZ1ir+ZiJNZnKUtoOjdvRqU/UvUEp7gJCTe002MEenB6e3tN/t8Raf7uwAnOgCrXNva2QDawqalG335V/1LdQLsoePQqQdX7HpVkhYsXmAJn6fT/FzOv2SSz9raCNRfcXudxXPFJqVvSfnX6rX2KsqcUlIlvN5WpBHiRLv53MP8HZ1MyNHOXtWGXJM2fF5Sht7SVlSqgwxtB0mCTwZlcl1OyeSLHO55Sfr29EBnLvhdqTD0DmAQW5HduNG+KNWctlQvMVe7tt7F9yvQuiGeq4iqTJw8UjGRlJKw6YsSgXzd0Fv6L9VMaJIW6CqtKm/zpLV1VuqtGG0JC+Ct4gmckKKyCMym6T0kKhX5gclohostBFtmgAAAABJRU5ErkJggg==) 
 *
 * @param {Object} props - size, with, height
 * @returns {JSX.Element} JSX Element
 *
 */
export const BagIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 40}
      height={props?.height ? props?.height : 40}
      viewBox="0 0 41 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_2838_42403)">
        <rect width="40" height="40" transform="translate(0.5)" fill="white" />
        <g clipPath="url(#clip1_2838_42403)">
          <path
            d="M39.2204 11.5354L39.2204 11.5354L39.2213 11.5407C40.2595 17.1087 40.2597 22.7716 39.2211 28.437L39.2204 28.4407C38.3027 33.6717 34.1717 37.8027 28.9407 38.7204L28.9407 38.7204L28.9355 38.7213C23.3675 39.7595 17.7046 39.7597 12.0392 38.7211L12.0354 38.7204C6.80448 37.8027 2.76886 33.7671 1.85115 28.5361L1.85133 28.5361L1.84864 28.5229C0.717229 22.9601 0.716901 17.2074 1.84897 11.5471L1.8491 11.5471L1.85115 11.5354C2.76886 6.30448 6.80448 2.26886 12.0354 1.35115L12.0354 1.35133L12.0487 1.34864C17.6115 0.217229 23.3642 0.216901 29.0245 1.34897L29.0245 1.3491L29.0361 1.35115C34.2671 2.26886 38.3027 6.30448 39.2204 11.5354Z"
            stroke="#F0F5FE"
          />
        </g>
        <path
          d="M10.8099 27.743C10.1115 24.7336 9.78921 21.2995 9.66852 19.0988C9.6141 18.1065 10.414 17.301 11.4078 17.301H29.4016C30.3954 17.301 31.1953 18.1065 31.1409 19.0988C31.0202 21.2995 30.6979 24.7336 29.9995 27.743C29.6758 29.1376 28.6492 30.1995 27.2323 30.405C25.7724 30.6168 23.5555 30.7964 20.4047 30.7964C17.2539 30.7964 15.037 30.6168 13.5771 30.405C12.1603 30.1995 11.1336 29.1376 10.8099 27.743Z"
          stroke="#0033C9"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M14.1064 17.3009V15.5015C14.1064 12.0233 16.9261 9.20361 20.4043 9.20361V9.20361C23.8825 9.20361 26.7021 12.0233 26.7021 15.5015V17.3009"
          stroke="#0033C9"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M16.8057 22.699V25.3981M24.0032 22.699V25.3981"
          stroke="#00CF6A"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_2838_42403">
          <rect width="40" height="40" fill="white" transform="translate(0.5)" />
        </clipPath>
        <clipPath id="clip1_2838_42403">
          <rect width="40" height="40" fill="white" transform="translate(0.5)" />
        </clipPath>
      </defs>
    </svg>
  );
};
