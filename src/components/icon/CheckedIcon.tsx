import { SVGProps } from 'react';
import { colors } from '../../constants/colors';

export const CheckedIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 16}
      height={props?.height ? props?.height : 16}
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.375 15.5C12.241 15.5 15.375 12.366 15.375 8.5C15.375 4.63401 12.241 1.5 8.375 1.5C4.50901 1.5 1.375 4.63401 1.375 8.5C1.375 12.366 4.50901 15.5 8.375 15.5ZM12.0271 7.6574C12.3349 7.38021 12.3597 6.90598 12.0825 6.59818C11.8053 6.29039 11.3311 6.26557 11.0233 6.54276L7.63017 9.59848L5.71752 7.93428C5.40503 7.66238 4.9313 7.69529 4.65941 8.00777C4.38751 8.32026 4.42042 8.79399 4.7329 9.06588L7.14641 11.1659C7.43235 11.4147 7.85897 11.411 8.14062 11.1574L12.0271 7.6574Z"
        fill={props?.color ? props?.color : colors.primary.blue}/>
    </svg>
  );
};
