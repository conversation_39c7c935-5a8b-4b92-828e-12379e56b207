import { SVGProps } from 'react';

export const LockupIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 24}
      height={props?.height ? props?.height : 24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.8875 10.3533C16.8875 13.9626 13.9618 16.8885 10.3528 16.8885C6.74377 16.8885 3.81807 13.9626 3.81807 10.3533C3.81807 6.74406 6.74377 3.81818 10.3528 3.81818C13.9618 3.81818 16.8875 6.74406 16.8875 10.3533ZM15.5021 16.931C14.0832 18.0434 12.2955 18.7066 10.3528 18.7066C5.73968 18.7066 2 14.9667 2 10.3533C2 5.73991 5.73968 2 10.3528 2C14.9659 2 18.7056 5.73991 18.7056 10.3533C18.7056 12.2955 18.0428 14.0828 16.9311 15.5015L21.5642 19.6936C22.1258 20.2017 22.1477 21.0765 21.6121 21.6121C21.0766 22.1477 20.2019 22.1258 19.6937 21.5642L15.5021 16.931Z"
        fill={props?.color ? props?.color : '#99A5B2'}
      />
    </svg>
  );
};
