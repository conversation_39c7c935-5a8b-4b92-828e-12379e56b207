import { SVGProps } from "react";

/**
 * @component @name ListIcon
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQuMTY2MDIgNS42MjVDNC4xNjYwMiA1Ljk3MDE4IDMuODg2MTkgNi4yNSAzLjU0MTAyIDYuMjVDMy4xOTU4NCA2LjI1IDIuOTE2MDIgNS45NzAxOCAyLjkxNjAyIDUuNjI1QzIuOTE2MDIgNS4yNzk4MiAzLjE5NTg0IDUgMy41NDEwMiA1QzMuODg2MTkgNSA0LjE2NjAyIDUuMjc5ODIgNC4xNjYwMiA1LjYyNVoiIGZpbGw9IiMwMDMzQzkiLz4KPHBhdGggZD0iTTUuODMyNjggNS42MjVDNS44MzI2OCA1LjI3OTgyIDYuMTEyNSA1IDYuNDU3NjggNUgxNi40NTc3QzE2LjgwMjkgNSAxNy4wODI3IDUuMjc5ODIgMTcuMDgyNyA1LjYyNUMxNy4wODI3IDUuOTcwMTggMTYuODAyOSA2LjI1IDE2LjQ1NzcgNi4yNUg2LjQ1NzY4QzYuMTEyNTEgNi4yNSA1LjgzMjY4IDUuOTcwMTggNS44MzI2OCA1LjYyNVoiIGZpbGw9IiMwMDMzQzkiLz4KPHBhdGggZD0iTTQuMTY2MDIgMTBDNC4xNjYwMiAxMC4zNDUyIDMuODg2MTkgMTAuNjI1IDMuNTQxMDIgMTAuNjI1QzMuMTk1ODQgMTAuNjI1IDIuOTE2MDIgMTAuMzQ1MiAyLjkxNjAyIDEwQzIuOTE2MDIgOS42NTQ4MiAzLjE5NTg0IDkuMzc1IDMuNTQxMDIgOS4zNzVDMy44ODYxOSA5LjM3NSA0LjE2NjAyIDkuNjU0ODIgNC4xNjYwMiAxMFoiIGZpbGw9IiMwMDMzQzkiLz4KPHBhdGggZD0iTTUuODMyNjggMTBDNS44MzI2OCA5LjY1NDgyIDYuMTEyNSA5LjM3NSA2LjQ1NzY4IDkuMzc1SDE2LjQ1NzdDMTYuODAyOSA5LjM3NSAxNy4wODI3IDkuNjU0ODIgMTcuMDgyNyAxMEMxNy4wODI3IDEwLjM0NTIgMTYuODAyOSAxMC42MjUgMTYuNDU3NyAxMC42MjVINi40NTc2OEM2LjExMjUxIDEwLjYyNSA1LjgzMjY4IDEwLjM0NTIgNS44MzI2OCAxMFoiIGZpbGw9IiMwMDMzQzkiLz4KPHBhdGggZD0iTTQuMTY2MDIgMTQuMzc1QzQuMTY2MDIgMTQuNzIwMiAzLjg4NjE5IDE1IDMuNTQxMDIgMTVDMy4xOTU4NCAxNSAyLjkxNjAyIDE0LjcyMDIgMi45MTYwMiAxNC4zNzVDMi45MTYwMiAxNC4wMjk4IDMuMTk1ODQgMTMuNzUgMy41NDEwMiAxMy43NUMzLjg4NjE5IDEzLjc1IDQuMTY2MDIgMTQuMDI5OCA0LjE2NjAyIDE0LjM3NVoiIGZpbGw9IiMwMDMzQzkiLz4KPHBhdGggZD0iTTUuODMyNjggMTQuMzc1QzUuODMyNjggMTQuMDI5OCA2LjExMjUgMTMuNzUgNi40NTc2OCAxMy43NUgxNi40NTc3QzE2LjgwMjkgMTMuNzUgMTcuMDgyNyAxNC4wMjk4IDE3LjA4MjcgMTQuMzc1QzE3LjA4MjcgMTQuNzIwMiAxNi44MDI5IDE1IDE2LjQ1NzcgMTVINi40NTc2OEM2LjExMjUxIDE1IDUuODMyNjggMTQuNzIwMiA1LjgzMjY4IDE0LjM3NVoiIGZpbGw9IiMwMDMzQzkiLz4KPC9zdmc+Cg==)
 *
 * @param {Object} props - size, with, height, color
 * @returns {JSX.Element} JSX Element
 *
 */
export const ListIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  const { color = "#0033C9", width = 20, height = 20 } = props;
  return (
    <svg width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M4.16602 5.625C4.16602 5.97018 3.88619 6.25 3.54102 6.25C3.19584 6.25 2.91602 5.97018 2.91602 5.625C2.91602 5.27982 3.19584 5 3.54102 5C3.88619 5 4.16602 5.27982 4.16602 5.625Z"
        fill={color}
      />
      <path
        d="M5.83268 5.625C5.83268 5.27982 6.1125 5 6.45768 5H16.4577C16.8029 5 17.0827 5.27982 17.0827 5.625C17.0827 5.97018 16.8029 6.25 16.4577 6.25H6.45768C6.11251 6.25 5.83268 5.97018 5.83268 5.625Z"
        fill={color}
      />
      <path
        d="M4.16602 10C4.16602 10.3452 3.88619 10.625 3.54102 10.625C3.19584 10.625 2.91602 10.3452 2.91602 10C2.91602 9.65482 3.19584 9.375 3.54102 9.375C3.88619 9.375 4.16602 9.65482 4.16602 10Z"
        fill={color}
      />
      <path
        d="M5.83268 10C5.83268 9.65482 6.1125 9.375 6.45768 9.375H16.4577C16.8029 9.375 17.0827 9.65482 17.0827 10C17.0827 10.3452 16.8029 10.625 16.4577 10.625H6.45768C6.11251 10.625 5.83268 10.3452 5.83268 10Z"
        fill={color}
      />
      <path
        d="M4.16602 14.375C4.16602 14.7202 3.88619 15 3.54102 15C3.19584 15 2.91602 14.7202 2.91602 14.375C2.91602 14.0298 3.19584 13.75 3.54102 13.75C3.88619 13.75 4.16602 14.0298 4.16602 14.375Z"
        fill={color}
      />
      <path
        d="M5.83268 14.375C5.83268 14.0298 6.1125 13.75 6.45768 13.75H16.4577C16.8029 13.75 17.0827 14.0298 17.0827 14.375C17.0827 14.7202 16.8029 15 16.4577 15H6.45768C6.11251 15 5.83268 14.7202 5.83268 14.375Z"
        fill={color}
      />
    </svg>
  );
};
