import { SVGProps } from 'react';
import { colors } from '../../constants/colors';

export const GeneralPrevIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 24}
      height={props?.height ? props?.height : 24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.9973 4.28869C16.4927 4.70802 16.5373 5.43039 16.097 5.90214L10.4056 12L16.097 18.0979C16.5373 18.5696 16.4927 19.292 15.9973 19.7113C15.502 20.1306 14.7435 20.0882 14.3032 19.6164L7.90321 12.7593C7.49906 12.3263 7.49906 11.6737 7.90321 11.2407L14.3032 4.3836C14.7435 3.91184 15.502 3.86935 15.9973 4.28869Z"
        fill={props?.color ? props?.color : colors.dark[500]}
      />
    </svg>
  );
};
