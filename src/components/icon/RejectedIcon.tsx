import { SVGProps } from 'react';

export const RejectedIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 16}
      height={props?.height ? props?.height : 16}
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <circle cx="8.625" cy="8.5" r="7.25" fill="white" stroke="white" strokeWidth="0.5" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.625 15.5C12.491 15.5 15.625 12.366 15.625 8.5C15.625 4.63401 12.491 1.5 8.625 1.5C4.75901 1.5 1.625 4.63401 1.625 8.5C1.625 12.366 4.75901 15.5 8.625 15.5ZM10.6049 5.53015C10.8783 5.25678 11.3215 5.25678 11.5948 5.53015C11.8682 5.80352 11.8682 6.24673 11.5948 6.5201L9.61495 8.5L11.5948 10.4799C11.8682 10.7533 11.8682 11.1965 11.5948 11.4698C11.3215 11.7432 10.8783 11.7432 10.6049 11.4698L8.625 9.48995L6.6451 11.4698C6.37173 11.7432 5.92852 11.7432 5.65515 11.4698C5.38178 11.1965 5.38178 10.7533 5.65515 10.4799L7.63505 8.5L5.65515 6.5201C5.38178 6.24673 5.38178 5.80352 5.65515 5.53015C5.92852 5.25678 6.37173 5.25678 6.6451 5.53015L8.625 7.51005L10.6049 5.53015Z"
        fill="#E3173C"
      />
    </svg>
  );
};
