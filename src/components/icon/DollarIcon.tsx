import { SVGProps } from "react";

/**
 * @component @name DollarIcon
 *
 * @preview ![img](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAD5SURBVHgBdVKBEYIwDGxdQNygG+gIOIE4gecEsAFs4DkBbiBMIE4gTgBu4Ab4fwQsOcjdHyX5fptvrFHRdV2IzwFwQADUQGmtrXye9TaQmMtvCbTAF9gBsayPEGj9UxzQAIlZCNQy4Tg/+WJBESkWqVwCPMaeqDRzQk7S5IQ+z1y4wpqqV1W8SG8fr2/j9R9x49b0zvmxMb05BczYqxrzjhvpVqCKGfAETmNP/yB/vZLrOFXM5SZzomzhPZgzURVHb0AFBKrWyJCMTiUzm0OVS/l0mkSl1CwEarE/AHrk7tITn6eWNfMHoZ2HkbMzyqHp33Ywh+YVesh/X4K6tDDY1C8AAAAASUVORK5CYII=)
 *
 * @param {Object} props - size, with, height
 * @returns {JSX.Element} JSX Element
 *
 */
export const DollarIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 20}
      height={props?.height ? props?.height : 20}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_2844_50398)">
        <path
          d="M19.6064 5.72451C20.1312 8.53906 20.1312 11.4013 19.6064 14.2636C19.1294 16.9827 16.9827 19.1294 14.2636 19.6064C11.449 20.1312 8.58676 20.1312 5.72451 19.6064C3.00537 19.1294 0.90638 17.0304 0.429338 14.3113C-0.143113 11.4967 -0.143113 8.58676 0.429338 5.72451C0.90638 3.00537 3.00537 0.90638 5.72451 0.429338C8.53906 -0.143113 11.449 -0.143113 14.3113 0.429338C17.0304 0.90638 19.1294 3.00537 19.6064 5.72451Z"
          fill="#F0F5FE"
          fillOpacity="0.12"
        />
        <circle cx="10" cy="10" r="6.25" stroke="white" />
        <path
          d="M11.3679 8.50889C11.3679 8.50889 10.8538 7.93579 10.0199 8.00741C9.18605 8.07903 8.7841 8.59133 8.7841 9.10503C8.7841 10.6033 11.3679 9.56499 11.3679 11.1014C11.3679 11.8935 9.65396 12.4526 8.63281 11.5208"
          stroke="white"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path d="M10.1221 7L10.1221 8.00132" stroke="white" strokeLinecap="round" />
        <path d="M10.1221 11.9988L10.1221 13.0001" stroke="white" strokeLinecap="round" />
      </g>
      <defs>
        <clipPath id="clip0_2844_50398">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
