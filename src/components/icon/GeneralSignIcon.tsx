import { SVGProps } from 'react';
import { colors } from '../../constants/colors';

export const GeneralSignIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    
    <svg
      {...props}
      width={props?.width ? props?.width : 12}
      height={props?.height ? props?.height : 12}
      viewBox="0 0 10 11"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.12596 3.87575C8.12596 5.55972 6.76083 6.92485 5.07686 6.92485C3.3929 6.92485 2.02777 5.55972 2.02777 3.87575C2.02777 2.19179 3.3929 0.82666 5.07686 0.82666C6.76083 0.82666 8.12596 2.19179 8.12596 3.87575ZM5.07686 6.11887C6.3157 6.11887 7.31998 5.11459 7.31998 3.87575C7.31998 2.63692 6.3157 1.63264 5.07686 1.63264C3.83803 1.63264 2.83375 2.63692 2.83375 3.87575C2.83375 5.11459 3.83803 6.11887 5.07686 6.11887Z"
        fill={props?.color ? props?.color : colors.white}
      />
      <path
        d="M5.09259 3.97363L5.09305 3.97373C5.21013 4.00042 5.28918 4.03198 5.33847 4.07021C5.38571 4.10682 5.40562 4.14931 5.40562 4.20373C5.40562 4.2731 5.37538 4.33097 5.32001 4.37261C5.26387 4.41481 5.18105 4.44072 5.07801 4.44072C4.94056 4.44072 4.83443 4.40054 4.77025 4.33597C4.76633 4.33204 4.76234 4.32801 4.75832 4.32394C4.72513 4.2904 4.68811 4.25298 4.64792 4.22326C4.60249 4.18965 4.55004 4.16351 4.48995 4.16351C4.4232 4.16351 4.36105 4.19345 4.32188 4.24118C4.28203 4.28975 4.26611 4.3572 4.29535 4.42573C4.38446 4.63476 4.58117 4.77341 4.83056 4.83079V5.00045C4.83056 5.12324 4.92608 5.22178 5.04271 5.22178H5.07032C5.18694 5.22178 5.28246 5.12324 5.28246 5.00045V4.82318C5.62344 4.74542 5.86805 4.50018 5.87544 4.14284V4.14212C5.87544 3.97386 5.83044 3.83784 5.73212 3.73326C5.63479 3.62968 5.48841 3.56044 5.29216 3.51747C5.29209 3.51746 5.29206 3.51745 5.29203 3.51744L5.05599 3.46365C5.05596 3.46364 5.05593 3.46363 5.0559 3.46362C4.94209 3.43687 4.86679 3.40721 4.82025 3.37101C4.77617 3.3367 4.75755 3.29637 4.75755 3.2413C4.75755 3.17199 4.78777 3.11602 4.8405 3.07627C4.89403 3.03592 4.97151 3.01203 5.06366 3.01203C5.1759 3.01203 5.26752 3.05324 5.32516 3.11721C5.3594 3.15521 5.39866 3.1979 5.44274 3.2309C5.48706 3.26408 5.53854 3.28921 5.59737 3.28921C5.66326 3.28921 5.72446 3.25955 5.76258 3.21234C5.80148 3.16417 5.81608 3.09745 5.78565 3.03038C5.69666 2.83427 5.50847 2.6933 5.28246 2.63072V2.45228C5.28246 2.32948 5.18694 2.23096 5.07032 2.23096H5.04271C4.92608 2.23096 4.83056 2.32948 4.83056 2.45228V2.6383C4.5182 2.72326 4.28774 2.96022 4.28774 3.2875C4.28774 3.45232 4.33473 3.58973 4.42961 3.69581C4.5239 3.8012 4.66289 3.87275 4.84213 3.91212L5.09259 3.97363Z"
        fill={props?.color ? props?.color : colors.white}
        stroke="#99A5B2"
        strokeWidth="0.074504"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.50957 7.73329L2.32421 7.2694C2.57513 7.12651 2.86697 7.07259 3.15238 7.11638L5.24434 7.43737C5.47283 7.47243 5.67691 7.57346 5.83839 7.71928L7.86442 7.19888C9.04562 6.83824 9.87291 8.31305 8.92917 9.1097L8.90575 9.12947L6.85017 10.3649C6.60695 10.5404 6.31463 10.6348 6.0147 10.6348H3.5979L3.30372 10.7997C2.7939 11.0854 2.14947 10.9187 1.84224 10.4215L1.12134 9.25486C0.79654 8.72924 0.972646 8.03904 1.50957 7.73329ZM5.95049 8.61815H5.42026C5.42026 8.45013 5.28818 8.25951 5.1221 8.23402L3.03015 7.91304C2.92431 7.8968 2.81609 7.91679 2.72304 7.96978L1.9084 8.43367C1.76813 8.51355 1.72212 8.69386 1.80698 8.83118L2.52788 9.9978C2.60814 10.1277 2.77649 10.1712 2.90968 10.0966L3.32253 9.86523C3.36509 9.84138 3.41307 9.82885 3.46187 9.82885H6.0147C6.14532 9.82885 6.27262 9.78771 6.37854 9.71128L8.40927 8.49382C8.68323 8.26256 8.44265 7.86505 8.09977 7.96973L5.95049 8.61815Z"
        fill={props?.color ? props?.color : colors.white}
      />
    </svg>
  );
};
