import { SVGProps } from 'react';
import { colors } from '../../constants/colors';

export const BillIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width={props?.width ? props?.width : 28}
      height={props?.height ? props?.height : 28}
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.33333 2.88892H20.6667C22.5076 2.88892 24 4.3813 24 6.22225V23.8438C24 24.007 23.9283 24.1619 23.8039 24.2674L21.8594 25.9173C21.6521 26.0932 21.3479 26.0932 21.1406 25.9173L19.3594 24.406C19.1521 24.2301 18.8479 24.2301 18.6406 24.406L16.8594 25.9173C16.6521 26.0932 16.3479 26.0932 16.1406 25.9173L14.3594 24.406C14.1521 24.2301 13.8479 24.2301 13.6406 24.406L11.8594 25.9173C11.6521 26.0932 11.3479 26.0932 11.1406 25.9173L9.35943 24.406C9.1521 24.2301 8.8479 24.2301 8.64057 24.406L6.85943 25.9173C6.6521 26.0932 6.3479 26.0932 6.14057 25.9173L4.19612 24.2674C4.07172 24.1619 4 24.007 4 23.8438V6.22225C4 4.38131 5.49238 2.88892 7.33333 2.88892ZM8.44444 7.33336C7.8308 7.33336 7.33333 7.83082 7.33333 8.44447C7.33333 9.05812 7.83079 9.55558 8.44444 9.55558H18.4444C19.0581 9.55558 19.5556 9.05812 19.5556 8.44447C19.5556 7.83082 19.0581 7.33336 18.4444 7.33336H8.44444ZM7.33333 12.8889C7.33333 12.2753 7.8308 11.7778 8.44444 11.7778H14C14.6137 11.7778 15.1111 12.2753 15.1111 12.8889C15.1111 13.5026 14.6136 14 14 14H8.44444C7.8308 14 7.33333 13.5026 7.33333 12.8889ZM8.44444 16.2222C7.8308 16.2222 7.33333 16.7197 7.33333 17.3334C7.33333 17.947 7.8308 18.4445 8.44444 18.4445H16.2222C16.8359 18.4445 17.3333 17.947 17.3333 17.3334C17.3333 16.7197 16.8359 16.2222 16.2222 16.2222H8.44444Z"
        fill={props.color ? props.color : colors.white}
      />
    </svg>
  );
};
