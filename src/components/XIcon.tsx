import { SVGProps } from "react";
import { colors } from "../constants/colors";

export const XIcon = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => {
    return (
      <svg {...props} width={props?.width ? props?.width : 24} height={props?.height ? props?.height : 24} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.6653 4.33995C19.219 3.88668 18.4953 3.88668 18.049 4.33995L12 10.4833L5.95098 4.33995C5.50467 3.88668 4.78105 3.88668 4.33474 4.33995C3.88842 4.79322 3.88842 5.52812 4.33473 5.98139L10.3838 12.1247L4.58032 18.0186C4.13401 18.4719 4.13401 19.2068 4.58032 19.66C5.02664 20.1133 5.75025 20.1133 6.19657 19.66L12 13.7661L17.8034 19.66C18.2497 20.1133 18.9734 20.1133 19.4197 19.66C19.866 19.2068 19.866 18.4719 19.4197 18.0186L13.6162 12.1247L19.6653 5.98139C20.1116 5.52812 20.1116 4.79322 19.6653 4.33995Z"
        fill={colors.dark[500]}
      />
    </svg>
    );
  }