import { Visually<PERSON><PERSON><PERSON> } from "@radix-ui/react-visually-hidden";
import { Drawer, DrawerClose, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle } from "./ui/Drawer";
import { XIcon } from "./XIcon";
import { Button } from "./ui/Button";
import { memo, ReactNode } from "react";

export const BottomSheet = ({
  title,
  children,
  ...props
}: React.ComponentProps<typeof Drawer> & { title?: string; children?: ReactNode }) => {
  return (
    <Drawer {...props}>
      <DrawerContent>
        <DrawerHeader className="w-full gap-0 p-0 bg-white rounded-t-lg">
          <div className="relative flex items-center justify-center w-full h-12 p-4">
            {title ? <DrawerTitle>{title}</DrawerTitle> : <DrawerTitle className="sr-only">title</DrawerTitle>}
            <DrawerDescription className="sr-only">description</DrawerDescription>
            <DrawerClose
              className="absolute transition-transform duration-300 -translate-y-1/2 top-1/2 right-4 active:scale-95"
              asChild>
              <Button variant="ghost" className="p-0 rounded-full h-fit">
                <XIcon className="w-6 h-6 text-gray-500" strokeWidth={3} />
              </Button>
            </DrawerClose>
          </div>
        </DrawerHeader>
        {children}
      </DrawerContent>
    </Drawer>
  );
};

export default memo(BottomSheet);
