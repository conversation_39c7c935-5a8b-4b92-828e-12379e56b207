import { images } from "@/res";
import { Button } from "@/components/ui/Button";
import { useEffect } from "react";
import { cn } from "@/lib/utils";
import { closeWindow } from "@/lib/ZalopaySDK/closeWindow";
import {launchDeeplink} from "@/lib/ZalopaySDK/launchDeeplink";

interface NonWhitelistUIProps {
  className?: string;
  onClose?: () => void;
}

export const NonWhitelistUI = (props: NonWhitelistUIProps) => {
  const handleOnClose = () => {
    if (props.onClose) {
      props.onClose();
      return;
    }
    closeWindow();
  };

  const handleOpenOAOHub = () =>{
    launchDeeplink({
      zpi: '/bank-oao?from-source=installment',
      zpa: 'zalopay://launch/f/bank-routing?entrypoint=OAO&tracking_source=installment',
    })
  }

  useEffect(() => {
    return () => {
      props.onClose?.();
    };
  }, []);

  return (
    <>
      <div className={cn("w-full p-4 flex flex-col gap-6 items-center h-screen justify-center", props?.className)}>
        <img loading="lazy" src={images.ImgNotice} className="w-[180px] h-[180px]" />
        <div className="text-center">
          <h2 className="text-lead font-bold mb-2">Zalopay chưa thể cung cấp dịch vụ Trả góp cho bạn</h2>
          <p className="text-base text-dark-300">
            Zalopay sẽ gửi thông báo cho bạn ngay khi bạn thoả điều kiện để tiếp cận dịch vụ.
          </p>
        </div>
        <Button onClick={handleOpenOAOHub} variant={"primary"} size="lg" className="w-full">
          Khám phá kho tài chính
        </Button>
        <Button onClick={handleOnClose} variant={"outlined"} size="lg" className="w-full">
          Về trang chủ
        </Button>
      </div>
    </>
  );
};
