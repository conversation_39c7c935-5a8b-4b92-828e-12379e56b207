import { format } from "date-fns";
import isNumber from "lodash.isnumber";
import { ID_TYPE } from "../screens/OnboardingScreen/constant";
import { ProfileInfo, IdType } from "../types/profileInfo";
import { renderStringValue } from "../utils/renderStringValue";
import { OnboardingInfo } from "../api/getOnboardingInfo";
import { Gender } from "../types";

export const FullInfoUser = ({
  profileInfo,
  includeIdType = true,
}: {
  profileInfo: ProfileInfo | OnboardingInfo | undefined;
  includeIdType?: boolean;
}) => {
  
  const renderGender = (gender: string): string => {
    const formattedGender = Gender[gender.toUpperCase() as keyof typeof Gender];
    return formattedGender || "-";
  };

  return (
    <ul className="flex flex-col py-2 bg-white rounded-lg text-lead">
      <li className="shortScreen:py-2.5 p-4 flex justify-between">
        <p className="w-1/3">Họ và tên</p>
        <p className="flex-1 text-right">{renderStringValue(profileInfo?.full_name)}</p>
      </li>
      <div className="w-full px-4 ">
        <div className="w-full h-px bg-dark-50" />
      </div>
      <li className="shortScreen:py-2.5 p-4 flex justify-between">
        <p className="w-1/3">Ngày sinh</p>
        <p className="flex-1 text-right">
          {profileInfo?.date_of_birth ? format(new Date(profileInfo?.date_of_birth), "dd/MM/yyyy") : "-"}
        </p>
      </li>
      <div className="w-full px-4 ">
        <div className="w-full h-px bg-dark-50" />
      </div>
      <li className="shortScreen:py-2.5 p-4 flex justify-between">
        <p className="w-1/3">Số điện thoại</p>
        <p className="flex-1 text-right">{renderStringValue(profileInfo?.phone_number)}</p>
      </li>
      <div className="w-full px-4 ">
        <div className="w-full h-px bg-dark-50" />
      </div>
      {includeIdType ? (
        <li className="shortScreen:py-2.5 p-4 flex justify-between">
          <p className="w-1/3">Loại giấy tờ</p>
          <p className="flex-1 text-right">
            {profileInfo?.id_type && isNumber(profileInfo?.id_type)
              ? ID_TYPE[profileInfo?.id_type as IdType]
              : "-"}
          </p>
        </li>
      ) : null}
      <div className="w-full px-4 ">
        <div className="w-full h-px bg-dark-50" />
      </div>
      <li className="shortScreen:py-2.5 p-4 flex justify-between">
        <p className="w-1/3">Số giấy tờ</p>
        <p className="flex-1 text-right">{renderStringValue(profileInfo?.id_number)}</p>
      </li>
      <div className="w-full px-4 ">
        <div className="w-full h-px bg-dark-50" />
      </div>
      <li className="shortScreen:py-2.5 p-4 flex justify-between">
        <p className="w-1/3">Ngày cấp</p>
        <p className="flex-1 text-right">
          {profileInfo?.id_issue_date ? format(new Date(profileInfo?.id_issue_date), "dd/MM/yyyy") : "-"}
        </p>
      </li>
      <div className="w-full px-4 ">
        <div className="w-full h-px bg-dark-50" />
      </div>
      <li className="shortScreen:py-2.5 p-4 flex justify-between">
        <p className="w-1/3">Nơi cấp</p>
        <p className="flex-1 text-right">{renderStringValue(profileInfo?.id_issue_place)}</p>
      </li>
      <div className="w-full px-4 ">
        <div className="w-full h-px bg-dark-50" />
      </div>
      <li className="shortScreen:py-2.5 p-4 flex justify-between">
        <p className="w-1/3">Giới tính</p>
        <p className="flex-1 text-right">{profileInfo?.gender ? renderGender(profileInfo.gender) : "-"}</p>
      </li>
      <div className="w-full px-4 ">
        <div className="w-full h-px bg-dark-50" />
      </div>
      <li className="shortScreen:py-2.5 p-4 flex justify-between">
        <p className="w-1/3">Địa chỉ</p>
        <p className="flex-1 text-right">{renderStringValue(profileInfo?.permanent_address)}</p>
      </li>
    </ul>
  );
};
