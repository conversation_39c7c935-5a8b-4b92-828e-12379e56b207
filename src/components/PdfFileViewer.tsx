import React, { FC, useEffect } from "react";
import { PDFDocumentProxy } from "pdfjs-dist/types/src/display/api";
import { pdfViewerUrl } from "@/constants";
import { getUserAgentInfo } from "@/lib/ZalopaySDK/getUserAgentInfo";

const pdfjs = require("pdfjs-dist");
pdfjs.GlobalWorkerOptions.workerSrc =
  "https://simg.zalopay.com.vn/fs/cash-loan/document/pdfjs-dist@3.9.179_pdf.worker.min.js";

export const LegacyPdfFileViewer: FC<{
  url: string;
  width?: number | string;
  height?: number | string;
  options?: { headers?: any; password?: string; withCredentials?: boolean };
}> = ({ url: fileUrl, width, height, options }) => {
  const [pdfResource, setPdfResource] = React.useState<PDFDocumentProxy>();

  useEffect(() => {
    if (fileUrl) {
      const documentLoadTask = pdfjs.getDocument({
        url: fileUrl,
        password: options?.password,
        httpHeaders: options?.headers,
        withCredentials: !!options?.withCredentials,
      });
      documentLoadTask.promise
        .then((pdf: any) => {
          setPdfResource(pdf);
        })
        .catch((e: any) => {
          console.error("Error loading pdf", e);
        });
    }
  }, [fileUrl]);

  const renderPages = (pdfResource?: PDFDocumentProxy) => {
    if (!pdfResource || !pdfResource.numPages) {
      return null;
    }
    return Array(pdfResource.numPages)
      .fill(0)
      .map((v, i) => <PdfPage key={i} pdf={pdfResource} index={i + 1} />);
  };

  return (
    <div id={"pdf-viewer"} style={{ width, height }}>
      {renderPages(pdfResource)}
    </div>
  );
};

export const PdfFileViewer = ({
  url,
  height,
  withCredentials,
  onScroll,
  onMessage,
}: {
  url: string;
  onMessage?: (event: MessageEvent<any>) => void;
  withCredentials?: boolean;
  height?: number | string;
  onScroll?: (element: HTMLElement) => void;
}) => {
  const userAgentInfo = getUserAgentInfo();
  const osMajorVer = parseFloat(userAgentInfo?.os?.version!);
  const renderLegacyViewer = userAgentInfo?.os?.name === "iOS" && osMajorVer < 17;

  const loadContractUrl = (url: string) => {
    const pdfViewer = document.getElementById("pdf-viewer") as HTMLIFrameElement;
    const contractUrl = `${pdfViewerUrl}?file=${encodeURIComponent(url)}&with_credentials=${!!withCredentials}`;
    if (!pdfViewer) return;
    pdfViewer.src = contractUrl;
    pdfViewer.contentWindow?.location.replace(contractUrl);
  };

  const handleOnScroll = (_: any) => {
    const pdfViewer = document.getElementById("pdf-viewer");
    if (pdfViewer) onScroll?.(pdfViewer);
  };

  useEffect(() => {
    if (!renderLegacyViewer) {
      loadContractUrl(url);
    }
    onMessage && window.addEventListener("message", onMessage);
    window.addEventListener("scroll", handleOnScroll);
    return () => {
      onMessage && window.removeEventListener("message", onMessage);
      window.removeEventListener("scroll", handleOnScroll);
    };
  }, []);

  return renderLegacyViewer ? (
    <LegacyPdfFileViewer url={url} width="100%" height={height} options={{ withCredentials }} />
  ) : (
    <iframe id="pdf-viewer" width="100%" height={height} style={{ minHeight: height }} />
  );
};

const PdfPage: FC<{ pdf: PDFDocumentProxy; index: number; customScale?: number }> = ({ index, pdf, customScale }) => {
  useEffect(() => {
    if (pdf) {
      pdf
        .getPage(index)
        .then((page: any) => {
          const desiredWidth = document.documentElement.clientWidth;
          const viewport = page.getViewport({ scale: customScale || 1 });
          const scaleRaito = viewport.width / desiredWidth;
          const scaledViewport = page.getViewport({ scale: scaleRaito });
          const canvas = document.getElementById(`pdf-page-${index}`) as any;
          const context = canvas.getContext("2d");

          canvas.width = Math.floor(scaledViewport.width);
          canvas.height = Math.floor(scaledViewport.height);

          const renderContext = {
            canvasContext: context,
            viewport: scaledViewport,
          };
          page.render(renderContext);
        })
        .catch((e: any) => {
          console.log("Error rendering page", e);
        });
    }
  }, [pdf, index]);

  return <canvas id={`pdf-page-${index}`} className="!w-full block" />;
};
