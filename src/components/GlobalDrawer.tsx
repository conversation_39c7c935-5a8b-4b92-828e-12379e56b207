import React, { useCallback, useEffect, useLayoutEffect, useState } from "react";

import { Button } from "@/components/ui/Button";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/Drawer";
import { cn } from "@/lib/utils";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";

import { XIcon } from "./XIcon";

type DrawerProps = {
  children: React.ReactNode;
  title?: string;
  contentClassName?: string;
  headless?: boolean;
  onClickClose?: () => void;
};

let openDrawerFn: ((props: DrawerProps & React.ComponentProps<typeof Drawer>) => void) | null = null;
let closeDrawerFn: (() => void) | null = null;
let closeAllDrawerFn: (() => void) | null = null;
let isDrawerOpen: boolean | null = null;

export const GlobalDrawer = {
  open: (props: DrawerProps & React.ComponentProps<typeof Drawer>) => {
    openDrawerFn?.(props);
  },
  close: () => closeDrawerFn?.(),
  closeAll: () => closeAllDrawerFn?.(),
};

type DrawerItem = DrawerProps & React.ComponentProps<typeof Drawer>;

export const GlobalDrawerProvider: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [drawerStack, setDrawerStack] = useState<DrawerItem[]>([]);

  const openDrawer = useCallback((props: DrawerItem) => {
    setDrawerStack((prev) => [...prev, props]);
    setIsOpen(true);
  }, []);

  const closeDrawer = useCallback(() => {
    setDrawerStack((prev) => {
      const newStack = [...prev];
      newStack.pop();
      if (newStack.length === 0) {
        setIsOpen(false);
      }
      return newStack;
    });
  }, []);

  const closeAllDrawer = useCallback(() => {
    setDrawerStack([]);
    setIsOpen(false);
  }, []);

  const handleSetOpen = (open: boolean) => {
    if (!open) {
      setDrawerStack([]);
      setIsOpen(false);
    }
  };

  useLayoutEffect(() => {
    document.addEventListener("focusin", (e) => e.stopImmediatePropagation());
    document.addEventListener("focusout", (e) => e.stopImmediatePropagation());
  }, []);

  useEffect(() => {
    openDrawerFn = openDrawer;
    closeDrawerFn = closeDrawer;
    closeAllDrawerFn = closeAllDrawer;
    isDrawerOpen = isOpen;
    return () => {
      openDrawerFn = null;
      closeDrawerFn = null;
      isDrawerOpen = null;
    };
  }, [openDrawer, closeDrawer, isDrawerOpen]);

  const currentDrawer = drawerStack[0];
  const nestedDrawers = drawerStack.slice(1);

  return (
    <>
      <Drawer open={isOpen} onOpenChange={handleSetOpen} {...currentDrawer}>
        <DrawerContent className={cn("mx-auto outline-none max-h-[90%]", currentDrawer?.contentClassName)}>
          {currentDrawer?.headless ? (
            //for screen reader
            <VisuallyHidden>
              <DrawerTitle></DrawerTitle>
              <DrawerDescription></DrawerDescription>
            </VisuallyHidden>
          ) : (
            <DrawerHeader className={cn("w-full p-0 gap-0 rounded-t-lg", currentDrawer?.title ? "bg-white" : "")}>
              {!currentDrawer?.dismissible ? (
                <div className="flex-shrink-0 mx-auto mt-2 h-1.5 w-10 rounded-full bg-dark-50" />
              ) : null}
              <div className="relative flex items-center justify-center w-full h-12 p-4">
                {currentDrawer?.title ? (
                  <DrawerTitle className="font-bold text-lead">{currentDrawer?.title || ""}</DrawerTitle>
                ) : (
                  //for screen reader
                  <VisuallyHidden>
                    <DrawerTitle></DrawerTitle>
                  </VisuallyHidden>
                )}
                <VisuallyHidden>
                  <DrawerDescription></DrawerDescription>
                </VisuallyHidden>
                <DrawerClose
                  className={cn(
                    "absolute top-1/2 -translate-y-1/2 right-4 active:scale-95 transition-transform duration-300"
                  )}
                  onClick={() => currentDrawer?.onClickClose?.()}
                  asChild>
                  <Button variant="ghost" className="p-0 rounded-full h-fit">
                    <XIcon className="w-6 h-6 text-gray-500" strokeWidth={3} />
                  </Button>
                </DrawerClose>
              </div>
            </DrawerHeader>
          )}

          {currentDrawer?.children}

          {Boolean(nestedDrawers) &&
            nestedDrawers.map((nestedDrawer, index) => {
              const { children = null, title = "", headless = false, ...nestedDrawerProps } = nestedDrawer;

              return (
                <Drawer
                  nested
                  key={index}
                  open={Boolean(nestedDrawers[index])}
                  onOpenChange={(open) => !open && closeDrawer()}
                  {...nestedDrawerProps}>
                  <DrawerContent
                    className={cn("mx-auto outline-none max-h-[90%] border-none", nestedDrawerProps?.contentClassName)}>
                    {headless ? (
                      <VisuallyHidden>
                        <DrawerTitle></DrawerTitle>
                        <DrawerDescription></DrawerDescription>
                      </VisuallyHidden>
                    ) : (
                      <DrawerHeader className={cn("w-full p-0 gap-0 rounded-t-lg", title ? "bg-white" : "")}>
                        {!nestedDrawerProps?.dismissible ? (
                          <div className="flex-shrink-0 mx-auto mt-2 h-1.5 w-10 rounded-full bg-dark-50" />
                        ) : null}
                        <div className="relative flex items-center justify-center w-full h-12 p-4">
                          {title ? (
                            <DrawerTitle className="font-bold text-lead">{title}</DrawerTitle>
                          ) : (
                            <VisuallyHidden>
                              <DrawerTitle></DrawerTitle>
                            </VisuallyHidden>
                          )}
                          <DrawerClose
                            className={cn(
                              "absolute top-1/2 -translate-y-1/2 right-4 active:scale-95 transition-transform duration-300"
                            )}
                            onClick={() => {
                              nestedDrawer?.onClickClose?.();
                            }}
                            asChild>
                            <Button variant="ghost" className="p-0 rounded-full h-fit">
                              <XIcon className="w-6 h-6 text-gray-500" strokeWidth={3} />
                            </Button>
                          </DrawerClose>
                        </div>
                      </DrawerHeader>
                    )}
                    {children}
                  </DrawerContent>
                </Drawer>
              );
            })}
        </DrawerContent>
      </Drawer>
    </>
  );
};
