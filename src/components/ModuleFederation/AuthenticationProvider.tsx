import { loadRemote } from "@/utils/loadRemote";
import { useState, useEffect } from "react";
import withErrorBoundary from "@/hocs/withErrorBoundary";
import { withImportFederatedModule } from "@/hocs/withImportFederatedModule";
import { getAuthenicationApp } from "@/api/getAuthenicationApp";

const AuthenticationProvider = (props: any) => {
  const [MFComponent, setMFComponent] = useState<React.ComponentType<any> | null>(null);
  const [isEmpty, setIsEmpty] = useState(false);

  useEffect(() => {
    const fetchRemoteComponent = async () => {
      try {
        const remoteModule = await loadRemote(
          getAuthenicationApp(), // Remote URL
          "authentication_challenge_app", // Remote container name
          "./AuthenticationContext" // Module name (remote component)
        );

        // Assuming RecommendView is a default export
        const Component = remoteModule.AuthenticationProvider;
        setMFComponent(() => Component); // Set the remote component for rendering
      } catch (error) {
        setIsEmpty?.(true);
        console.error("Error loading remote component:", error);
      }
    };
    fetchRemoteComponent();
  }, []);
  
  return <>{MFComponent ? <MFComponent {...props} /> : null}</>;
};

export default withErrorBoundary(withImportFederatedModule(AuthenticationProvider));
