import { ProductPageSliderProps } from "@/types/promotion";
import { loadRemote } from "@/utils/loadRemote";
import { useState, useEffect, useRef } from "react";
import withErrorBoundary from "@/hocs/withErrorBoundary";
import { withImportFederatedModule } from "@/hocs/withImportFederatedModule";
import { createRemoteComponent } from "@zpi/federation-adapter";

const FinButtonSOFButton = (props: any) => {
  const MFComponent = useRef<React.ComponentType<any> | null>(null);
  const [isEmpty, setIsEmpty] = useState(true);

  useEffect(() => {
    const fetchRemoteComponent = async () => {
      try {
        // MFComponent.current = createRemoteComponent({
        //   loader: async () =>
        //     await loadRemote(
        //       "https://socialdev.zalopay.vn/mfpublic/share-module/micro-app/bd211552-0808-4858-b5e1-fee2f9c677f7", // Remote URL
        //       "fin_sdk", // Remote container name
        //       "./FinSuggestSOFButton" // Module name (remote component)
        //     ),
        //   export: "default",
        //   fallback: null,
        //   loading: null,
        // });
        const remoteModule = await loadRemote(
          "https://sjs.zalopay.com.vn/zst/spa/v2/micro-apps/fin_sdk/1.0.3/remoteEntry.js", // Remote URL
          // "http://localhost:3001/remoteEntry.js",
          "fin_sdk", // Remote container name
          "./FinSuggestSOFButton" // Module name (remote component)
        )
        MFComponent.current = remoteModule.default;
        console.log("remoteModule", remoteModule);

        setIsEmpty(false);
      } catch (error) {
        console.error("Error loading remote component:", error);
      }
    };
    fetchRemoteComponent();
  }, []);

  if (isEmpty || !MFComponent.current) return null;
  const Comp = MFComponent.current;
  return <>{Comp ? <Comp {...props} /> : null}</>;
};

export default withErrorBoundary(withImportFederatedModule<ProductPageSliderProps>(FinButtonSOFButton));
