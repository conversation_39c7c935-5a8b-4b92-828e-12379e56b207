import { loadRemote } from "@/utils/loadRemote";
import { useState, useEffect, useRef, memo } from "react";
import withErrorBoundary from "@/hocs/withErrorBoundary";
import { withImportFederatedModule } from "@/hocs/withImportFederatedModule";
import { getAuthenicationApp } from "@/api/getAuthenicationApp";
import { createRemoteComponent } from "@zpi/federation-adapter";

const R18AuthenticationContainer = (props: any) => {
  const MFComponent = useRef<React.ComponentType<any> | null>(null);
    const [isEmpty, setIsEmpty] = useState(true);
  
    useEffect(() => {
      const fetchRemoteComponent = async () => {
        try {
          MFComponent.current = createRemoteComponent({
            loader: async () =>
              await loadRemote(
                getAuthenicationApp(), // Remote URL
                "authentication_challenge_app", // Remote container name
                "./AuthenticationContainer" // Module name (remote component)
              ),
            export: "R18AuthenticationContainer",
            fallback: null,
            loading: null,
          });
          setIsEmpty(false);
        } catch (error) {
          console.error("Error loading remote component:", error);
        }
      };
      fetchRemoteComponent();
    }, []);
  
    if (isEmpty || !MFComponent.current) return null;
  
    const Comp = MFComponent.current;
    return <>{Comp ? <Comp {...props} /> : null}</>;
};

export default withErrorBoundary(withImportFederatedModule(memo(R18AuthenticationContainer)));
