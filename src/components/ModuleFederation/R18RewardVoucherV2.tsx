import { ProductPageSliderProps } from "@/types/promotion";
import { loadRemote } from "@/utils/loadRemote";
import { useState, useEffect, useRef } from "react";
import withErrorBoundary from "@/hocs/withErrorBoundary";
import { withImportFederatedModule } from "@/hocs/withImportFederatedModule";
import { createRemoteComponent } from "@zpi/federation-adapter";
import { getAdvertisingGatewayAppURL } from "@/api/getAdvertisingGatewayAppURL";

const R18RewardVoucherV2 = (props: any) => {
  const MFComponent = useRef<React.ComponentType<any> | null>(null);
  const [isEmpty, setIsEmpty] = useState(true);

  useEffect(() => {
    const fetchRemoteComponent = async () => {
      try {
        MFComponent.current = createRemoteComponent({
          loader: async () =>
            await loadRemote(
              getAdvertisingGatewayAppURL() as string, // Remote URL
              "advertising_gateway_app", // Remote container name
              "./R18RewardVoucherV2" // Module name (remote component)
            ),
          export: "default",
          fallback: null,
          loading: null,
        });
        setIsEmpty(false);
      } catch (error) {
        console.error("Error loading remote component:", error);
      }
    };
    fetchRemoteComponent();
  }, []);

  if (isEmpty || !MFComponent.current) return null;
  const Comp = MFComponent.current;
  return <>{Comp ? <Comp {...props} /> : null}</>;
};

export default withErrorBoundary(withImportFederatedModule<ProductPageSliderProps>(R18RewardVoucherV2));
