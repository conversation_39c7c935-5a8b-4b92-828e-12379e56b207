import { useMemo } from "react";
import loadable from "@loadable/component";
import { loadRemoteComponent } from "./loader";
import { RemoteComponentType, TRemoteComponent } from "./types";

/**
 * @component @name RemoteComponent
 *
 * @preview ##
 *
 * @param {TRemoteComponent} props -
 * ex:
 *  module: "./InstallmentPlan";
 *  props: '{"amount":500000,"appID":61,"appTransID":"240626000000115"}';
 *  remote_entry: "https://socialdev.zalopay.vn/mfpublic/share-module/micro-app/947ecaf8-eb9c-4d38-b64b-10db0cf6f4a3";
 *  scope: "installment_external_app";
 * @returns {JSX.Element} JSX Element
 *
 */



type RemoteComponentProps = {
  remoteModule: TRemoteComponent;
};

function RemoteComponent(props: RemoteComponentProps) {
  const { remoteModule } = props;
  const { scope, module, remote_entry } = remoteModule;

  if (!module || !scope || !remote_entry) return null;

  const Lazy = useMemo(() => {
    const m: RemoteComponentType = {
      scope,
      module,
      remoteEntry: remote_entry,
      props,
    };
    return loadable(() => loadRemoteComponent(m));
  }, [scope, module, remote_entry]);

  const propsData = { ...remoteModule.props };

  return <Lazy {...propsData} />;
}

export default RemoteComponent;
