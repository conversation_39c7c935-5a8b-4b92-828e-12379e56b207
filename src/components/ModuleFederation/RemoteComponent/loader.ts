import { FunctionComponent } from 'react';
import { RemoteComponentType } from './types';

declare const __webpack_share_scopes__: Record<
    string,
    Record<string, { loaded?: 1; get: () => Promise<unknown>; from: string; eager: boolean }>
>;

declare let __webpack_init_sharing__: (scope: string) => Promise<void>;
declare const __webpack_require__: (id: string | number) => unknown;

export type WebpackShareScopes = Record<
    string,
    Record<string, { loaded?: 1; get: () => Promise<unknown>; from: string; eager: boolean }>
> & {
    default?: string;
};

export type WebpackRequire = {
    l: (
        url: string | undefined,
        cb: (event: any) => void,
        id: string | number,
    ) => Record<string, unknown>;
};

export type ModulePath = string;

type Module = {
    get: (module: ModulePath) => Promise<() => { default: React.FunctionComponent }>;
    init: (obj?: typeof __webpack_share_scopes__) => void;
};

declare global {
    interface Window {
        [key: string]: any;
    }
}

function loadRemoteScript(scope: string, remote: string): Promise<Module> {
    const __error__: Error & { type?: string; request?: string } = new Error();
    return new Promise<void>((resolve, reject) => {
        if (typeof window[scope] !== 'undefined') return resolve();
        const webpackRequire = __webpack_require__ as unknown as WebpackRequire;
        webpackRequire.l(
            remote,
            (event) => {
                if (typeof window[scope] !== 'undefined') return resolve();
                const errorType = event && (event.type === 'load' ? 'missing' : event.type);
                const realSrc = event && event.target && event.target.src;
                __error__.message = 'Loading script failed.\n(' + errorType + ': ' + realSrc + ')';
                __error__.name = 'ScriptExternalLoadError';
                __error__.type = errorType;
                __error__.request = realSrc;
                reject(__error__);
            },
            scope,
        );
    }).then(() => window[scope]);
}

async function initSharing() {
    const webpackShareScopes = __webpack_share_scopes__ as unknown as WebpackShareScopes;
    if (!webpackShareScopes?.default) {
        await __webpack_init_sharing__('default');
    }
}

const initContainer = async (containerScope: any) => {
    try {
        const webpackShareScopes = __webpack_share_scopes__ as unknown as WebpackShareScopes;
        if (!containerScope.__initialized && !containerScope.__initializing) {
            containerScope.__initializing = true;
            await containerScope.init(webpackShareScopes.default);
            containerScope.__initialized = true;
            delete containerScope.__initializing;
        }
    } catch (error) {
        console.error(error);
    }
};



export async function loadRemoteComponent(data: RemoteComponentType): Promise<{ default: FunctionComponent }> {
    const { scope, module, remoteEntry } = data;
    const container = await loadRemoteScript(scope, remoteEntry);
    // Initializes the share scope. This fills it with known provided modules from this build and all remotes
    await initSharing();
    // Initialize the container, it may provide shared modules
    await initContainer(container);
    const factory = await container.get(module);
    const Module = factory();

    console.groupCollapsed('Remote Module', 'color:green;');
    console.log('Module', Module);
    console.log('__webpack_init_sharing__', __webpack_init_sharing__);
    console.log('__webpack_share_scopes__', __webpack_share_scopes__);
    console.groupEnd();

    return Module;
}
