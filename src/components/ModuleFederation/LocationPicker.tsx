import { loadRemote } from "@/utils/loadRemote";
import { useState, useEffect, useRef } from "react";
import withErrorBoundary from "@/hocs/withErrorBoundary";
import { withImportFederatedModule } from "@/hocs/withImportFederatedModule";
import { Button } from "../ui/Button";
import { getFinSDKURL } from "@/api/getFinSDKURL";

export enum PermissionStatus {
    PERMISSION_DENIED = 'PERMISSION_DENIED',
    PERMISSION_NOT_GRANTED = 'PERMISSION_NOT_GRANTED',
    PERMISSION_ALWAYS = 'PERMISSION_ALWAYS',
    PERMISSION_WHEN_USE = 'PERMISSION_WHEN_USE',
}

export type CityModel = {
  id: number;
  name: string;
  city_name: string;
  latitude: number;
  longitude: number;
};

export interface CurrentLocation {
  permissionStatus: PermissionStatus;
  shouldOpenSetting: boolean;
  location: CityModel
};

export interface LocationPickerProps extends React.ComponentProps<typeof Button> {
  onLocationChange: (result: CurrentLocation) => void;
  screenMetadata: Record<string, any>;
}

const LocationPicker = (props: any) => {
  const MFComponent = useRef<React.ComponentType<any> | null>(null);
  const [isEmpty, setIsEmpty] = useState(true);

  useEffect(() => {
    const fetchRemoteComponent = async () => {
      try {
        const remoteModule = await loadRemote(
          getFinSDKURL(), // Remote URL
          // "http://localhost:3001/remoteEntry.js",
          "fin_sdk", // Remote container name
          "./LocationPicker" // Module name (remote component)
        )
        MFComponent.current = remoteModule.default;

        setIsEmpty(false);
      } catch (error) {
        console.error("Error loading remote component:", error);
      }
    };
    fetchRemoteComponent();
  }, []);

  if (isEmpty || !MFComponent.current) return null;
  const Comp = MFComponent.current;
  return <>{Comp ? <Comp {...props} /> : null}</>;
};

export default withErrorBoundary(withImportFederatedModule<any>(LocationPicker));
