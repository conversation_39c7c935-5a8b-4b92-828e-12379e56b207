import { loadRemote } from "@/utils/loadRemote";
import { useState, useEffect, useRef, memo } from "react";
import withErrorBoundary from "@/hocs/withErrorBoundary";
import { withImportFederatedModule } from "@/hocs/withImportFederatedModule";
import { getAuthenicationApp } from "@/api/getAuthenicationApp";

const AuthenticationContainer = (props: any) => {
  const MFComponent = useRef<any | null | undefined>(null);
  const [isEmpty, setIsEmpty] = useState(true);

  useEffect(() => {
    const fetchRemoteComponent = async () => {
      try {
        const remoteModule = await loadRemote(
          getAuthenicationApp(), // Remote URL
          "authentication_challenge_app", // Remote container name
          "./AuthenticationContainer" // Module name (remote component)
        );

        // Assuming RecommendView is a default export
        MFComponent.current = remoteModule.AuthenticationContainer;
        setIsEmpty(false);
      } catch (error) {
        console.error("Error loading remote component:", error);
      }
    };
    fetchRemoteComponent();
  }, []);

  if (isEmpty || !MFComponent.current) return null;
  const Comp = MFComponent.current;
  return <>{Comp ? <Comp {...props} /> : null}</>;
};

export default withErrorBoundary(withImportFederatedModule(memo(AuthenticationContainer)));
