import { images } from "@/res"
import { Button } from "@/components/ui/Button";
import { useEffect } from "react";
import { MaintenanceInfo } from "@/types/maintenanceInfo";
import { cn } from "@/lib/utils";

interface MaintenanceUIProps extends MaintenanceInfo {
    className?: string;
    onClose?: () => void;
}

export const MaintenanceUI = (props: MaintenanceUIProps ) => {

    const handleOnClose= () => {
        props.onClose?.();
    }

    useEffect(() => {
        return () => {
            props.onClose?.();
        }
    }, [])

    const img = props?.images ? props?.images : images.ImgMaintenance;
    const title = props?.title ? props.title : "Tính năng đang bảo trì";
    const description = props.description ? props.description : "Tính năng đang được bảo trì nhằm đảm bảo trải nghiệm tốt nhất cho người dùng. Mong bạn thông cảm và quay trở lại sau."
    const cta_title = props.cta_title ? props.cta_title : "Đã hiểu";
    const cta_variant = props.cta_variant ? props.cta_variant : "primary";
    return (
        <>
            <div className={cn("w-full p-4 pt-6 flex flex-col gap-6 items-center", props?.className)}>
                <img loading="lazy" src={img} className="w-[180px] h-[180px]" />
                <div className="text-center">
                    <h2 className="text-lead font-bold mb-2">{title}</h2>
                    <p className="text-base text-dark-300">{description}</p>
                </div>
                <Button onClick={handleOnClose} variant={cta_variant as "primary" | "outlined" | undefined} size="lg" className="w-full">{cta_title}</Button>
            </div>
        </>
    )
}