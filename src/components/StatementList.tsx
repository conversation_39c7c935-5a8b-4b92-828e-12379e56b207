import { useEffect, useState } from "react";
import { toast } from "sonner";
import { format, startOfYear, endOfYear } from "date-fns";
import { Button } from "./ui/Button";
import { Skeleton } from "./ui/Skeleton";
import { WarningCard } from "./WarningCard";
import { getStatementList } from "@/api/getStatementList";
import { Statement } from "@/types/statement";
import debounce from "lodash.debounce";

export const StatementList = ({
  accountId,
  onClick,
  onLoad,
}: {
  accountId?: string;
  onClick?: (statement: Statement) => void;
  onLoad?: () => void;
}) => {
  const [statementList, setStatementList] = useState<Statement[] | null>();

  const handleLoadStatement = (statement: Statement) => {
    onClick?.(statement);
  };

  const groupStatmentsByYear = (items: Statement[]) => {
    return items.reduce((acc, item) => {
      const year = new Date(item.incurred_date).getFullYear(); // Extract year from incurred_date
      if (!acc[year]) {
        acc[year] = [];
      }
      acc[year].push(item);
      return acc;
    }, {} as Record<number, Statement[]>);
  };

  const handleGetStatementList = async () => {
    if (!accountId) {
      return;
    }

    try {
      const currentYear = new Date().getFullYear();
      const fromYear = currentYear - 2;

      const payload = {
        account_id: accountId,
        from_time: startOfYear(new Date(fromYear, 0, 1)).toISOString(), // First day of the year - 2
        to_time: endOfYear(new Date(currentYear, 11, 31)).toISOString(), // Last day of the current year
      };

      const result = await getStatementList(payload);
      if (result.statements) {
        setStatementList(result.statements);
      } else {
        setStatementList(null);
      }
    } catch {
      toast.error("Không thể thực hiện yêu cầu, vui lòng thử lại sau");
      setStatementList(null);
    }
  };

  const handleRetryGetStatementList = debounce(() => {
    setStatementList(undefined);
    setTimeout(handleGetStatementList, 2000);
  }, 200);

  useEffect(() => {
    onLoad?.();
    (async () => await handleGetStatementList())();
  }, []);

  if (statementList === null) {
    return (
      <div className="min-h-96 max-h-[95%] flex flex-col">
        <div className="flex flex-col items-center justify-center flex-1 p-4">
          <WarningCard />
          <Button variant="outlined" onClick={handleRetryGetStatementList}>
            Thử lại
          </Button>
        </div>
      </div>
    );
  }

  if (Array.isArray(statementList) && !statementList?.length) {
    return (
      <div className="min-h-96 max-h-[95%] flex flex-col">
        <div className="flex flex-col items-center justify-center flex-1 p-4">
          <WarningCard title="Chưa có lịch sử sao kê" description="Bạn chưa có lịch sử sao kê." />
        </div>
      </div>
    );
  }

  if (!statementList) {
    return (
      <>
        <div className="min-h-96 max-h-[95%]">
          <div className="p-4">
            <div className="pb-6">
              <Skeleton className="h-5 mb-3 rounded-xl w-14 bg-slate-200" />
              <div className="grid grid-flow-row grid-cols-3 gap-4">
                <Skeleton className="w-auto h-10 rounded-full bg-slate-200" />
                <Skeleton className="w-auto h-10 rounded-full bg-slate-200" />
                <Skeleton className="w-auto h-10 rounded-full bg-slate-200" />
                <Skeleton className="w-auto h-10 rounded-full bg-slate-200" />
              </div>
            </div>
            <div className="pb-6">
              <Skeleton className="h-5 mb-3 rounded-xl w-14 bg-slate-200" />
              <div className="grid grid-flow-row grid-cols-3 gap-4">
                <Skeleton className="w-auto h-10 rounded-full bg-slate-200" />
                <Skeleton className="w-auto h-10 rounded-full bg-slate-200" />
                <Skeleton className="w-auto h-10 rounded-full bg-slate-200" />
                <Skeleton className="w-auto h-10 rounded-full bg-slate-200" />
                <Skeleton className="w-auto h-10 rounded-full bg-slate-200" />
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  const groupedByYear = groupStatmentsByYear(statementList);

  return (
    <div className="overflow-y-auto min-h-96">
      {Object.keys(groupedByYear)
        .reverse()
        .map((year: string, idx: number) => (
          <div key={idx} className="p-4">
            <div className="pb-6">
              <h3 className="pb-3 font-bold text-lead">{year}</h3>
              <div className="grid grid-flow-row grid-cols-3 gap-4">
                {groupedByYear[parseInt(year)].map((statement, index) => (
                  <Button
                    key={index}
                    onClick={() => handleLoadStatement(statement)}
                    className="w-full font-normal rounded-full text-lead"
                    variant="secondary">
                    Tháng {format(statement.incurred_date, "M")}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        ))}
    </div>
  );
};
