import { cn } from "@/lib/utils";

export default function AnimatedGradientText({ text, loop = true, className}: { text: string, loop?: boolean, className?: string }) {
  return (
    <>
      <style>
        {`
        .animate-text-gradient {
            animation: text-gradient 1.3s linear ${loop ? "infinite" : "1"};
        }
        @keyframes text-gradient {
            to {
                background-position: -200%
            }
        }`}
      </style>
      <span className={cn("inline-flex animate-text-gradient bg-gradient-to-r from-[#ACACAC] via-[#363636] to-[#ACACAC] bg-[200%_auto] text-transparent bg-clip-text", className)}>
        {text}
      </span>
    </>
  );
}
