import { colors } from "@/constants/colors";
import { GeneralCheckSolidSecondary, GeneralClosecircleSolidSecondary, GeneralInfoSolidSecondary, GeneralLoadingSecondary, GeneralWarningSolidSecondary } from "@zpi/looknfeel-icons";
import { Toaster as Sonner } from "sonner";
import { LoadingFill } from "../icon/LoadingFill";

type ToasterProps = React.ComponentProps<typeof Sonner>;

const Toaster = ({ ...props }: ToasterProps) => {
  return (
    <Sonner
      className="toaster group"
      richColors
      toastOptions={{
        classNames: {
          toast:
            "group flex justify-between px-4 py-3 gap-2 toast group-[.toaster]:bg-white group-[.toaster]:rounded-full group-[.toaster]:text-primary-dark",
          content: "flex-1",
          description: "group-[.toast]:text-muted-foreground",
          actionButton: "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
          cancelButton: "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground self-end",
          // closeButton: "group-[.toast]:bg-white group-[.toast]:text-gray-400 group-[.toast]:rounded-full group-[.toast]:top-[22px] group-[.toast]:left-[unset] group-[.toast]:right-[24px]",
        },
      }}
      icons={{
        success: <GeneralCheckSolidSecondary color={colors.primary.green} />,
        info: <GeneralInfoSolidSecondary color={colors.primary.blue} />,
        warning: <GeneralWarningSolidSecondary color={colors.secondary.orange} />,
        error: <GeneralClosecircleSolidSecondary color={colors.secondary.red} />,
        loading: <LoadingFill className="duration-500 animate-spin" />,
      }}
    
      {...props}
    />
  );
};

export { Toaster };
