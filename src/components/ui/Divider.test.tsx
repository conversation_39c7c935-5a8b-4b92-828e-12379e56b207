import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { Divider } from './Divider';

describe('LineDot', () => {
  it('renders correctly with default props', () => {
    render(<Divider />);
    const canvas = screen.getByTestId('divider-canvas');
    expect(canvas).toBeInTheDocument();
  });

  it('renders with specified thick', () => {
    const thick = 5;
    render(<Divider thick={thick} />);
    const canvas = screen.getByTestId('divider-canvas');
    expect(canvas).toBeInTheDocument();
  });

  it('renders with specified orientation', () => {
    const orientation = 'vertical';
    render(<Divider orientation={orientation} />);
    const canvas = screen.getByTestId('divider-canvas');
    expect(canvas).toBeInTheDocument();
  });
});
