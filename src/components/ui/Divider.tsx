import { useRef, useEffect, useCallback, memo } from "react";
import { colors } from "../../constants/colors";

interface DividerProps {
  color?: string;
  thick?: number; //px
  orientation?: "vertical" | "horizontal";
  shape?: "circle" | "square"; // Shape of the dot
  type?: "dot" | "line";
  className?: string;
}

export const Divider = memo(
  ({
    color = colors.dark[100],
    thick = 1,
    orientation = "horizontal",
    shape = "circle",
    type = "dot",
    className,
  }: DividerProps) => {
    const renderDivider = useCallback(
      (canvas: any) => {
        if (!canvas) {
          return;
        }
        const ctx = canvas.getContext("2d");
        const parentWidth = (canvas?.parentNode as HTMLElement)?.offsetWidth || 0;
        const parentHeight = (canvas?.parentNode as HTMLElement)?.offsetHeight || 0;
        const spacing = 4;

        if (!ctx || !canvas) return;

        // Set the canvas size based on orientation
        if (orientation === "horizontal") {
          canvas.width = parentWidth;
          canvas.height = thick + 10;
        } else {
          canvas.width = thick + 10;
          canvas.height = parentHeight;
        }

        // Set the drawing color
        ctx.fillStyle = color;
        ctx.strokeStyle = color;
        ctx.lineWidth = thick;

        if (type === "dot") {
          // Draw the dot in a loop
          if (orientation === "horizontal") {
            let x = thick / 2;
            while (x < parentWidth) {
              ctx.beginPath();
              if (shape === "circle") {
                ctx.arc(x, canvas.height / 2, thick / 2, 0, 2 * Math.PI);
              } else {
                ctx.rect(x - thick / 2, canvas.height / 2 - thick / 2, thick, thick);
              }
              ctx.fill();
              x += thick + spacing;
            }
          } else {
            let y = thick / 2;
            while (y < parentHeight) {
              ctx.beginPath();
              if (shape === "circle") {
                ctx.arc(canvas.width / 2, y, thick / 2, 0, 2 * Math.PI);
              } else {
                ctx.rect(canvas.width / 2 - thick / 2, y - thick / 2, thick, thick);
              }
              ctx.fill();
              y += thick + spacing;
            }
          }
        } else if (type === "line") {
          // Draw a continuous line
          ctx.beginPath();
          if (orientation === "horizontal") {
            ctx.moveTo(0, canvas.height / 2);
            ctx.lineTo(canvas.width, canvas.height / 2);
          } else {
            ctx.moveTo(canvas.width / 2, 0);
            ctx.lineTo(canvas.width / 2, canvas.height);
          }
          ctx.stroke();
        }
      },
      [color, thick, orientation, shape, type]
    );

    return (
      <canvas
        className={className}
        data-testid="divider-canvas"
        ref={(canvasRef) => renderDivider(canvasRef)}
        style={{ width: "100%" }}
      />
    );
    });
  
  Divider.displayName = 'Divider';

