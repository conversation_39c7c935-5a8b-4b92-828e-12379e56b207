import { VariantProps, cva } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { ReactNode, HTMLAttributes, forwardRef } from 'react';

const AnimationVariants = cva('transform-gpu transition-all motion-reduce:transition-none', {
  variants: {
    variant: {
      default: '',
      slideIn: 'slide-in-from-bottom-6',
      blurOut: 'animate-blur-out'
    },
    timing: {
      default: 'duration-400',
      fast: 'duration-200 ease-out',
      slow: 'duration-700 ease-in-out',
    },
  },
  defaultVariants: {
    variant: 'default',
    timing: 'default',
  },
});

export interface AppAnimationProps extends HTMLAttributes<HTMLDivElement>, VariantProps<typeof AnimationVariants> {
  delay?: number;
  className?: string;
  children: ReactNode;
}

const AppAnimation = forwardRef<HTMLDivElement, AppAnimationProps>(
  ({ className, children, delay = 0, variant, timing, ...props }, ref) => {
    return (
      <div
        ref={ref}
        style={{
          animationDelay: `${delay}ms`,
        }}
        {...props}
        className={cn(AnimationVariants({ variant, timing }), className)}>
        {children}
      </div>
    );
  },
);

AppAnimation.displayName = 'AppAnimation';

export { AnimationVariants, AppAnimation };
