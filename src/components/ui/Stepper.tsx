import React, { createContext, useContext } from "react";
import { CheckedIcon } from "@/components/icon/CheckedIcon";
import { cn } from "@/lib/utils";

// Context type
type StepperContextType = {
  activeStep: number;
  stepsCount: number;
};

// Create context
const StepperContext = createContext<StepperContextType | undefined>(undefined);

// Hook to use stepper context
const useStepperContext = () => {
  const context = useContext(StepperContext);
  if (!context) {
    throw new Error("Stepper compound components must be used within a Stepper component");
  }
  return context;
};

// Main Stepper component
const Stepper = ({ activeStep, children }: { activeStep: number; children: React.ReactNode }) => {
  const childrenArray = React.Children.toArray(children);

  return (
    <StepperContext.Provider value={{ activeStep, stepsCount: childrenArray.length }}>
      <div className="overflow-hidden flex relative flex-col justify-center w-full text-base text-center bg-white rounded-lg">
        <div className="flex gap-0">{children}</div>
      </div>
    </StepperContext.Provider>
  );
};

// Step component
const Step = ({ step, title }: { step: number; title: string }) => {
  const { activeStep, stepsCount } = useStepperContext();

  return (
    <div className="flex flex-col items-center flex-1 py-2 bg-white">
      <div className="w-full flex justify-center items-center">
        <Line visible={step !== 1} active={activeStep >= step} />

        <StepIndicator step={step} active={activeStep === step} completed={activeStep > step} />

        <Line visible={step !== stepsCount} active={activeStep >= step} />
      </div>

      <div className="flex flex-col px-2 mt-1">
        <div className="text-dark-500">{title}</div>
      </div>
    </div>
  );
};

// Line component
const Line = ({ visible, active }: { visible: boolean; active: boolean }) => (
  <div className={cn("h-px flex-1", !visible && "invisible", active ? "bg-primary" : "bg-dark-200")} />
);

// StepIndicator component
const StepIndicator = ({ step, active, completed }: { step: number; active: boolean; completed: boolean }) => {
  if (completed) {
    return <CheckedIcon className="h-6 w-6" />;
  }

  return (
    <div className={cn("w-6 h-6 p-px border border-solid border-white")}>
      <div
        className={cn(
          "w-full h-full flex justify-center items-center rounded-full border border-solid",
          active ? "border-primary bg-primary" : "bg-dark-50 border-dark-300",
        )}>
        <span className={cn("font-semibold text-supertiny text-dark-300", active && "text-white")}>
          {step}
        </span>
      </div>
    </div>
  );
};

export { Stepper, Step };