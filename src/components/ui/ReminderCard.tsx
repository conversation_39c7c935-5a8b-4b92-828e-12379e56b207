import { FC, HTMLAttributes, forwardRef, ReactNode } from 'react';
import { cn } from '@/lib/utils';
import { images } from '@/res';

const ReminderCard = forwardRef<
  HTMLDivElement,
  { className?: string; children: ReactNode; variant?: 'primary' | 'danger' | 'warning' | 'success' }
>(({ className, children, variant = 'primary', ...props }, ref) => {
  const imgBg = {
    primary: images.BGPrimaryReminder,
    danger: images.BGDangerReminder,
    warning: images.BGWarningReminder,
    success: images.BGSuccessReminder,
  };
  const style = {
    backgroundImage: `url('${imgBg[variant]}')`,
    backgroundPosition: 'top 0px left 0px',
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'cover',
  };

  return (
    <>
      <div
        ref={ref}
        className={cn('relative flex flex-col justify-center rounded-lg pt-3', className)}
        {...props}
        style={style}>
        {children}
      </div>
    </>
  );
});

ReminderCard.displayName = 'ReminderCard';

const ReminderCardContent = ({ className, ...props }: HTMLAttributes<HTMLDivElement>) => (
  <div className={cn('px-4 flex justify-between', className)} {...props} />
);

ReminderCardContent.displayName = 'ReminderCardContent';

const ReminderCardTitle = ({ className, ...props }: HTMLAttributes<HTMLDivElement>) => (
  <h2 className={cn('flex gap-1 text-lead font-bold text-white', className)} {...props} />
);
ReminderCardTitle.displayName = 'ReminderCardTitle';

const ReminderCardDescription = ({ className, ...props }: HTMLAttributes<HTMLDivElement>) => (
  <p className={cn('mt-1.5 text-tiny leading-4 text-white', className)} {...props} />
);
ReminderCardDescription.displayName = 'ReminderCardDescription';

const ReminderCardFooter = ({ className, ...props }: HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      'flex gap-5 justify-between px-4 py-2 mt-2.5 w-full text-xs leading-4 text-primary bg-white border rounded-b-lg border-blue-50 border-solid',
      className,
    )}
    {...props}
  />
);

ReminderCardFooter.displayName = 'ReminderCardFooter';

interface IReminderCountdownBox {
  daysLeft?: number;
}

const ReminderCardCountdownBox: FC<IReminderCountdownBox> = ({ daysLeft }) => {
  if (daysLeft === undefined) {
    return null;
  }

  return (
    <div className="flex flex-col px-2 py-1 text-xs text-center bg-white rounded-lg shadow-sm min-w-16 whitespace-nowrap text-dark-500">
      {daysLeft === 0 ? (
        <span className="py-2 text-base font-bold">Hôm <br></br>nay</span>
      ) : (
        <>
          <span className="leading-[133%] text-dark-300">{daysLeft >= 0 ? 'Còn' : 'Quá'}</span>
          <span className="text-base font-bold">{Math.abs(daysLeft).toString().padStart(2, '0')}</span>
          <span className="leading-[133%]">ngày</span>
        </>
      )}
    </div>
  );
};

ReminderCardCountdownBox.displayName = 'ReminderCountdownBox';

export {
  ReminderCard,
  ReminderCardContent,
  ReminderCardTitle,
  ReminderCardDescription,
  ReminderCardCountdownBox,
  ReminderCardFooter,
};
