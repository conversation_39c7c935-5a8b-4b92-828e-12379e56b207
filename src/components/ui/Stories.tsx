import React, { memo, useCallback, useEffect, useState } from 'react';
import { EmblaOptionsType } from 'embla-carousel';
import useEmblaCarousel from 'embla-carousel-react';
import { cn } from '../../lib/utils';
import Autoplay from 'embla-carousel-autoplay';

type PropType = {
  slides: string[];
  options?: EmblaOptionsType;
  autoplay?: boolean;
  duration?: number;
  visibleTimeline?: boolean;
  onSwipe?: () => void;
};

const Stories: React.FC<PropType> = props => {
  const { slides, options, autoplay, duration = 3000, visibleTimeline = false, onSwipe } = props;
  //WIP:
  //  - set duration
  const autoplayOptions = {
    delay: duration,
    stopOnInteraction: false,
    stopOnMouseEnter: true,
  };

  const [emblaRef, emblaApi] = useEmblaCarousel(options, [Autoplay(autoplayOptions)]);

  const [activeIndex, setActiveIndex] = useState(0);

  const onSlideChange = useCallback(() => {
    if (!emblaApi || !visibleTimeline) return;
    setActiveIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  const play = useCallback(() => {
    if (!autoplay) {
      return;
    }
    emblaApi?.plugins()?.autoplay?.play();
  }, [autoplay, emblaApi]);

  const stop = useCallback(() => {
    emblaApi?.plugins()?.autoplay?.stop();
  }, [emblaApi]);

  const handlePrev = () => {
    onSwipe?.()
    stop();
    emblaApi?.scrollPrev();
    play();
  };

  const handleNext = () => {
    onSwipe?.()
    stop();
    emblaApi?.scrollNext();
    play();
  };

  useEffect(() => {
    if (!emblaApi) return;
    if (!autoplay) {
      stop();
    }
    emblaApi.on('select', onSlideChange);
    emblaApi.on('pointerUp', () => { onSwipe?.() })
    return () => {
      emblaApi.off('select', onSlideChange);
      emblaApi.off('pointerUp', () => { onSwipe?.() })
    };
  }, [emblaApi, onSlideChange, play, stop, autoplay]);

  return (
    <>
      <div className="stories">
        {visibleTimeline ? <div className="absolute flex w-full gap-3 px-4 pt-4">
          {slides.map((_, index) => (
            <div key={index} className={cn('h-1 rounded-full relative bg-white/20 flex-1')}>
              <b
                className={cn(
                  'absolute w-0 h-full bg-white rounded-full',
                  activeIndex > index && 'w-full',
                  !autoplay && activeIndex === index && 'w-full',
                  autoplay && activeIndex === index && 'animate-fillWidth',
                )}></b>
            </div>
          ))}
        </div> : null}
        <div className="overflow-hidden" ref={emblaRef}>
          <div className="stories__container">
            {slides.map((item, index) => (
              <div className="stories__slide" key={index}>
                <div className="stories__slide__number">
                  <span>{index + 1}</span>
                </div>
                <img loading="lazy" className="stories__slide__img" src={item} alt="Your alt text" />
              </div>
            ))}
          </div>
        </div>
        {visibleTimeline ? <div className="absolute top-0 left-0 z-10 flex w-full h-full">
          <button className="w-1/2 h-full" onClick={handlePrev}></button>
          <button className="w-1/2 h-full" onClick={handleNext}></button>
        </div> : null}
      </div>
    </>
  );
};

export default memo(Stories);
