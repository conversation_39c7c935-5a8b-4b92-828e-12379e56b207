import { FillXIcon } from '../icon/FillXIcon';

import { cn } from '../../lib/utils';
import { ReactNode, forwardRef, useState } from 'react';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  containerClass?: string;
  reset?: () => void;
  icon?: ReactNode;
  label?: string;
}

const Input = forwardRef<HTMLInputElement, InputProps>(({ containerClass, className, label, reset, ...props }, ref) => {
  return (
    <div
      className={cn(
        'relative gap-2 px-4 py-2 flex items-center bg-white rounded-lg border border-solid border-dark-200 has-[:focus]:border-blue-500',
        containerClass,
      )}>
      {props?.icon}
      <div className="relative flex flex-col-reverse w-full pr-3">
        <input
          className={cn(
            'peer px-0 w-full h-full bg-transparent duration-300 text-dark-700 font-sans font-normal disabled:bg-dark-50 disabled:border-0 transition-all text-lead border-none',
            className,
          )}
          ref={ref}
          {...props}
        />
        {label ? <label className="text-tiny text-dark-300">{label}</label> : null}
        {reset ? (
          <button
            type="button"
            onClick={reset}
            className="absolute visible -translate-y-1/2 peer-placeholder-shown:invisible -right-2 top-1/2">
            <FillXIcon width={24} height={24} />
          </button>
        ) : null}
      </div>
    </div>
  );
});
Input.displayName = 'Input';

export { Input };
