import { Component, PropsWithChildren, ReactNode } from "react";

export interface ErrorHandlerState {
  error: Error | null;
}

export class ErrorBoundary extends Component<
  PropsWithChildren<ReactNode> & {
    fallback?: ReactNode;
  }
> {
  state: ErrorHandlerState = {
    error: null,
  };

  static getDerivedStateFromError(error: Error): { error: Error } {
    return { error };
  }

  render(): any {
    if (this.state.error) {
      window?.Sentry?.captureException(this.state.error);

      return this.props.fallback ? this.props.fallback : <></>;
    }
    return this.props.children;
  }
}
