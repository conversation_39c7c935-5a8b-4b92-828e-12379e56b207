/// <reference types="react-scripts" />
declare var __BASE_NAME__;
declare var __DEV__;
declare var __VERSION__;
declare var __APP_NAME__;
declare var ADVERTISING_GATEWAY_APP_URL;;


declare global {
  interface Window {
    __USER_INFO__: string;
    __APP_INFO__: {
      env: string;
      ver: string;
    };
    zlpSdk: any;
    UM_AUTH_CHALLENGE: {
        openAuthChallenge: (payload: any) => void;
    }
    [key: string]: Container | any;
  }
}

type Container = {
    get(module: string): () => Promise<any>;
    init(shareScope: any): void;
  };
  
  declare global {
    interface Window {
      
    }
  }
  declare const __webpack_share_scopes__: any;
  