import { ADJUST_KYC_DEEPLINK, EKYC_URL } from '../constants';
import { appOrigin, rootBase } from '../shared/environment';
import { launchDeeplink } from '../lib/ZalopaySDK/launchDeeplink';

export const launchUpdateEKYC = (source: number = 0) => {
  const queryParams = new URLSearchParams({
    cb_url: (appOrigin + rootBase) as string,
    source: source.toString(),
    is_update: '1', // 0 kyc, 1 adjust kyc
  });
  const ekycUrl = `${EKYC_URL}?${queryParams.toString()}`;

  launchDeeplink({zpi: ekycUrl, zpa: `${ADJUST_KYC_DEEPLINK}?source=${source}`});
};


