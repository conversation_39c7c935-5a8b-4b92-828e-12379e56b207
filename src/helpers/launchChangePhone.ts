import { UM_UPDATE_PHONE_DEEPLINK, UM_UPDATE_PHONE_URL } from "../constants";
import { launchDeeplink } from "../lib/ZalopaySDK/launchDeeplink";
import { appOrigin, rootBase } from "../shared/environment";

export const launchChangePhone = (source: string = "installment") => {
  const queryParams = new URLSearchParams({
    cb_url: (appOrigin + rootBase) as string,
    source: source.toString(),
    zpi_env: __DEV__ ? "DEVELOPMENT" : "",
  });
  const zpiUrl = `${UM_UPDATE_PHONE_URL}?${queryParams.toString()}`;

  launchDeeplink({ zpi: zpiUrl, zpa: `${UM_UPDATE_PHONE_DEEPLINK}?source=${source}` });
};
