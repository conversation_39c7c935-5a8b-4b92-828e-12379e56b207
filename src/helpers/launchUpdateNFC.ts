import { NFC_DEEPLINK, NFC_URL } from '../constants';
import { launchDeeplink } from '../lib/ZalopaySDK/launchDeeplink';
import { appOrigin, rootBase } from '../shared/environment';

export const launchUpdateNFC = (source: number = 0) => {
//WIP
//   const platform =  appStore.getState().appInfo?.platform;
//   const { openAuthChallenge } = useAuthentication();
//  if(platform === PLATFORM.ZPI) {
//   openAuthChallenge({
//     authType: UMAuthType.NFC,
//     source: source,
//     onComplete: (data: any) => {
//       console.log(data);
//     },
//   });
//  } else {
//   launchDeeplink({zpi: "", zpa: `${NFC_DEEPLINK}?source=${source}`});
//  }

const queryParams = new URLSearchParams({
  cb_url: (appOrigin + rootBase) as string,
  source: source.toString(),
  is_update: '1',
});
const nfcUrl = `${NFC_URL}?${queryParams.toString()}`;

launchDeeplink({zpi: nfcUrl, zpa: `${NFC_DEEPLINK}?source=${source}`});
};


