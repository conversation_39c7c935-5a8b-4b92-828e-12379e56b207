//DO NOT EDIT
//this file should not be edited manually, instead run script sync_image_res to update new image in res/images folder
export const local_images = {
  CIMBLogo: require('./CIMBLogo.svg'),
  CIMBLogo2: require('./CIMBLogo2.svg'),
  ErrorState: require('./ErrorState.png'),
  EmptyHistory: require('./HistoryScreen/EmptyHistory.svg'),
  IconDotted: require('./IconDotted.png'),
  IconEarlyDischarge: require('./IconEarlyDischarge.png'),
  IconTransactionPay: require('./IconTransactionPay.svg'),
  IconTransactionPlus: require('./IconTransactionPlus.svg'),
  IconTransactionRePay: require('./IconTransactionRePay.svg'),
  ImgGiftbox: require('./ImgGiftbox.png'),
  ImgTofiWonder: require('./ImgTofiWonder.png'),
  InstallmentLogo: require('./InstallmentLogo.svg'),
  IconGeneralCheckDone: require('./LandingScreen/IconGeneralCheckDone.png'),
  IconGeneralCloseCircle: require('./LandingScreen/IconGeneralCloseCircle.png'),
  IconGeneralWarning: require('./LandingScreen/IconGeneralWarning.png'),
  IconTips: require('./LandingScreen/IconTips.png'),
  ReminderWatermark: require('./ReminderWatermark.svg'),
  SplashscreenInstallment: require('./SplashScreen/SplashscreenInstallment.svg'),
  ZaloPayLogo: require('./ZaloPayLogo.svg'),
};
