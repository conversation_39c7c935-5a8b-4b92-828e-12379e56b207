import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

export type UIStore = {
  showAsset: boolean;
  setShowAsset: (showAsset: boolean) => void;
};

export const uiStore = create<UIStore, any>(
  persist(
    set => ({
      showAsset: true,
      setShowAsset: (showAsset: boolean) => {
        set({ showAsset });
      },
    }),
    {
      name: "installment_ui_store", // name of the item in the storage (must be unique)
      storage: createJSONStorage(() => localStorage), // (optional) by default, 'localStorage' is used
    },
  ),
);
