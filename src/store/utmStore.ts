import { create } from 'zustand';

export type UTMStore = {
  utmSource: string | null;
  utmCampaign: string | null;
  setUtmSource: (utmSource: string | null) => void;
  setUtmCampaign: (utmCampaign: string | null) => void;
};

export const utmStore = create<UTMStore>(set => ({
  utmSource: null,
  utmCampaign: null,
  setUtmSource: (utmSource: string | null) => {
    set({ utmSource });
  },
  setUtmCampaign: (utmCampaign: string | null) => {
    set({ utmCampaign });
  },
}));
