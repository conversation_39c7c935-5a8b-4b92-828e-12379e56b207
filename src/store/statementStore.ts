import { create } from "zustand";
import { getLastestStatement } from "@/api/getLastestStatement";
import { Statement } from "@/types/statement";
import { createPersistStore } from "@/lib/persistStore/createPersistStore";

export type StatementStore = {
  lastestStatement?: Statement | undefined;
  getLastestStatementInfo: (account_id: string) => Promise<void>;
};

export const statementStore = createPersistStore<StatementStore>("statement-store", (set) => ({
  lastestStatement: undefined,
  getLastestStatementInfo: async (account_id: string) => {
    try {
      const result = await getLastestStatement(account_id);
      if (result && result?.statement) {
        set({ lastestStatement: result.statement });
      }
    } catch {}
  },
}));
