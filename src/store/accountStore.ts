import { PermissionInfo } from "@/types/permission";
import { AccountInfo, getAccountInfo } from "@/api/getAccountInfo";
import { BindStatus } from "@/types/bindStatus";
import { OnboardingInfo, getOnboardingInfo as fetchOnboardingInfo } from "@/api/getOnboardingInfo";
import { ContractRequest, ContractResponse, getContract } from "@/api/getContract";
import { toast } from "sonner";
import { KycStatus } from "@/types/kycStatus";
import { getPermission } from "@/api/getPermission";
import { createPersistStore } from "@/lib/persistStore/createPersistStore";
import { deleteOldDB } from "@/lib/persistStore/idbService";
import { ScreenKey } from "@/constants";

export type AccountStore = {
  permissionInfo?: PermissionInfo | null;
  accountInfo?: AccountInfo | null;
  onboardingInfo?: OnboardingInfo | undefined;
  contractInfo?: ContractResponse | undefined;
  isEKYC: boolean | undefined;
  isBound: boolean | undefined;
  isWhitelist: boolean | undefined;
  initPermission: () => Promise<PermissionInfo | undefined | null>;
  setPermissionInfo: (permissionInfo: PermissionInfo | undefined) => void;
  setBindStatus: (bindStatus: BindStatus) => void;
  setAccountInfo: (accountInfo: AccountInfo) => void;
  getAccountInfo: () => Promise<void>;
  getOnboardingInfo: () => void;
  getContractInfo: (onboardingId: string) => Promise<ContractResponse | undefined>;
  permissionNavigateHandler: () => string;
  resetStore: () => void;
};

export const accountStore = createPersistStore<AccountStore>("account-store", (set, get) => ({
  profileInfo: undefined,
  permissionInfo: undefined,
  accountInfo: undefined,
  contractInfo: undefined,
  isEKYC: undefined,
  isBound: undefined,
  isWhitelist: undefined,
  initPermission: async () => {
    try {
      const result: PermissionInfo = await getPermission();
      if (result) {
        get().setPermissionInfo(result);
        return result;
      } else {
        return null;
      }
    } catch (err) {
      toast.error("Có lỗi khi lấy thông tin.");
      return null;
    }
  },
  setPermissionInfo: (permissionInfo: PermissionInfo | undefined) => {
    const isEKYC =
      permissionInfo?.kyc_status?.status &&
      [KycStatus.KYC_STATUS_APPROVE, KycStatus.KYC_STATUS_BYPASS].includes(permissionInfo?.kyc_status?.status);
    const isBound = Boolean(permissionInfo && permissionInfo?.bind_status === BindStatus.BIND_STATUS_BOUND);
    const isWhitelist = Boolean(permissionInfo && permissionInfo.is_whitelisted)
    set({ permissionInfo: permissionInfo, isEKYC, isBound, isWhitelist});
  },
  setBindStatus: (bindStatus: BindStatus) => {
    set((state) => ({ ...state, permissionInfo: { ...state.permissionInfo!, bind_status: bindStatus } }));
  },
  setAccountInfo: (accountInfo: AccountInfo) => {
    set({ accountInfo: accountInfo });
  },
  getAccountInfo: async () => {
    const permissionInfo = get().permissionInfo;
    try {
      const accountInfo = await getAccountInfo({
        account_id: permissionInfo?.bind_info?.account_id,
      });
      if (accountInfo?.account) {
        set({ accountInfo: accountInfo.account });
      }
    } catch {}
  },
  getOnboardingInfo: async () => {
    const permissionInfo = get().permissionInfo;
    try {
      const onboardingInfo = await fetchOnboardingInfo({
        onboarding_id: permissionInfo?.bind_info?.onboarding_id,
      });
      if (onboardingInfo) {
        set({ onboardingInfo });
      }
    } catch {}
  },
  getContractInfo: async (onboardingId: string) => {
    try {
      if (!onboardingId) {
        toast.error("Đã có lỗi gì đó, vui lòng thử lại sau.");
        return;
      }
      const payload: ContractRequest = {
        onboarding_id: onboardingId,
      };

      const contractInfo: ContractResponse = await getContract(payload);
      if (contractInfo) {
        set({ contractInfo });
      }
      return contractInfo;
    } catch {
      toast.error("Không thể xử lý yêu cầu của bạn, vui lòng thử lại sau.");
      return;
    }
  },
  permissionNavigateHandler: () => {
      const permissionInfo = get().permissionInfo;
      if (permissionInfo?.bind_status) {
        switch (permissionInfo.bind_status) {
          case BindStatus.BIND_STATUS_UNBOUND:
          case BindStatus.BIND_STATUS_UNKNOWN:
            return ScreenKey.OnboardingScreen;
          case BindStatus.BIND_STATUS_ONBOARDING:
            return ScreenKey.HomeScreen;
          default:
            return ScreenKey.HomeScreen;
        }
      } else {
        return ScreenKey.HomeScreen;
      }
    },
  resetStore: async () => {
    await deleteOldDB("account-store");
    set(
      {
        permissionInfo: undefined,
        accountInfo: undefined,
        onboardingInfo: undefined,
        contractInfo: undefined,
        isEKYC: undefined,
        isBound: undefined,
      },
      true
    );
  }
}), {
  skipHydration: true,
});