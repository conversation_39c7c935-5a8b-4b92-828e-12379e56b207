import { toast } from 'sonner';
import { create } from 'zustand';

import { getEKYCProfile } from '@/api/getEKYCProfile';
import { getOnboardingStatus as _fetchOnboardingStatus } from '@/api/getOnboardingStatus';
import { OnboardingState, OnboardingStatus } from '@/screens/OnboardingScreen/constant';
import { OTPInfo, ProfileIssue } from '@/types';
import { ProfileInfo } from '@/types/profileInfo';

export type OnboardingStore = {
  profileInfo: ProfileInfo | undefined;
  onBoardingState: OnboardingState | undefined;
  profileIssues: ProfileIssue[] | undefined;
  onboardingId: string | undefined;
  onboardingStatus: { onboardings: OnboardingStatus[] } | undefined;
  otpInfo: OTPInfo | undefined;
  notice: any;
  setNotice: (
    notice:
      | {
          content?: string | object;
          metadata?: {
            screenId: string;
          };
          callback?: () => void | Promise<void>;
        }
      | undefined
      | null,
  ) => void;
  setProfileInfo: (profileInfo: ProfileInfo) => void;
  setOnBoardingState: (onBoardingState: OnboardingState) => void;
  setProfileIssues: (profileIssues: ProfileIssue[]) => void;
  setOnboardingId: (onboardingId: string) => void;
  setOnboardingStatus: (onBoardingStatus: { onboardings: OnboardingStatus[] } | undefined) => void;
  setOTPInfo: (otpInfo: OTPInfo) => void;
  getProfile: () => void;
  getOnboardingStatus: () => Promise<void>;
  resetOnboarding: () => void;
};

export const onboardingStore = create<OnboardingStore>((set, get) => ({
  profileInfo: undefined,
  onBoardingState: undefined,
  profileIssues: undefined,
  onboardingId: undefined,
  onboardingStatus: undefined,
  otpInfo: undefined,
  notice: undefined,
  setNotice: (
    notice:
      | {
          content?: string | object;
          callback?: () => void | Promise<void>;
          metadata?: { screenId: string };
        }
      | undefined
      | null,
  ) => {
    set({
      notice: {
        ...notice,
        content: notice?.content && typeof notice.content === "string" ? JSON.parse(notice.content) : notice?.content,
      },
    });
  },
  setProfileInfo: (profileInfo: ProfileInfo) => {
    set({ profileInfo: profileInfo });
  },
  setOnBoardingState: (onBoardingState: OnboardingState) => {
    const payload = {
      onBoardingState: onBoardingState,
    };
    set(payload);
  },
  setProfileIssues: (profileIssues: ProfileIssue[] | undefined) => {
    set({ profileIssues: profileIssues });
  },
  setOnboardingId: (onboardingId: string) => {
    set({ onboardingId: onboardingId });
  },
  setOnboardingStatus: (onboardingStatus: { onboardings: OnboardingStatus[] } | undefined) => {
    if (!onboardingStatus) {
      set({ onboardingStatus: undefined, onboardingId: undefined });
      return;
    }

    set({ onboardingStatus: onboardingStatus, onboardingId: onboardingStatus?.onboardings[0]?.id });

    if (onboardingStatus.onboardings[0]?.next_step) {
      switch (onboardingStatus.onboardings[0].next_step) {
        case "register_waiting":
          return get().setOnBoardingState(OnboardingState.REGISTER_WAITING);
        case "permission_waiting":
          return get().setOnBoardingState(OnboardingState.PERMISSION_WAITING);
        case "permission_rejected":
          return get().setOnBoardingState(OnboardingState.PERMISSION_REJECTED);
        case "contract_waiting":
          return get().setOnBoardingState(OnboardingState.CONTRACT_WAITING);
        case "face_challenge_waiting":
          return get().setOnBoardingState(OnboardingState.FACE_CHALLENGE_WAITING);
        case "otp_waiting":
          return get().setOnBoardingState(OnboardingState.OTP_WAITING);
        case "approval_waiting":
          return get().setOnBoardingState(OnboardingState.APPROVAL_WAITING);
        case "approval_rejected":
          return get().setOnBoardingState(OnboardingState.APPROVAL_REJECTED);
        case "approval_success":
          return get().setOnBoardingState(OnboardingState.APPROVAL_SUCCESS);
        default:
          return get().setOnBoardingState(OnboardingState.PERMISSION_WAITING);
      }
    }
  },
  setOTPInfo: (otpInfo: OTPInfo) => {
    set({ otpInfo: otpInfo });
  },
  getProfile: async () => {
    try {
      set({ profileInfo: undefined, profileIssues: undefined });
      const result = await getEKYCProfile();
      if (result?.profile_info) {
        get().setProfileInfo(result.profile_info);
      }
      if (result?.profile_issues) {
        get().setProfileIssues(result.profile_issues);
      }
    } catch {
      toast.error("Không lấy được thông tin, vui lòng thử lại sau", { position: "top-center" });
    }
  },
  getOnboardingStatus: async () => {
    try {
      get().setOnboardingStatus(undefined);
      const result = await _fetchOnboardingStatus();
      if (result) {
        get().setOnboardingStatus(result);
      }
    } catch {
      toast.error("Không lấy được thông tin đăng ký, vui lòng thử lại sau", { position: "top-center" });
    }
  },
  resetOnboarding: () => {
    set({
      profileInfo: undefined,
      onBoardingState: OnboardingState.PERMISSION_WAITING,
      profileIssues: undefined,
      onboardingId: undefined,
      onboardingStatus: undefined,
      otpInfo: undefined,
    });
  },
}));
