import { create } from "zustand";
import { AppInfo } from "../types";
import { MaintenanceInfo } from "@/types/maintenanceInfo";


export type AppStore = {
  appInfo: AppInfo | null;
  openApprovedRegisterDialog: boolean;
  loading: boolean;
  setAppInfo: (appInfo: AppInfo) => void;
  setLoading: (loading: boolean) => void;
  setOpenApprovedRegisterDialog: (open: boolean) => void;
  maintenanceInfo: MaintenanceInfo | null | undefined;
  setMaintananceInfo: (MaintenanceInfo: MaintenanceInfo | null | undefined) => void;
};

export const appStore = create<AppStore>(
  (set) => ({
    appInfo: null,
    openApprovedRegisterDialog: false,
    loading: false,

    setAppInfo: (appInfo: AppInfo) => {
      set({ appInfo: appInfo });
    },
    setOpenApprovedRegisterDialog: (open: boolean) => {
      set({ openApprovedRegisterDialog: open });
    },
    setLoading: (loading: boolean) => {
      set({ loading });
    },
    maintenanceInfo: undefined,
    setMaintananceInfo: (maintenanceInfo: MaintenanceInfo | null | undefined) => {
      set({ maintenanceInfo })
    },
  })
);
