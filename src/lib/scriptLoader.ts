export interface LoadScriptOptions {
  id?: string,
  url: string,
  async?: boolean,
  defer?: boolean,
  crossorigin?: string | undefined | null,
}

/**
 * Get script element
 * @param id script element id 
 * @param url script URL
 * @returns HTMLScriptElement
 */
export function getScriptElement({ id, url }: { id?: string, url?: string }): HTMLScriptElement | null {
  let element = null;
  if (id) {
    element = <HTMLScriptElement>document.getElementById(id);
  } else {
    const scripts = document.getElementsByTagName('script');
    for (let i = scripts.length; i--;) {
      if (scripts[i].src === url) {
        element = scripts[i];
        break;
      }
    }
  }
  return element;
}

/**
 * Check script has loaded
 * @param id script element id 
 * @param url script URL
 * @returns boolean
 */
export function isScriptLoaded(params: { id?: string, url?: string }): boolean {
  return !!getScriptElement(params);
}

/**
 * Load script by URL 
 * @param id script element id 
 * @param url Script URL
 * @param async script loader async
 * @param defer script loader defer
 * @param crossorigin script loader crossorigin
 * @returns Promise
 */
export function loadScript(options: LoadScriptOptions) {
  const { id, url, async, defer, crossorigin } = options;
  let element: HTMLScriptElement | undefined;
  return new Promise<HTMLScriptElement | undefined>((resolve, reject) => {

    unloadScript(options);

    element = document.createElement('script');

    if (id) {
      element.id = id;
    }
    element.src = url;
    element.type = 'application/javascript';
    if (async !== false) {
      element.async = true;
    }
    if(defer === true) {
      element.defer = true;
    }

    if(!!crossorigin) {
      element.setAttribute('crossorigin', crossorigin);
    }

    element.onload = () => {
      resolve(element);
    };

    element.onerror = (error) => {
      // @ts-ignore
      const errorContent = typeof error === 'string' ? error : error?.target?.src;
      const throwContent = new Error(`Failed to loadScript ${errorContent}`);
      reject(throwContent);
    };
    // const bodyElement = document.getElementsByTagName("body")[0] || document.body || document;
    // bodyElement.appendChild(element);
    document.head.appendChild(element);
  });
}

/**
 * Remove script tags in DOM
 * @param options {id, url} 
 * @returns 
 */
export function unloadScript({ id, url }: { id?: string, url?: string }) {
  if (id) {
    const element = document.getElementById(id);
    if (element) {
      element.remove();
      return;
    }
  }

  const scripts = document.getElementsByTagName('script');
  for (let i = scripts.length; i--;) {
    if (scripts[i].src === url) {
      scripts[i].remove();
    }
  }
}
