import queryString from "query-string";
import { appOrigin, baseName, rootBase } from "../shared/environment";
import { navigateTo } from "./ZalopaySDK";

export const faceChallenge = (source: string, callbackUrl?: string, params?: { [key: string]: any }) => {
    const url = queryString.stringifyUrl({
      url: `/kyc/face-auth`,
      query: { ...params, cb_url: callbackUrl ? callbackUrl : `${appOrigin}${baseName}${rootBase}`, source },
    });
    navigateTo({ url });
  };
  