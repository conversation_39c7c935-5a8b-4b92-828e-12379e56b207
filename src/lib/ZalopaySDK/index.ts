import { baseName } from "../../shared/environment";

export const navigateTo = ({url, replace = false}:{ url: string; replace?: boolean }) => {
    if (!window.zlpSdk?.Navigator?.navigateTo) {
        window.zlpSdk?.Navigator.navigateTo?.({url, replace});
    } else {
      window.history.pushState({}, '', baseName + url);
    }
};

export const openDeepLink = ({ url }:{ url: string; }) => {
    if (window.zlpSdk?.Navigator?.openDeepLink) {
        window.zlpSdk?.Navigator?.openDeepLink?.({ url });
    }
};

export const getAppInfo = async () => {
    const result = await window.zlpSdk?.Device?.appInfo();
    return result;
};

