import { Logger } from "../logger";

export enum ETrackingStatus {
  ERROR = "error",
  SUCCESS = "success",
}
export interface ITrackingResult {
  status: ETrackingStatus;
  errorCode?: string;
  errorMessage?: string;
}

const trackingLogger = new Logger("TRACKING");

export const trackEvent = async (eventId: string, metadata?: Record<string, any>): Promise<ITrackingResult> => {
  if (window.zlpSdk?.Tracking?.trackEvent) {
    window.zlpSdk?.Tracking?.trackEvent?.({
      eventId,
      metadata,
    }).then((result: ITrackingResult) => {
      trackingLogger[result.status === ETrackingStatus.ERROR ? "error" : "info"](eventId, { metadata, result });
      return result;
    });
  }

  return {
    status: ETrackingStatus.ERROR,
    errorCode: "",
    errorMessage: "ZLP SDK not found",
  } as ITrackingResult;
};
