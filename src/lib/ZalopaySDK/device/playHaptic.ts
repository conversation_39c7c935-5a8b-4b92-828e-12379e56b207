
export enum PlayHapticType {
    HeavyTap = 'HeavyTap',
    LightTap = 'LightTap',
    Selection = 'Selection',
    MediumTap = 'MediumTap',
    Custom = 'Custom',
    Success = 'Success',
    Error = 'Error',
}
export const HAPTIC_MAPPINGS = {
    [PlayHapticType.HeavyTap]: {
        android: { constant: 0 },
        ios: { type: 'feedback', submode: 2, intensity: 0.8 },
    },
    [PlayHapticType.LightTap]: {
        android: { constant: 3 },
        ios: { type: 'feedback', submode: 0, intensity: 0.2 },
    },
    [PlayHapticType.Selection]: {
        android: { constant: 4 },
        ios: { type: 'selection' },
    },
    [PlayHapticType.MediumTap]: {
        android: { constant: 6 },
        ios: { type: 'feedback', submode: 1, intensity: 0.5 },
    },
    [PlayHapticType.Custom]: {
        android: { constant: 9, duration: 150 },
        ios: { type: 'notification', submode: 1, intensity: 0.6 },
    },
    [PlayHapticType.Success]: {
        android: { constant: 16 },
        ios: { type: 'notification', submode: 0 },
    },
    [PlayHapticType.Error]: {
        android: { constant: 17 },
        ios: { type: 'notification', submode: 2 },
    },
};

export interface PlayHapticAndroidOptions {
    constant: number;
    duration?: number;
}

export type PlayHapticIOSFeedbackType = 'feedback' | 'notification' | 'selection';

export interface PlayHapticIOSOptions {
    feedbackType: PlayHapticIOSFeedbackType;
    submode?: number;
    intensity?: number;
}

export interface PlayHapticOptions {
    type?: PlayHapticType; // Default is LightTap if not provided
    milliseconds?: number;
    android?: PlayHapticAndroidOptions;
    ios?: PlayHapticIOSOptions;
}

/**
 * Triggers device haptic feedback (vibration) on both Android and iOS platforms via the ZaloPay SDK.
 *
 * Supports predefined types (LightTap, HeavyTap, MediumTap, Selection, Success, Error) and custom haptic patterns.
 *
 * @param {PlayHapticOptions} [options] - Haptic feedback configuration options.
 * @param {PlayHapticType} [options.type] - The vibration type. Defaults to 'LightTap' if not specified.
 * @param {number} [options.milliseconds] - Duration of vibration in milliseconds (optional).
 * @param {PlayHapticAndroidOptions} [options.android] - Android-specific haptic settings (used when type is 'Custom').
 * @param {PlayHapticIOSOptions} [options.ios] - iOS-specific haptic settings (used when type is 'Custom').
 *
 * @returns {Promise<boolean>} Resolves to true if the haptic feedback was triggered successfully, false otherwise.
 *
 * @example
 * // Trigger a default LightTap haptic
 * await playHaptic({ type: PlayHapticType.LightTap, milliseconds: 300 });
 *
 * @example
 * // Trigger a custom haptic pattern
 * await playHaptic({
 *   type: PlayHapticType.Custom,
 *   android: { constant: 0 },
 *   ios: { feedbackType: 'feedback', submode: 2, intensity: 0.8 }
 * });
 */
export const playHaptic = async (options: PlayHapticOptions = {}): Promise<boolean> => {
    try {
        await window.zlpSdk?.Device?.playHaptic(options);
        return true;
    } catch {
        return false;
    }
};