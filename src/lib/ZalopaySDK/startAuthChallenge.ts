import { UMAuthType, UMStatus } from "../../constants";

export type UMAuthResult = { status: UMStatus; result: string };

export type AuthChallengeResult = {
  status: string;
  data: UMAuthResult;
};
export const startAuthChallenge = async ({
  payload,
}: {
  payload: {
    source: number;
    authType: UMAuthType;
    params?: Object;
    options?: Object;
    skipResult?: boolean;
    numberOfRetries?: number;
  };
}) => {
  try {
    if (window.zlpSdk?.Device?.startAuthChallenge) {
      const result = await window.zlpSdk?.Device?.startAuthChallenge?.(payload);
      return result as AuthChallengeResult;
    }
  } catch (e) {
    return { status: "error", data: { status: UMStatus.Failure, result: "" } } as AuthChallengeResult;
  }
};
