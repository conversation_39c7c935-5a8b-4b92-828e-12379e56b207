import { PLATFORM } from '../../constants';
import { openDeepLink, navigateTo } from '.';
import { appStore } from '../../store/appStore';

export const launchDeeplink = ({ zpi, zpa }: { zpi?: string; zpa?: string }) => {
  const platform =  appStore.getState().appInfo?.platform;
  if (platform === PLATFORM.ZPA) {
    zpa && openDeepLink({ url: zpa });
  } else {
    zpi && navigateTo({ url: zpi, replace: true });
  }
};
