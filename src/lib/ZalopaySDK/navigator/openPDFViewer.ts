/**
 * Opens a PDF file viewer in a bottom sheet using the ZaloPay SDK.
 *
 * @param {OpenPDFViewerOptions} options - Options for the PDF viewer.
 * @param {string} options.url - The URL of the PDF file to display.
 * @param {number} [options.height=80] - Height of the bottom sheet as a percentage (30-100). Defaults to 80.
 * @returns {Promise<boolean>} Resolves to true if the viewer opened successfully, false otherwise.
 *
 * @example
 * const success = await openPDFViewer({
 *   url: "https://example.com/file.pdf",
 *   height: 80,
 * });
 * if (!success) {
 *   // Handle error
 * }
 *
 * @see window.zlpSdk.Navigator.openPDFViewer
 *
 * @remarks
 * - Supported in ZaloPay App (Android >= 10.4.0, iOS >= 9.14.0) and ZMP for iOS.
 * - The height must be between 30 and 100.
 * - On error, returns false. Common error codes:
 *   - 051201: The PDF viewer's height exceeds the limit.
 */

export interface OpenPDFViewerOptions {
  url: string;
  height?: number;
}

export const openPDFViewer = async (options: OpenPDFViewerOptions) => {
  try {
    await window.zlpSdk?.Navigator?.openPDFViewer(options);
    return true;
  } catch {
    return false;
  }
};
