interface MainAppInfo {
  environmnent: string,
  version: string
}

export function getMainAppInfo(): MainAppInfo {
  const appInfo = window.__APP_INFO__;
  return {
    environmnent: appInfo.env,
    version: appInfo.ver
  };
}

export function getMainAppVersion(): string {
  const appInfo = getMainAppInfo();
  return appInfo.version;
}

export function getMainAppEnviroment(): string {
  const appInfo = getMainAppInfo();
  return appInfo.environmnent;
}
