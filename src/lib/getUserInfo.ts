type UserInfo = {
  avatar: string;
  display_name: string;
  phone: string;
  profile_level: number;
  zalopay_id: string;
};

export const getUserInfo = (): UserInfo | undefined => {
  try {
    if (typeof window !== "undefined" && window.__USER_INFO__) {
      const userInfo =
        typeof window.__USER_INFO__ === "string" ? JSON.parse(window.__USER_INFO__) : window.__USER_INFO__;
      return userInfo || undefined;
    }
    return undefined;
  } catch (error) {
    console.error("Error getting user info:", error);
    return undefined;
  }
};
