export type LogType = "info" | "warn" | "error" | "debug";

export class Logger {
  protected serviceUrl: string = "";
  protected key: string = "";
  constructor(key?:string, serviceUrl?: string) {
    this.serviceUrl = serviceUrl || "";
    this.key = key || "";
  }

  async sendLog(level: LogType, message: any, additionalData?: any) {
    const styles: Record<LogType, string> = {
      info: "color: blue; font-weight: bold;",
      warn: "color: orange; font-weight: bold;",
      error: "color: red; font-weight: bold;",
      debug: "color: green; font-weight: bold;",
    };
    const key = this.key;
    
    if (__DEV__ || window?.__APP_INFO__?.env === "sandboxqc") {
      console.log(`%c[${this.key ? this.key.toUpperCase() : level.toUpperCase()}]:`, styles[level], message, additionalData || "");
    }

    if (this.serviceUrl) {
      try {
        fetch(this.serviceUrl, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ level, key, message, additionalData, timestamp: new Date().toISOString() }),
        });
      } catch (error) {
        if (__DEV__ || window?.__APP_INFO__?.env === "sandboxqc") {
          console.error("Failed to send log to server:", error);
        }
      }
    }
  }

  info(message: string, additionalData?: any) {
    this.sendLog("info", message, additionalData);
  }

  warn(message: string, additionalData?: any) {
    this.sendLog("warn", message, additionalData);
  }

  error(message: string, additionalData?: any) {
    this.sendLog("error", message, additionalData);
  }

  debug(message: string, additionalData?: any) {
    this.sendLog("debug", message, additionalData);
  }
}
