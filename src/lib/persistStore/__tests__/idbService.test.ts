import { deleteDB } from 'idb';
import * as idbService from '../idbService';
import * as getUserInfo from '../../getUserInfo';
import 'fake-indexeddb/auto';

jest.mock('../../getUserInfo');

describe('idbService', () => {
    const mockUserId = 'test-user-123';
    jest.setTimeout(10000);

    beforeEach(() => {
        // Mock getUserInfo
        (getUserInfo.getUserInfo as jest.Mock).mockReturnValue({ zalopay_id: mockUserId });
    });

    afterEach(async () => {
        // Clean up database after each test
        await deleteDB(idbService.getDBName());
    });

    describe('getDBName', () => {
        it('should return correct database name with user ID', () => {
            const dbName = idbService.getDBName();
            expect(dbName).toBe(`installment-app-${mockUserId}`);
        });
    });
});