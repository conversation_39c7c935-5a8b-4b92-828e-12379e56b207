import { createIDBStorage, createPersistStore } from '../createPersistStore';
import { getData, saveData, deleteData } from '../idbService';

// Mock các hàm từ idbService
jest.mock('../idbService', () => ({
    getData: jest.fn(),
    saveData: jest.fn(),
    deleteData: jest.fn(),
}));

describe('createIDBStorage', () => {
    const storeName = 'testStore';
    const storage = createIDBStorage(storeName);

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return stringified data when getData succeeds', async () => {
        const testData = { foo: 'bar' };
        (getData as jest.Mock).mockResolvedValue(testData);

        const result = await storage.getItem('testKey');
        expect(JSON.stringify(result)).toBe(JSON.stringify(testData));
        expect(getData).toHaveBeenCalledWith('testKey');
    });

    it('should return null and log error when getData fails', async () => {
        const error = new Error('Test error');
        (getData as jest.Mock).mockRejectedValue(error);
        console.error = jest.fn();

        const result = await storage.getItem('testKey');
        expect(result).toBeNull();
        expect(console.error).toHaveBeenCalledWith(`Error getting data for ${storeName}:`, error);
    });
});

describe('setItem', () => {
    const storeName = 'testStore';
    const storage = createIDBStorage(storeName);

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should save parsed data successfully', async () => {
        const testData = { state: { foo: 'bar' } };
        await storage.setItem('testKey', testData);

        expect(saveData).toHaveBeenCalledWith('testKey', testData);
    });

    it('should log error when saveData fails', async () => {
        const error = new Error('Test error');
        (saveData as jest.Mock).mockRejectedValue(error);
        console.error = jest.fn();

        await storage.setItem('testKey', { state: { foo: 'bar' } });
        expect(console.error).toHaveBeenCalledWith(`Error saving data for ${storeName}:`, error);
    });
});

describe('removeItem', () => {
    const storeName = 'testStore';
    const storage = createIDBStorage(storeName);

    beforeEach(() => {
        jest.clearAllMocks();
    });
    it('should delete data successfully', async () => {
        await storage.removeItem('testKey');
        expect(deleteData).toHaveBeenCalledWith('testKey');
    });

    it('should log error when deleteData fails', async () => {
        const error = new Error('Test error');
        (deleteData as jest.Mock).mockRejectedValue(error);
        console.error = jest.fn();

        await storage.removeItem('testKey');
        expect(console.error).toHaveBeenCalledWith(`Error removing data for ${storeName}:`, error);
    });
});

describe('createPersistStore', () => {
    it('create store', () => {
        type TestStore = { count: number; increment: () => void };
        const storeName = 'testStore';

        const store = createPersistStore<TestStore>(
            storeName,
            (set) => ({
                count: 0,
                increment: () => set((state) => ({ count: state.count + 1 })),
            })
        );

        expect(store).toBeDefined();
        const state = store.getState();
        expect(state.count).toBe(0);
        expect(typeof state.increment).toBe('function');
    });
});