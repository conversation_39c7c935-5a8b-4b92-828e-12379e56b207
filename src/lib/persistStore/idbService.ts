import { openDB, deleteDB } from 'idb';
import { getUserInfo } from '../getUserInfo';

const DB_VERSION = 1;
const DB_PREFIX = 'installment-app';

export const getDBName = () => {
  const userId = getUserInfo()?.zalopay_id;
  return `${DB_PREFIX}-${userId}`;
};

export const initDB = async () => {
  const dbName = getDBName();
  
  return openDB(dbName, DB_VERSION, {
    upgrade(db) {
      // Tạo object store nếu chưa tồn tại
      if (!db.objectStoreNames.contains('persist-data')) {
        db.createObjectStore('persist-data', { keyPath: 'id' });
      }
    },
  });
};

export const saveData = async (storeKey: string, data: any) => {
  const db = await initDB();
  return db.put('persist-data', { id: storeKey, data });
};

export const getData = async (storeKey: string) => {
  const db = await initDB();
  const result = await db.get('persist-data', storeKey);
  return result?.data;
};

export const deleteData = async (storeKey: string) => {
  const db = await initDB();
  return db.delete('persist-data', storeKey);
};

export const deleteOldDB = async (oldUserId: string) => {
  try {
    await deleteDB(`${DB_PREFIX}-${oldUserId}`);
    console.log(`DB for user ${oldUserId} deleted successfully`);
  } catch (error) {
    console.error('Error deleting old database:', error);
  }
};