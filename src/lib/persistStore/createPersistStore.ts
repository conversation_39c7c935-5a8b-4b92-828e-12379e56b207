// utils/createPersistStore.ts
import { StateCreator, create } from "zustand";
import { persist, createJSONStorage, PersistOptions, PersistStorage } from "zustand/middleware";
import { getData, saveData, deleteData } from "./idbService";

export const createIDBStorage = <T>(storeName: string): PersistStorage<T> => {
  return createJSONStorage<T>(() => ({
    getItem: async (name: string) => {
      try {
        const key = name;
        const data = await getData(key);
        return data ? JSON.stringify(data) : null;
      } catch (error) {
        console.error(`Error getting data for ${storeName}:`, error);
        return null;
      }
    },
    setItem: async (name: string, value: string) => {
      try {
        const key = name;
        await saveData(key, JSON.parse(value));
      } catch (error) {
        console.error(`Error saving data for ${storeName}:`, error);
      }
    },
    removeItem: async (name: string,) => {
      try {
        const key = name;
        await deleteData(key);
      } catch (error) {
        console.error(`Error removing data for ${storeName}:`, error);
      }
    },
  }))!;
};

export function createPersistStore<T extends object>(
  storeName: string,
  initializer: StateCreator<T, [["zustand/persist", unknown]]>,
  persistOptions?: Partial<PersistOptions<T>>
) {
  return create<T>()(
    persist(initializer, {
      name: storeName,
      storage: createIDBStorage<T>(storeName),
      ...persistOptions,
    })
  );
}
