import { clsx, ClassValue } from "clsx";
import { extendTailwindMerge } from "tailwind-merge";
import { extendTheme } from "@/constants/extendTheme";

export const twMerge = extendTailwindMerge({
  extend: {
    classGroups: {
      'font-size': Object.keys(extendTheme.fontSize).map((key) => `text-${key}`),
    },
  },
});

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
