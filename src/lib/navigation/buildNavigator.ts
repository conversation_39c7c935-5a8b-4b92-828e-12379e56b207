import { useMemo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

export const useNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const formatValue = (value: string | null) => {
    if (!isNaN(Number(value))) {
      return Number(value);
    }

    if (value === 'true') {
      return true;
    }

    if (value === 'false') {
      return false;
    }

    if (value === 'undefined') {
      return undefined;
    }

    return value;
  };

  const navigation = useMemo(
    () => ({
      // TODO: Remove these usages of "any".
      navigate: (key: string, params?: any) => {
          return navigate('/' + key, {state: params, viewTransition: true });
       
      },
      replace: (key: string, params?: any) => {
          return navigate('/' + key, { replace: true, state: params, viewTransition: true });
      },
      goBack: () => navigate(-1),
      // TODO: Remove any.
      getParam: (name: string, defaultValue?: any) => {
        const params = new URLSearchParams(location.search);
        const state = (location.state || {}) as any;
        if (name in state) {
          return state[name];
        } else if (params.has(name)) {
          return formatValue(params.get(name));
        }
        return defaultValue;
      }
    }),
    [navigate, location],
  );

  return navigation;
};

export type TUseNavigation = ReturnType<typeof useNavigation>;
