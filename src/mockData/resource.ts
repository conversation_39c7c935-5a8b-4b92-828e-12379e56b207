import { ResourceTypes } from "../types";

export const RESOURCE_TYPES: ResourceTypes[] = [
  {
    type: 'JOB_TITLE',
    data: [
      {
        code: '01',
        vietnamese: 'Ch<PERSON> Tịch',
        english: 'Chairman',
      },
      {
        code: '02',
        vietnamese: 'PHÓ CHỦ TỊCH',
        english: 'VICE CHAIRMAN',
      },
      {
        code: '03',
        vietnamese: 'HIỆU TRƯỞNG',
        english: 'PRESIDENT',
      },
      {
        code: '04',
        vietnamese: 'PHÓ HIỆU TRƯỞNG',
        english: 'VICE PRESIDENT',
      },
      {
        code: '05',
        vietnamese: 'CỐ VẤN CAO CẤP',
        english: 'SENIOR ADVISOR',
      },
      {
        code: '06',
        vietnamese: 'GIÁM ĐỐC',
        english: 'DIRECTOR',
      },
      {
        code: '07',
        vietnamese: 'TỔNG GIÁM ĐỐC',
        english: 'GENERAL MANAGER',
      },
      {
        code: '08',
        vietnamese: 'TRƯỞNG BỘ PHẬN',
        english: 'HEAD OF DEPARTMENT',
      },
      {
        code: '09',
        vietnamese: 'QUẢN LÝ NGHIÊN CỨU & PHÁT TRIỂN',
        english: 'RESEARCH & DEVELOPMENT  MANAGER',
      },
      {
        code: '10',
        vietnamese: 'QUẢN LÝ MARKETING',
        english: 'MARKETING MANAGER',
      },
      {
        code: '11',
        vietnamese: 'THƯ KÝ',
        english: 'SECRETARY',
      },
      {
        code: '12',
        vietnamese: 'QUẢN LÝ SẢN XUẤT',
        english: 'MANUFACTURING MANAGER',
      },
      {
        code: '13',
        vietnamese: 'QUẢN LÝ KIỂM SOÁT SẢN PHẨM',
        english: 'PRODUCT CONTROL MANAGER',
      },
      {
        code: '14',
        vietnamese: 'QUẢN LÝ KỸ THUẬT',
        english: 'TECHNICAL MANAGER',
      },
      {
        code: '15',
        vietnamese: 'TRƯỞNG PHÒNG KẾ TOÁN',
        english: 'ACCOUNTING MANAGER',
      },
      {
        code: '16',
        vietnamese: 'GIÁM ĐỐC NHÀ MÁY',
        english: 'PLANT MANAGER',
      },
      {
        code: '17',
        vietnamese: 'QUẢN LÝ DỊCH VỤ',
        english: 'SERVICE MANAGER',
      },
      {
        code: '18',
        vietnamese: 'QUẢN LÝ DỰ ÁN',
        english: 'PRODUCT MANAGER',
      },
      {
        code: '19',
        vietnamese: 'QUẢN LÝ NHÂN SỰ',
        english: 'PERSONNEL MANAGER',
      },
      {
        code: '20',
        vietnamese: 'QUẢN LÝ BẢO HIỂM',
        english: 'ASSURANCE MANAGER',
      },
      {
        code: '21',
        vietnamese: 'QUẢN LÝ KIỂM SOÁT CHẤT LƯỢNG',
        english: 'QUALITY CONTORL MANAGER',
      },
      {
        code: '22',
        vietnamese: 'QUẢN LÝ KINH DOANH',
        english: 'SALES MANAGER',
      },
      {
        code: '23',
        vietnamese: 'QUẢN LÝ THU MUA',
        english: 'PURCHASING MANAGER',
      },
      {
        code: '24',
        vietnamese: 'NGƯỜI QUẢN LÝ TÀI CHÍNH',
        english: 'FINANCE MANAGER',
      },
      {
        code: '25',
        vietnamese: 'QUẢN LÝ XUẤT KHẨU',
        english: 'EXPORT MANAGER',
      },
      {
        code: '26',
        vietnamese: 'TÍN DỤNG VÀ NGƯỜI QUẢN LÝ PHÁP LÝ',
        english: 'CREDIT AND LEGAL MANAGER',
      },
      {
        code: '999',
        vietnamese: 'KHÁC',
        english: 'OTHERS',
      },
    ],
  },
  {
    type: 'OCCUPATION',
    data: [
      {
        code: '29',
        vietnamese: 'Công nhân',
        english: 'Worker',
      },
      {
        code: '40',
        vietnamese: 'Học sinh/ Sinh viên',
        english: 'Student',
      },
      {
        code: '34',
        vietnamese: 'Nhân viên kinh doanh',
        english: 'Sales',
      },
      {
        code: '72',
        vietnamese: 'Nhân viên văn phòng',
        english: 'Company staff',
      },
      {
        code: '04',
        vietnamese: 'Kỹ sư/ Kỹ thuật viên/ Lập trình viên',
        english: 'Engineer/Technician/Programmer',
      },
      {
        code: '20',
        vietnamese: 'Lao động tay nghề',
        english: 'Skilled Labour',
      },
      {
        code: '27',
        vietnamese: 'Tài xế/ Giao hàng',
        english: 'Driver/Transportation Worker',
      },
      {
        code: '39',
        vietnamese: 'Nội trợ',
        english: 'Housewife',
      },
      {
        code: '120',
        vietnamese: 'Làm việc tự do/Người nhận thu nhập từ hoa hồng',
        english: 'Self-Employed/Guide/Commission Earner',
      },
      {
        code: '03',
        vietnamese: 'Kế toán',
        english: 'Accountant',
      },
      {
        code: '88',
        vietnamese: 'Chủ cửa hàng tạp hóa/ Chủ gian hàng',
        english: 'Grocery Shop Owner/Hawker/Stall Owner',
      },
      {
        code: '46',
        vietnamese: 'Chủ cơ sở kinh doanh khác',
        english: 'Other Business Owner',
      },
      {
        code: '113',
        vietnamese: 'Chủ quán bar/club/nhà hàng',
        english: 'Bar/Club/Restaurant Owner',
      },
      {
        code: '114',
        vietnamese: 'Chủ chuỗi cửa hàng bán lẻ',
        english: 'Retail Store Owner',
      },
      {
        code: '55',
        vietnamese: 'Chủ đại lý du lịch/bãi xe/giặt ủi/dệt may',
        english: 'Travel Agency/Parking Outlet/Launderettes /Textile Owner',
      },
      {
        code: '52',
        vietnamese: 'Chủ tiệm cầm đồ/đổi tiền',
        english: 'Pawnshop/Money Changer Owner',
      },
      {
        code: '32',
        vietnamese: 'Chủ tịch/ Giám đốc/ Tổng giám đốc',
        english: 'Chairman/Co Director/CEO/COO/General Mgr',
      },
      {
        code: '16',
        vietnamese: 'Nhà báo/ Phóng viên',
        english: 'Journalist/Reporter',
      },
      {
        code: '61',
        vietnamese: 'Kiểm toán',
        english: 'Auditor',
      },
      {
        code: '77',
        vietnamese: 'Cá nhân có ảnh hưởng chính trị/Ngoại giao/ Thành viên quốc hội',
        english: 'Political Exposed Persons/Diplomat/Ambassador/ Minister',
      },
      {
        code: '45',
        vietnamese: 'Người môi giới / đấu giá',
        english: 'Auctioneer/ Dealer/ Broker',
      },
      {
        code: '98',
        vietnamese: 'Kinh doanh vàng bạc đá quý, dịch vụ chuyển tiền, trang phục xa xỉ',
        english: 'Money Remittance/Money Transmitter/ Jewels/ Gems/ Precious Metals/ Costume',
      },
      {
        code: '2210',
        vietnamese: 'Khác',
        english: 'Others',
      },
      {
        code: '01',
        vietnamese: 'Y bác sĩ/Nha sĩ/Dược sĩ',
        english: 'Healthcare professionals',
      },
    ],
  },
  {
    type: 'CITY',
    data: [
      {
        code: '001',
        vietnamese: 'Thành phố Hà Nội',
        english: 'Hanoi',
      },
      {
        code: '002',
        vietnamese: 'Tỉnh Hà Giang',
        english: 'Ha Giang',
      },
      {
        code: '004',
        vietnamese: 'Tỉnh Cao Bằng',
        english: 'Cao Bang',
      },
      {
        code: '006',
        vietnamese: 'Tỉnh Bắc Kạn',
        english: 'Bac Kan',
      },
      {
        code: '008',
        vietnamese: 'Tỉnh Tuyên Quang',
        english: 'Tuyen Quang',
      },
      {
        code: '010',
        vietnamese: 'Tỉnh Lào Cai',
        english: 'Lao Cai',
      },
      {
        code: '011',
        vietnamese: 'Tỉnh Điện Biên',
        english: 'Dien Bien',
      },
      {
        code: '012',
        vietnamese: 'Tỉnh Lai Châu',
        english: 'Lai Chau',
      },
      {
        code: '014',
        vietnamese: 'Tỉnh Sơn La',
        english: 'Son La',
      },
      {
        code: '015',
        vietnamese: 'Tỉnh Yên Bái',
        english: 'Yen Bai',
      },
      {
        code: '017',
        vietnamese: 'Tỉnh Hoà Bình',
        english: 'Hoa Binh',
      },
      {
        code: '019',
        vietnamese: 'Tỉnh Thái Nguyên',
        english: 'Thai Nguyen',
      },
      {
        code: '020',
        vietnamese: 'Tỉnh Lạng Sơn',
        english: 'Lang Son',
      },
      {
        code: '022',
        vietnamese: 'Tỉnh Quảng Ninh',
        english: 'Quang Ninh',
      },
      {
        code: '024',
        vietnamese: 'Tỉnh Bắc Giang',
        english: 'Bac Giang',
      },
      {
        code: '025',
        vietnamese: 'Tỉnh Phú Thọ',
        english: 'Phu Tho',
      },
      {
        code: '026',
        vietnamese: 'Tỉnh Vĩnh Phúc',
        english: 'Vinh Phuc',
      },
      {
        code: '027',
        vietnamese: 'Tỉnh Bắc Ninh',
        english: 'Bac Ninh',
      },
      {
        code: '030',
        vietnamese: 'Tỉnh Hải Dương',
        english: 'Hai Duong',
      },
      {
        code: '031',
        vietnamese: 'Thành phố Hải Phòng',
        english: 'Hai Phong',
      },
      {
        code: '033',
        vietnamese: 'Tỉnh Hưng Yên',
        english: 'Hung Yen',
      },
      {
        code: '034',
        vietnamese: 'Tỉnh Thái Bình',
        english: 'Thai Binh',
      },
      {
        code: '035',
        vietnamese: 'Tỉnh Hà Nam',
        english: 'Ha Nam',
      },
      {
        code: '036',
        vietnamese: 'Tỉnh Nam Định',
        english: 'Nam Dinh',
      },
      {
        code: '037',
        vietnamese: 'Tỉnh Ninh Bình',
        english: 'Ninh Binh',
      },
      {
        code: '038',
        vietnamese: 'Tỉnh Thanh Hóa',
        english: 'Thanh Hoa',
      },
      {
        code: '040',
        vietnamese: 'Tỉnh Nghệ An',
        english: 'Nghe An',
      },
      {
        code: '042',
        vietnamese: 'Tỉnh Hà Tĩnh',
        english: 'Ha Tinh',
      },
      {
        code: '044',
        vietnamese: 'Tỉnh Quảng Bình',
        english: 'Quang Binh',
      },
      {
        code: '045',
        vietnamese: 'Tỉnh Quảng Trị',
        english: 'Quang Tri',
      },
      {
        code: '046',
        vietnamese: 'Tỉnh Thừa Thiên Huế',
        english: 'Thua Thien Hue',
      },
      {
        code: '048',
        vietnamese: 'Thành phố Đà Nẵng',
        english: 'Da Nang',
      },
      {
        code: '049',
        vietnamese: 'Tỉnh Quảng Nam',
        english: 'Quang Nam',
      },
      {
        code: '051',
        vietnamese: 'Tỉnh Quảng Ngãi',
        english: 'Quang Ngai',
      },
      {
        code: '052',
        vietnamese: 'Tỉnh Bình Định',
        english: 'Binh Dinh',
      },
      {
        code: '054',
        vietnamese: 'Tỉnh Phú Yên',
        english: 'Phu Yen',
      },
      {
        code: '056',
        vietnamese: 'Tỉnh Khánh Hòa',
        english: 'Khanh Hoa',
      },
      {
        code: '058',
        vietnamese: 'Tỉnh Ninh Thuận',
        english: 'Ninh Thuan',
      },
      {
        code: '060',
        vietnamese: 'Tỉnh Bình Thuận',
        english: 'Binh Thuan',
      },
      {
        code: '062',
        vietnamese: 'Tỉnh Kon Tum',
        english: 'Kon Tum',
      },
      {
        code: '064',
        vietnamese: 'Tỉnh Gia Lai',
        english: 'Gia Lai',
      },
      {
        code: '066',
        vietnamese: 'Tỉnh Đắk Lắk',
        english: 'Dak Lak',
      },
      {
        code: '067',
        vietnamese: 'Tỉnh Đắk Nông',
        english: 'Dak Nong',
      },
      {
        code: '068',
        vietnamese: 'Tỉnh Lâm Đồng',
        english: 'Lam Dong',
      },
      {
        code: '070',
        vietnamese: 'Tỉnh Bình Phước',
        english: 'Binh Phuoc',
      },
      {
        code: '072',
        vietnamese: 'Tỉnh Tây Ninh',
        english: 'Tay Ninh',
      },
      {
        code: '074',
        vietnamese: 'Tỉnh Bình Dương',
        english: 'Binh Duong',
      },
      {
        code: '075',
        vietnamese: 'Tỉnh Đồng Nai',
        english: 'Dong Nai',
      },
      {
        code: '077',
        vietnamese: 'Tỉnh Bà Rịa - Vũng Tàu',
        english: 'Ba Ria - Vung Tau',
      },
      {
        code: '079',
        vietnamese: 'Thành phố Hồ Chí Minh',
        english: 'Ho Chi Minh City',
      },
      {
        code: '080',
        vietnamese: 'Tỉnh Long An',
        english: 'Long An',
      },
      {
        code: '082',
        vietnamese: 'Tỉnh Tiền Giang',
        english: 'Tien Giang',
      },
      {
        code: '083',
        vietnamese: 'Tỉnh Bến Tre',
        english: 'Ben Tre',
      },
      {
        code: '084',
        vietnamese: 'Tỉnh Trà Vinh',
        english: 'Tra Vinh',
      },
      {
        code: '086',
        vietnamese: 'Tỉnh Vĩnh Long',
        english: 'Vinh Long',
      },
      {
        code: '087',
        vietnamese: 'Tỉnh Đồng Tháp',
        english: 'Dong Thap',
      },
      {
        code: '089',
        vietnamese: 'Tỉnh An Giang',
        english: 'An Giang',
      },
      {
        code: '091',
        vietnamese: 'Tỉnh Kiên Giang',
        english: 'Kien Giang',
      },
      {
        code: '092',
        vietnamese: 'Thành phố Cần Thơ',
        english: 'Can Tho',
      },
      {
        code: '093',
        vietnamese: 'Tỉnh Hậu Giang',
        english: 'Hau Giang',
      },
      {
        code: '094',
        vietnamese: 'Tỉnh Sóc Trăng',
        english: 'Soc Trang',
      },
      {
        code: '095',
        vietnamese: 'Tỉnh Bạc Liêu',
        english: 'Bac Lieu',
      },
      {
        code: '096',
        vietnamese: 'Tỉnh Cà Mau',
        english: 'Ca Mau',
      },
      {
        code: '097',
        vietnamese: 'Tp Thủ Đức',
        english: 'THU DUC CITY',
      },
      {
        code: 'rachel_code',
        vietnamese: 'Rachel VN',
        english: 'Rachel EN',
      },
    ],
  },
];
