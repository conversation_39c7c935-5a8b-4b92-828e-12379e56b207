import { ReactNode, ErrorInfo, Suspense, ComponentType, Component } from "react";
import { WarningCard } from "../components/WarningCard";
import { Button } from "../components/ui/Button";

export const withImportFederatedModule =
  <T extends {}>(
    Component: ComponentType<any>,
  ) =>
  // eslint-disable-next-line react/display-name
  (props: T & Omit<FederatedWrapperProps, "children">) =>
    (
      <FederatedWrapper error={props.error} fallback={props.fallback}>
        <Component {...props} />
      </FederatedWrapper>
    );

type FederatedWrapperProps = {
  error?: ReactNode;
  fallback?: ReactNode;
  children: ReactNode;
};

type FederatedWrapperState = {
  hasError: boolean;
};

class FederatedWrapper extends Component<FederatedWrapperProps, FederatedWrapperState> {
  constructor(props: FederatedWrapperProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): FederatedWrapperState {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.log("Loading federated module error: ", errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        this.props.error || (
          <div className="flex flex-col gap-3 mx-auto items-center">
          <WarningCard
            title="Có lỗi xảy ra khi tải thông tin."
            description={`Xin lỗi vì sự bất tiện này, bạn vui lòng bấm "Thử lại" để tải lại thông tin.`}
          />
          <Button className="w-36" variant="outlined" onClick={() => {this.setState({ hasError: false})}}>Thử lại</Button>
          </div>
        )
      );
    }

    return <Suspense fallback={this.props.fallback || null}>{this.props.children}</Suspense>;
  }
}
