import { Component, ComponentType, ReactElement } from 'react';

export interface ErrorHandlerState {
  error: Error | null;
}
class ErrorBoundary extends Component<
  {
    children: ReactElement;
    fallback?: ReactElement;
  },
  {
    hasError: boolean;
    error: Error | null;
  }
> {

  state = { hasError: false, error: null };


  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: any, errorInfo: any) {
    window?.Sentry?.captureException(error);
    console.error(error, errorInfo);
  }
  
  render() {
    if (this.state.hasError) {
      return this.props.fallback ? this.props.fallback : <></>;
    }

    return this.props.children;
  }
}

export default function withErrorBoundary(
  Component: ComponentType<any>,
  ErrorComponent?: ReactElement,
) {
  return function WithErrorBoundary(props: any) {
    return (
      <ErrorBoundary fallback={ErrorComponent}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };
}
