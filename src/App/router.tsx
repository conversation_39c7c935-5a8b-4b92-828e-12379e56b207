import { createBrowserRouter, RouteObject } from "react-router-dom";
import { ScreenKey } from "@/constants";
import loadable from "@loadable/component";
import { buildPath } from "@/utils/buildPath";
import Layout from "./_layout";
import { rootBase } from "@/shared/environment";

const HomeScreenLazy = loadable(() => import("@/screens/HomeScreen"));
const SplashScreenLazy = loadable(() => import("@/screens/SplashScreen"));
const OnboardingScreenLazy = loadable(() => import("@/screens/OnboardingScreen"));
const NonWhitelistScreenLazy = loadable(() => import("@/components/NonWhitelistUI"), {
  resolveComponent: (components) => components.NonWhitelistUI,
});
const HistoryScreenLazy = loadable(() => import("@/screens/HistoryScreen"));
const AccountScreenLazy = loadable(() => import("@/screens/AccountScreen"));
const RepaymentScreenLazy = loadable(() => import("@/screens/RepaymentScreen"));
const StatementScreenLazy = loadable(() => import("@/screens/StatementScreen"));
const TransactionScreenLazy = loadable(() => import("@/screens/TransactionScreen"));
const FAQMainScreenLazy = loadable(() => import("@/screens/FAQScreen/FAQMainScreen"));
const FAQDetailScreenLazy = loadable(() => import("@/screens/FAQScreen/FAQDetailScreen"));
const ErrorScreenLazy = loadable(() => import("@/screens/ErrorScreen"));


const ErrorPage = () => {
  return (
    <div>
      <h2>404 - Page Not Found</h2>
      <p>The page you&apos;re looking for doesn&apos;t exist.</p>
    </div>
  );
};

const mainRoutes: RouteObject[] = [
  {
    path: buildPath(ScreenKey.HomeScreen),
    element: <HomeScreenLazy />
  },
  {
    path: buildPath(ScreenKey.SplashScreen),
    element: <SplashScreenLazy />
  },
  {
    path: buildPath(ScreenKey.NonWhitelistScreen),
    element: <NonWhitelistScreenLazy />
  },
  {
    path: buildPath(ScreenKey.OnboardingScreen),
    element: <OnboardingScreenLazy />
  },
  {
    path: buildPath(ScreenKey.HistoryScreen),
    element: <HistoryScreenLazy />,
  },
  {
    path: buildPath(ScreenKey.AccountScreen),
    element: <AccountScreenLazy />,
  },
  {
    path: buildPath(ScreenKey.RepaymentScreen),
    element: <RepaymentScreenLazy />,
  },
  {
    path: buildPath(ScreenKey.StatementScreen),
    element: <StatementScreenLazy />,
  },
  {
    path: buildPath(`${ScreenKey.TransactionScreen}/:id`),
    element: <TransactionScreenLazy />,
  },
  {
    path: buildPath(ScreenKey.FAQMainScreen),
    element: <FAQMainScreenLazy />,
  },
  {
    path: buildPath(ScreenKey.FAQDetailScreen),
    element: <FAQDetailScreenLazy />,
  },
  {
    path: buildPath(`${ScreenKey.Error}`),
    element: <ErrorScreenLazy />,
  },
]
const routes: RouteObject[] = [
  {
    path: "/",
    element: <Layout />,
    errorElement: <ErrorPage />,
    children: mainRoutes
  }
];
const router = createBrowserRouter(routes, {
  basename: rootBase,
});

export default router;
