import { FC, useEffect, useLayoutEffect } from "react";

import { GlobalDrawer, GlobalDrawerProvider } from "@/components/GlobalDrawer";
import { MaintenanceUI } from "@/components/MaintenanceUI";
import { Toaster } from "@/components/ui/Toaster";
import { getAppInfo } from "@/lib/ZalopaySDK";
import { closeWindow } from "@/lib/ZalopaySDK/closeWindow";
import { setEnvironment, setModuleId } from "@/shared/environment";
import { AppStore, appStore } from "@/store/appStore";
import { utmStore } from "@/store/utmStore";
import { MaintenanceType } from "@/types/maintenanceInfo";

import { Navigator } from "./Navigator";
import { HapticProvider } from "@/providers/HapticProvider";

type Props = {
  appid: number;
  environment: number;
  /**
   * @ios
   */
  moduleId?: number;
  utm_campaign?: string;
  utm_source?: string;
  utm_toast?: string;
};

export const Root = (props: Props) => {
  const { setUtmCampaign, setUtmSource } = utmStore.getState();
  const { setAppInfo, setMaintananceInfo } = appStore.getState() as AppStore;
  const appInfo = appStore((state) => state.appInfo);
  const maintenanceInfo = appStore((state) => state.maintenanceInfo);

  useLayoutEffect(() => {
    setEnvironment(props.environment);
  }, [props.environment]);

  useEffect(() => {
    setModuleId(props.moduleId);
  }, [props.moduleId]);

  useEffect(() => {
    props?.utm_campaign && setUtmCampaign(props?.utm_campaign);
    props?.utm_source && setUtmSource(props?.utm_source);
  }, [props, setUtmCampaign, setUtmSource]);

  useEffect(() => {
    if (appInfo === null) {
      (async () => {
        const result = await getAppInfo();
        result?.status === "success" && setAppInfo(result?.data);
      })();
    }
  }, [appInfo]);

  if (maintenanceInfo) {
    if (maintenanceInfo.type === MaintenanceType.PAGE) {
      // Fullscreen maintanance page
      return <MaintenanceUI {...maintenanceInfo} className="justify-center h-screen" onClose={closeWindow} />;
    }
    GlobalDrawer.open({
      shouldScaleBackground: false,
      dismissible: true,
      children: (
        <MaintenanceUI
          {...maintenanceInfo}
          onClose={() => {
            GlobalDrawer.close();
            setMaintananceInfo(undefined);
          }}
        />
      ),
    });
  }

  return (
    <div className="text-base installment-app-container">
      <HapticProvider>
        <Navigator>
          <Toaster position="top-center" visibleToasts={1} offset={24} />
          <GlobalDrawerProvider />
        </Navigator>
      </HapticProvider>
    </div>
  );
};
