/* eslint-disable react/no-unknown-property */
import { useMemo } from "react";
import { Outlet } from "react-router-dom";

import Navbar, { INavItem } from "@/components/Navbar";
import { ScreenKey } from "@/constants";
import { colors } from "@/constants/colors";
import { PermissionProvider } from "@/providers/PermissionProvider";
import { buildPath } from "@/utils/buildPath";
import {
  GeneralHistorySecondary,
  GeneralHomeIc24,
  GeneralHomeSecondary,
  GeneralNavbarProfileLineSecondary,
  GeneralNavbarProfileSolidSecondary,
} from "@zpi/looknfeel-icons";

export const Layout = () => {
  const navItems: INavItem[] = useMemo(
    () => [
      {
        key: ScreenKey.HomeScreen,
        label: "Trang chủ",
        path: buildPath(ScreenKey.HomeScreen),
        activeIcon: <GeneralHomeIc24 viewBox="0 0 24 24" className="w-6 h-6" color={colors.primary.blue} />,
        inActiveIcon: <GeneralHomeSecondary viewBox="0 0 24 24" className="w-6 h-6" />,
      },
      {
        key: ScreenKey.HistoryScreen,
        label: "<PERSON>ị<PERSON> sử",
        path: buildPath(ScreenKey.HistoryScreen),
        activeIcon: <GeneralHistorySecondary className="bg-primary rounded-lg pr-0.5 w-6 h-6" color={colors.white} />,
        inActiveIcon: <GeneralHistorySecondary className="w-6 h-6" />,
      },
      {
        key: ScreenKey.AccountScreen,
        label: "Tài khoản",
        path: buildPath(ScreenKey.AccountScreen),
        activeIcon: <GeneralNavbarProfileSolidSecondary className="w-6 h-6" color={colors.primary.blue} />,
        inActiveIcon: <GeneralNavbarProfileLineSecondary className="w-6 h-6" />,
      },
    ],
    []
  );
  return (
    <PermissionProvider>      
      <main vaul-drawer-wrapper="">
        <Outlet />
      </main>
      <Navbar navItems={navItems} direction="row" />
    </PermissionProvider>
  );
};

export default Layout;
