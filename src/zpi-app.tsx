import React from "react";
import ReactDOMClient from "react-dom/client";
import "./styles/index.scss";
import { AppWrapper } from "./App";
import singleSpaReact from "single-spa-react";

const reactLifecycles = singleSpaReact({
  React,
  ReactDOMClient,
  rootComponent: AppWrapper,
  domElementGetter: () => {
    return document.getElementById("root")!;
  },
  errorBoundary(err, info, props) {
    return <div className="flex items-center justify-between h-16 px-6 text-white bg-primary">Ứng dụng chưa có sẵn</div>;
  },
});

export const bootstrap = reactLifecycles.bootstrap;

export const mount = reactLifecycles.mount;

export const unmount = reactLifecycles.unmount;
