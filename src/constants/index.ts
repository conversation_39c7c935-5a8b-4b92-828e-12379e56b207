export * from "./utm";
export const APP_DISPLAY_NAME = "Trả góp";
export const APP_NAME = "instalment";
export const APP_ELEMENT_ID = "installment-app-root";
export const NAVBAR_HEIGHT = 4; //rem ~64px
export const BASE_FONT_SIZE = 16; //px
export enum PartnerCode {
  CIMB = "CIMB",
}
export const APP_ID = "2263"; // aka DEEPLINK ID

export enum ScreenKey {
  SplashScreen = "SplashScreen",
  Error = "Error",
  HomeScreen = "HomeScreen",
  OnboardingScreen = "OnboardingScreen",
  NonWhitelistScreen = "NonWhitelistScreen",
  HistoryScreen = "HistoryScreen",
  AccountScreen = "AccountScreen",
  RepaymentScreen = "RepaymentScreen",
  StatementScreen = "StatementScreen",
  TransactionScreen = "TransactionScreen",
  FAQMainScreen = "FAQMainScreen",
  FAQDetailScreen = "FAQDetailScreen",
}

export const EKYC_URL = "/kyc/e-kyc";
export const EKYC_DEEPLINK = "zalopay://launch/f/kyc";
export const ADJUST_KYC_DEEPLINK = "zalopay://launch/f/adjust-kyc";
export const NFC_URL = "/kyc/nfc";
export const NFC_DEEPLINK = "zalopay://launch/f/identity-verification";
export const ZLP_PRIVACY_POLICY = "https://zalopay.vn/quy-dinh/chinh-sach-bao-ve-quyen-rieng-tu-hop-nhat";
export const ZLP_CS_URL = "/faq/services";
export const ZLP_CS_DEEPLINK = "zalopay://launch/app/-68";
export const CIMB_POLICY_CDN_URL =
  "https://simg.zalopay.com.vn/fs/Installment/pdf/Installment_TnC_Effective_28102024.pdf";
export const UM_UPDATE_PHONE_DEEPLINK = "zalopay://launch/f/update-phone-number";
export const UM_UPDATE_PHONE_URL = "/kyc/phone/change"; //ZMP link: https://zalo.me/s/***************7250/kyc/phone/change
export const CIMB_TEL = "1900969696";
export const pdfViewerUrl = "https://scdn.zalopay.vn/mini-app/viewer-pdf/0.0.9/web/viewer.html";

export enum PLATFORM {
  ZPI = "ZPI",
  ZPA = "ZPA",
}

export enum OS {
  IOS = "IOS",
  ANDROID = "ANDROID",
  WEB = "WEB",
}

export const ONBOARDING_STEP = [
  {
    step: 1,
    title: "Đăng ký",
  },
  {
    step: 2,
    title: "Ký hợp đồng",
  },
  {
    step: 3,
    title: "Duyệt hợp đồng",
  },
];

export enum UMAuthType {
  Unspecified = 0,
  PIN = 1,
  Bio = 2,
  OTP = 3,
  FaceAuth = 4,
  KYC = 5,
  Adjust = 6,
  NFC = 7,
  KYC_NFC = 8,
  Adjust_NFC = 9,
  Reset_NFC = 14,
}

export enum UMStatus {
  Invalid = -1,
  Cancel = 0,
  Success = 1,
  Failure = 2,
  Pending = 3,
}

export enum OTPType {
  CONTRACT_SIGNING = "CONTRACT_SIGNING",
  LINK_TYPE_3 = "LINK_TYPE_3",
}
