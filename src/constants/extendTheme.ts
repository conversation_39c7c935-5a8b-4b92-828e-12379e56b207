export const extendTheme = {
  transitionProperty: {
    width: "width",
  },
  fontSize: {
    supertiny: ["0.625rem", "0.75rem"], // 10px 12px
    tiny: ["0.75rem", "1rem"], // 12px 16px
    base: ["0.875rem", "1.125rem"], // 14px 18px
    lead: ["1rem", "1.25rem"], //16px 20px,
  },
  animation: {
    fillWidth: "fill-width 3s ease-in-out forwards",
    "marquee-left": "marquee-left var(--duration, 40s) linear infinite",
    "marquee-up": "marquee-up var(--duration, 40s) linear infinite",
    "slide-in": "slide-in 1s ease var(--slide-in-delay, 0) forwards",
    "force-in": "force-in var(--force-in-delay, 0.4s) cubic-bezier(0.22, 1, 0.36, 1) forwards",
    "image-show": "image-show 0.2s ease-in-out",
    "blur-out": "blurOut var(--duration, 0.15s) ease-in forwards"
  },
  keyframes: {
    "fill-width": {
      "0%": { width: 0 },
      "100%": { width: "100%" },
    },
    "marquee-left": {
      from: { transform: "translateX(0)" },
      to: { transform: "translateX(calc(-100% - var(--gap)))" },
    },
    "marquee-up": {
      from: { transform: "translateY(0)" },
      to: { transform: "translateY(calc(-100% - var(--gap)))" },
    },
    "slide-in": {
      from: {
        opacity: "0",
        transform: "translateY(-10px)",
      },
      to: {
        opacity: "1",
        transform: "translateY(0)",
      },
    },
    "force-in": {
      from: {
        opacity: 0.8,
        transform: "translateY(var(--force-in-translateY, 10px))",
        filter: "blur(var(--blurEnterFrom, 4px))",
      },
      to: {
        opacity: "1",
        transform: "translateY(0)",
        filter: "blur(--blurEnterTo, 0)",
      },
    },
    "image-show": {
      from: {
        filter: "blur(2px)",
      },
      to: {
        filter: "blur(0)",
      },
    },
    "blurOut": {
      from: {
        filter: "blur(4px)",
      },
      to: {
        filter: "blur(0)",
      }
    }
  },
  backgroundImage: {
    "gradient-onboarding": "linear-gradient(180deg, #0068FF 0%, #005CE2 100%)",
    "gradient-border-banner":
      "linear-gradient(246.72deg, #A1A1A1 2.79%, #CEE5FF 15.14%, #93F9FF 34.32%, #36FFC3 61.26%, #ECECEC 75.61%)",
    "gradient-banner": "linear-gradient(-10deg, #FFFFFF -10%, #45FFF4 25%, #FFF4D6 80%)",
    "gradient-border-asset":
      "linear-gradient(222deg, #0032AE 2.79%, #2079DD 15.14%, #93F9FF 34.32%, #36FFC3 61.26%, #0A48BD 75.61%)",
    "gradient-asset": "linear-gradient(351.16deg, #002B95 37.07%, #0032AE 57.01%, #0056FF 95.87%)",
    "gradient-home":
      "linear-gradient(180deg, rgb(255, 241, 241) 10%, hsla(68, 100%, 73%, 0.3) 50%, hsla(68, 100%, 73%, 0))",
  },
  screens: {
    shortScreen: { raw: "(max-height: 667px)" },
  },
  padding: {
    "4.5": "1.125rem", //18px
  },
  margin: {
    "4.5": "1.125rem", //18px
  },
  borderWidth: {
    "0.5": "0.125rem",
  },
  borderRadius: {
    "extra-lg": "0.625rem", //10px
    xxl: "1.25rem", //20px
  },
  height: {
    "7.5": "1.875rem",
  },
};
