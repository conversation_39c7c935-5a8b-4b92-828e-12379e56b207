/**
 * HapticProvider
 *
 * This React provider component enables global haptic feedback for its child components.
 *
 * Usage:
 *   Wrap your application (or a subtree) with <HapticProvider> to automatically provide haptic feedback
 *   for user interactions on elements that have a `data-haptic` attribute.
 *
 * How it works:
 *   - Listens for 'click' and 'input' events on the document.
 *   - If the event target or its closest parent has a `data-haptic` attribute, it triggers the corresponding haptic feedback.
 *   - Supports use cases like buttons, toggles, range sliders, and custom components.
 *
 * Example:
 *   <button data-haptic="light">Click me</button>
 *   <input type="checkbox" data-haptic="selection" />
 *   <input type="range" data-haptic="light" />
 *
 * This provider does not render any UI of its own—only its children.
 * Place it high in your component tree to enable haptic feedback app-wide.
 */
import { useEffect } from 'react';
import { PlayHapticType, HAPTIC_MAPPINGS, playHaptic } from '@/lib/ZalopaySDK/device/playHaptic';

export const HapticProvider = ({ children }: { children?: React.ReactNode }) => {
  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const hapticType = target?.closest('[data-haptic]')?.getAttribute('data-haptic') as PlayHapticType | null;
      if (hapticType && HAPTIC_MAPPINGS[hapticType]) {
        playHaptic({ type: hapticType });
      }
    };

    const handleInput = (e: Event) => {
      const target = e.target as HTMLElement;
      const hapticType = target?.closest('[data-haptic]')?.getAttribute('data-haptic') as PlayHapticType | null;
      if (hapticType && HAPTIC_MAPPINGS[hapticType]) {
        playHaptic({ type: hapticType });
      }
    };

    document.addEventListener('click', handleClick);
    document.addEventListener('input', handleInput);
    return () => {
      document.removeEventListener('click', handleClick);
      document.removeEventListener('input', handleInput);
    };
  }, []);

  return <>{children}</>;
};