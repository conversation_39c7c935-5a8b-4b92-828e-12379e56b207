import { render, waitFor } from '@testing-library/react';
import { PermissionProvider, usePermission } from '../PermissionProvider';
import { accountStore } from '@/store/accountStore';
import { ScreenKey } from '@/constants';
import { BindStatus } from '@/types/bindStatus';
import { KycStatus } from '@/types/kycStatus';

// Mock các dependencies
jest.mock('@/hooks/useTracking', () => ({
  useTracking: () => ({
    trackEvent: jest.fn(),
  }),
}));

jest.mock('@/lib/navigation/buildNavigator', () => ({
  useNavigation: () => ({
    replace: jest.fn(),
  }),
}));

jest.mock('@/shared/environment', () => ({
  getCurrentPath: jest.fn(),
}));

describe('PermissionProvider', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render children when ready', async () => {
    const TestComponent = () => {
      const { isLoading } = usePermission();
      return <div>{isLoading ? 'Loading' : 'Ready'}</div>;
    };

    const { getByText } = render(
      <PermissionProvider>
        <TestComponent />
      </PermissionProvider>
    );

    await waitFor(() => {
      expect(getByText('Ready')).toBeInTheDocument();
    });
  });

  it('should handle null permission info', async () => {
    // Mock accountStore
    jest.spyOn(accountStore.getState(), 'initPermission').mockResolvedValue(null);

    const TestComponent = () => {
      const { isLoading } = usePermission();
      return <div>{isLoading ? 'Loading' : 'Ready'}</div>;
    };

    const { getByText } = render(
      <PermissionProvider>
        <TestComponent />
      </PermissionProvider>
    );

    await waitFor(() => {
      expect(getByText('Ready')).toBeInTheDocument();
    });
  });

  it('should handle bound user with whitelist', async () => {
    const mockPermissionInfo = {
      bind_status: BindStatus.BIND_STATUS_BOUND,
      is_whitelisted: true,
      kyc_status: { status: 'APPROVED' }
    };

    jest.spyOn(accountStore.getState(), 'initPermission').mockResolvedValue({
      ...mockPermissionInfo,
      kyc_status: { status: KycStatus.KYC_STATUS_APPROVE }
    });
    jest.spyOn(accountStore.getState(), 'getAccountInfo').mockResolvedValue();
    jest.spyOn(accountStore.getState(), 'permissionNavigateHandler').mockReturnValue(ScreenKey.HomeScreen);

    const TestComponent = () => {
      const { isLoading } = usePermission();
      return <div>{isLoading ? 'Loading' : 'Ready'}</div>;
    };

    const { getByText } = render(
      <PermissionProvider>
        <TestComponent />
      </PermissionProvider>
    );

    await waitFor(() => {
      expect(getByText('Ready')).toBeInTheDocument();
    });
  });

  it('should handle non-whitelisted user', async () => {
    const mockPermissionInfo = {
      bind_status: BindStatus.BIND_STATUS_BOUND,
      is_whitelisted: false,
      kyc_status: { status: KycStatus.KYC_STATUS_APPROVE }
    };

    jest.spyOn(accountStore.getState(), 'initPermission').mockResolvedValue(mockPermissionInfo);

    const TestComponent = () => {
      const { isLoading } = usePermission();
      return <div>{isLoading ? 'Loading' : 'Ready'}</div>;
    };

    const { getByText } = render(
      <PermissionProvider>
        <TestComponent />
      </PermissionProvider>
    );

    await waitFor(() => {
      expect(getByText('Ready')).toBeInTheDocument();
    });
  });

  it('should handle error case', async () => {
    jest.spyOn(accountStore.getState(), 'initPermission').mockRejectedValue(new Error('Test error'));

    const TestComponent = () => {
      const { isLoading } = usePermission();
      return <div>{isLoading ? 'Loading' : 'Ready'}</div>;
    };

    const { getByText } = render(
      <PermissionProvider>
        <TestComponent />
      </PermissionProvider>
    );

    await waitFor(() => {
      expect(getByText('Ready')).toBeInTheDocument();
    });
  });
});