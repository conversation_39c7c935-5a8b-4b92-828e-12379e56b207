import React, { createContext, useContext, useEffect, useRef, useState } from "react";

import { PartnerCode, ScreenKey, UTMCampaign, UTMParameters } from "@/constants";
import { useTracking } from "@/hooks/useTracking";
import { useNavigation } from "@/lib/navigation/buildNavigator";
import { ScreenId, SplashScreen as SplashScreenTracking } from "@/screens/SplashScreen/SplashScreenTrackingId";
import { getCurrentPath } from "@/shared/environment";
import { accountStore } from "@/store/accountStore";
import { statementStore } from "@/store/statementStore";
import { utmStore } from "@/store/utmStore";
import { AppAction } from "@/types";
import { BindStatus } from "@/types/bindStatus";
import { getParameterByName } from "@/utils/getParameterByName";
import { getTargetScreenFromUTMCampaign } from "@/utils/getTargetScreenFromUTMCampaign";
import { onboardingStore } from "@/store/onboardingStore";
import { PermissionInfo } from "@/types/permission";
import SplashScreen from "@/screens/SplashScreen";

interface PermissionContextType {
  isLoading: boolean;
  preparePermission: () => Promise<void>;
}

const PermissionContext = createContext<PermissionContextType | undefined>(undefined);

export const PermissionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [ready, setReady] = useState(false);
  const navigation = useNavigation();
  const trackEvent = useTracking(ScreenId.SplashScreen).trackEvent;
  const initPermission = accountStore.getState().initPermission;
  
  const navigateWithTracking = (screen: string, permissionInfo?: any) => {
    trackEvent(SplashScreenTracking.Navigate, {
      utm_source: utmStore.getState().utmSource,
      permission_status: permissionInfo?.kyc_status?.status,
      routing_desc: screen,
    });
    navigation.replace(screen);
  };

  const UTMNavigateHandler = (utmCampaign: string) => {
    let targetScreen = getTargetScreenFromUTMCampaign(utmCampaign);
    if (!targetScreen) {
      return "";
    }

    if (utmCampaign === UTMCampaign.EarlyDischarge) {
      const transId = getParameterByName("transId");
      targetScreen = `${targetScreen}/${transId}?action=${AppAction.OPEN_EARLY_DISCHARGE_BOTTOM_SHEET}`;
    }
    return targetScreen;
  };

  const preparePermission = async () => {
    try {
      setIsLoading(true);
      trackEvent(SplashScreenTracking.LoadSplashScreen, { utm_source: utmStore.getState().utmSource });
      await accountStore.persist.rehydrate();
      let permissionInfo = accountStore.getState().permissionInfo;
      if (permissionInfo) {
        initPermission();
      } else {
        permissionInfo = await initPermission();
      }
      await prepareRouting(permissionInfo);

    } catch (error) {
      console.error("Navigation error:", error);
      navigateWithTracking(ScreenKey.Error);
    } finally {
      setIsLoading(false);
      setReady(true);
    }
  };

  const prepareRouting = async (permissionInfo?: PermissionInfo | null) => {
    if (permissionInfo === undefined) {
      return;
    }

    if (permissionInfo === null) {
      return navigateWithTracking(ScreenKey.Error);
    }

    if (permissionInfo?.bind_status === BindStatus.BIND_STATUS_BOUND) {
      await accountStore.getState().getAccountInfo();
      const accountId = accountStore.getState()?.accountInfo?.account_id;
      accountId && (await statementStore.getState().getLastestStatementInfo(accountId));

      // UTM flow
      const utmCampaign = getParameterByName(UTMParameters.UTMCampaign);
      if (utmCampaign) {
        const UTMTargetScreen = UTMNavigateHandler(utmCampaign);
        if (UTMTargetScreen) {
          let params = "";
          if (utmCampaign === UTMCampaign.EarlyDischarge) {
            const transId = getParameterByName("transId");
            params = `/${transId}?action=${AppAction.OPEN_EARLY_DISCHARGE_BOTTOM_SHEET}`;
          }

          return navigateWithTracking(UTMTargetScreen + params, permissionInfo);
        }
      }
    }

    // Whitelist check
    if (!permissionInfo?.is_whitelisted) {
      trackEvent(SplashScreenTracking.NonWhitelist, {
        whitelisted: false,
        partner: PartnerCode.CIMB,
        utm_source: utmStore.getState().utmSource,
        routing_desc: ScreenKey.NonWhitelistScreen,
        permission_status: permissionInfo?.kyc_status.status,
      });
      return navigateWithTracking(ScreenKey.NonWhitelistScreen, permissionInfo);
    }

    // get onboarding status
    if (permissionInfo?.bind_status === BindStatus.BIND_STATUS_ONBOARDING) {
      await onboardingStore.getState().getOnboardingStatus();
    }

    //Check if user go from rootPath
    const currentPath = getCurrentPath();
    if (!currentPath) {
      let targetScreen = accountStore.getState().permissionNavigateHandler();
      navigateWithTracking(targetScreen, permissionInfo);
    }
  };

  useEffect(() => {
    preparePermission();
  }, []);

  return (
    <PermissionContext.Provider value={{ isLoading, preparePermission }}>
      {ready ? children : <SplashScreen />}
    </PermissionContext.Provider>
  );
};

export const usePermission = () => {
  const context = useContext(PermissionContext);
  if (context === undefined) {
    throw new Error("usePermission must be used within an PermissionProvider");
  }
  return context;
};
