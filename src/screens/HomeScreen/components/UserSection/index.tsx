import { useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { AssetCard } from './AssetCard';
import { accountStore } from '@/store/accountStore';
import {
  ReminderCard,
  ReminderCardContent,
  ReminderCardCountdownBox,
  ReminderCardDescription,
  ReminderCardFooter,
  ReminderCardTitle,
} from '@/components/ui/ReminderCard';
import { useNavigation } from '@/lib/navigation/buildNavigator';
import { ScreenKey } from '@/constants';
import { ArrowNextIcon } from '@/components/icon/ArrowNextIcon';
import { cn } from '@/lib/utils';
import { statementStore } from '@/store/statementStore';
import { differenceInCalendarDays } from 'date-fns/differenceInCalendarDays';
import { Statement, StatementPaidStatus } from '@/types/statement';
import { formatCurrency } from '@/utils/formatCurrency';
import { GeneralCalendarSecondary } from '@zpi/looknfeel-icons';
import { colors } from '@/constants/colors';
import withErrorBoundary from '@/hocs/withErrorBoundary';
import { useTracking } from '@/hooks/useTracking';
import { HomeScreen, ScreenId } from '@/screens/HomeScreen/HomeTrackingId';
import { setCSSVariable } from '@/lib/setCSSVariable';

const UserSection = () => {
  const trackEvent = useTracking(ScreenId.HomeScreen).trackEvent;
  const accountInfo = accountStore(state => state.accountInfo);
  const lastestStatement = statementStore(state => state.lastestStatement);
  const navigation = useNavigation();

  const handleGotoRepay = () => {
    trackEvent(HomeScreen.ClickReminderRepayCTA);
    navigation.navigate(ScreenKey.RepaymentScreen);
  };

  const handleGotoStatementScreen = (statement: Statement) => {
    trackEvent(HomeScreen.ClickReminderStatementCTA);
    navigation.navigate(ScreenKey.StatementScreen, { statementId: statement.id });
  };

  const renderReminderCard = useCallback(
    (lastestStatement: Statement | undefined) => {
      const homeScreenBackgroundColorPropertyName = '--bg-homescreen';
      if (
        !lastestStatement ||
        Number(lastestStatement?.total_due_remaining) <= 0 ||
        lastestStatement?.paid_status === StatementPaidStatus.PAID_STATUS_PAID
      ) {
        setCSSVariable(homeScreenBackgroundColorPropertyName, colors.primary.blue); //sync bg color of promotion section when useSection not display
        return null;
      }

      setCSSVariable(homeScreenBackgroundColorPropertyName, colors.white);
      const remainDay = differenceInCalendarDays(new Date(lastestStatement.due_date), new Date());
      const variant = Number(lastestStatement.total_due_remaining) > 0 && remainDay < 0 ? 'danger' : 'warning';
      const title = variant === 'warning' ? 'Đến hạn thanh toán' : 'Quá hạn thanh toán';

      trackEvent(HomeScreen.LoadReminderCard, { reminder_type: variant === 'warning' ? 'due' : 'past_due' });

      return (
        <div className="bg-primary">
          <div className="w-full h-full px-4 py-4 bg-white rounded-t-xxl">
            <div className="flex flex-col gap-4">
              <ReminderCard variant={variant}>
                <ReminderCardContent>
                  <div>
                    <ReminderCardTitle className="flex items-center justify-start gap-1">
                      <GeneralCalendarSecondary color={colors.white} width={16} height={16} />
                      {title}
                    </ReminderCardTitle>
                    <ReminderCardDescription>
                      Số tiền phải trả: {formatCurrency(Number(lastestStatement.total_due_remaining))}
                    </ReminderCardDescription>
                  </div>
                  <ReminderCardCountdownBox daysLeft={remainDay} />
                </ReminderCardContent>
                <ReminderCardFooter>
                  <Button
                    onClick={() => handleGotoStatementScreen(lastestStatement)}
                    variant="link"
                    className="h-auto p-0 font-normal text-tiny">
                    Xem sao kê
                  </Button>
                  <Button
                    onClick={handleGotoRepay}
                    variant="link"
                    className="flex items-center justify-start h-auto gap-2 p-0 font-normal text-tiny">
                    Thanh toán ngay
                    <ArrowNextIcon className={cn('bg-primary rounded-full')} />
                  </Button>
                </ReminderCardFooter>
              </ReminderCard>
            </div>
          </div>
        </div>
      );
    },
    [navigation],
  );

  return (
    <div>
      <AssetCard accountInfo={accountInfo} />
      {renderReminderCard(lastestStatement)}
    </div>
  );
};

export default withErrorBoundary(UserSection);
