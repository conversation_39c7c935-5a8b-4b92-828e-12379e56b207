import { useCallback, useEffect } from "react";
import { EyeIcon } from "@/components/icon/EyeIcon";
import { Button } from "@/components/ui/Button";
import { formatCurrency } from "@/utils/formatCurrency";
import { colors } from "@/constants/colors";
import { DollarIcon } from "@/components/icon/DollarIcon";
import { GeneralNextIconV2 } from "@/components/icon/GeneralNextV2Icon";
import { WalletIcon } from "@/components/icon/WalletIcon";
import { AccountInfo } from "@/api/getAccountInfo";
import { Skeleton } from "@/components/ui/Skeleton";
import { statementStore } from "@/store/statementStore";
import { useNavigation } from "@/lib/navigation/buildNavigator";
import { ScreenKey } from "@/constants";
import { uiStore } from "@/store/uiStore";
import { StatementPaidStatus } from "@/types/statement";
import { useTracking } from "@/hooks/useTracking";
import { HomeScreen, ScreenId } from "@/screens/HomeScreen/HomeTrackingId";
import { accountStore } from "@/store/accountStore";
import { images } from "@/res";
import { toast } from "sonner";
import AnimatedGradientText from "@/components/ui/AnimatedGradientText";

export const AssetCard = ({ accountInfo }: { accountInfo?: AccountInfo | null }) => {
  const trackEvent = useTracking(ScreenId.HomeScreen).trackEvent;
  const navigation = useNavigation();
  const showAsset = uiStore((state) => state.showAsset);
  const setShowAsset = uiStore.getState().setShowAsset;
  const lastestStatement = statementStore((state) => state.lastestStatement);
  const installment_account_status = accountStore.getState()?.isBound ? "bound" : "unbound";

  const renderAsset = useCallback(
    (amount: number) => {
      return showAsset ? (
        formatCurrency(amount)
      ) : (
        <AnimatedGradientText className="from-white via-white/65 to-white" text={"**********"} />
      );
    },
    [showAsset]
  );

  const toggleShowAsset = () => {
    trackEvent(HomeScreen.ClickHideAsset, { installment_account_status });
    setShowAsset(!showAsset);
  };

  const handleNavigateHistory = () => {
    trackEvent(HomeScreen.ClickSpentCTA, { installment_account_status });
    navigation.navigate(ScreenKey.HistoryScreen);
  };

  const handleGotoStatementScreen = () => {
    if (!lastestStatement?.id) {
      toast.info("Bạn chưa có sao kê để thanh toán");
      return;
    }
    if (dueAmount === 0) {
      toast.info("Bạn không có dư nợ sao kê");
      return;
    }

    navigation.navigate(ScreenKey.StatementScreen, { statementId: lastestStatement?.id });
  };

  useEffect(() => {
    trackEvent(HomeScreen.LoadBalanceSection, { installment_account_status });
  }, []);

  const balance = accountInfo && accountInfo?.installment_balance ? Number(accountInfo.installment_balance) : undefined;
  const limit = accountInfo && accountInfo?.installment_limit ? Number(accountInfo.installment_limit) : undefined;
  const spent = balance && limit ? Number(limit - balance) : undefined;
  const dueAmount =
    lastestStatement &&
    lastestStatement.paid_status !== StatementPaidStatus.PAID_STATUS_PAID &&
    Number(lastestStatement.total_due_remaining) > 0
      ? Number(lastestStatement.total_due_remaining)
      : 0;

  return (
    <div className="relative flex px-4 pt-2 pb-4 mx-auto bg-primary">
      <img src={images.IconZ} className="absolute bottom-0 right-0" loading="lazy" />
      <div className="w-full text-white">
        <div className="relative flex flex-col items-start w-full">
          <Button
            onClick={toggleShowAsset}
            variant="ghost"
            animation="scale"
            className="flex items-center justify-start w-auto h-auto gap-2 px-0 py-0">
            <h2 className="text-xs">Hạn mức còn lại</h2>
            <EyeIcon color={colors.white} width={16} height={16} open={showAsset} />
          </Button>

          <h2 className="text-3xl font-bold leading-9">
            {balance !== undefined ? renderAsset(balance) : <Skeleton className="w-[188px] h-7 my-2 rounded-2xl" />}
          </h2>
          <div className="w-full mt-4 grid grid-cols-2 grid-rows-1 gap-3.5">
            <Button
              onClick={handleNavigateHistory}
              variant="secondary"
              animation="brighter"
              className="w-full flex flex-col text-white p-2 bg-[#00109D] items-start h-auto rounded-xl">
              <div className="flex items-center justify-start gap-2 mb-1">
                <DollarIcon width={20} height={20} />
                <span className="text-xs text-white/60">Tổng dư nợ</span>
              </div>
              <div className="flex items-center justify-between w-full">
                {spent !== undefined ? (
                  <p className="font-bold text-lead">{renderAsset(spent)}</p>
                ) : (
                  <Skeleton className="w-24 h-5 my-0.5 rounded-2xl" />
                )}
                <GeneralNextIconV2 width={16} height={16} />
              </div>
            </Button>
            <Button
              onClick={handleGotoStatementScreen}
              variant="secondary"
              animation="brighter"
              className="w-full flex flex-col text-white p-2 bg-[#00109D] items-start h-auto rounded-xl">
              <div className="flex items-center justify-start gap-2 mb-1">
                <WalletIcon width={20} height={20} />
                <span className="text-xs text-white/60">Số tiền đến hạn</span>
              </div>
              <div className="flex items-center justify-between w-full">
                {dueAmount !== undefined ? (
                  <p className="font-bold text-lead">{renderAsset(dueAmount)}</p>
                ) : (
                  <Skeleton className="w-24 h-5 my-0.5 rounded-2xl" />
                )}
                {dueAmount > 0 ? <GeneralNextIconV2 width={16} height={16} /> : null}
              </div>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
