import { Templates } from "@/types/promotion";
import R18RewardVoucherV2 from "@/components/ModuleFederation/R18RewardVoucherV2";
import R18ProductPageSlider from "@/components/ModuleFederation/R18ProductPageSlider";
import { useTracking } from "@/hooks/useTracking";
import { HomeScreen, ScreenId } from "../HomeTrackingId";
import { accountStore } from "@/store/accountStore";
import { INSTALLMENT_PRODUCT_INVENTORY_ID, INSTALLMENT_REWARD_INVENTORY_ID } from "@/screens/OnboardingScreen/constant";
import { useState, useRef, memo, useCallback, useMemo } from "react";

export const MerchantSection = () => {
  const trackEvent = useTracking(ScreenId.HomeScreen).trackEvent;
  const installment_account_status = accountStore.getState()?.isBound ? "bound" : "unbound";

  // State to track loading status of components
  const [loadedComponents, setLoadedComponents] = useState({
    rewardVoucher: false,
    productSlider: false,
  });

  // Ref for the section container
  const sectionRef = useRef<HTMLElement>(null);

  // Check if all components are loaded
  const allComponentsLoaded = true // loadedComponents.rewardVoucher && loadedComponents.productSlider;
  console.log("allComponentsLoaded", allComponentsLoaded);
  // No useEffect needed - CSS Grid handles the animation

  const onLoadProductPageSlider = (data: any) => {
    console.log("onLoadProductPageSlider", data);
    // setLoadedComponents((prev) => ({ ...prev, productSlider: true }));
    trackEvent(HomeScreen.LoadMerchantComponent, {
      installment_account_status,
    });
  };

  const onLoadRewardVoucherV2 = (data: any) => {
    console.log("onLoadRewardVoucherV2", data);
    // setLoadedComponents((prev) => ({ ...prev, rewardVoucher: true }));
    trackEvent(HomeScreen.LoadVoucherComponent, {
      installment_account_status,
    });
  };

  const renderAds = useMemo(() => {
    return (
      <>
        <R18RewardVoucherV2
          className="[&>div>section>div>h3]:!text-base w-full mb-3 overflow-hidden empty:hidden"
          reward_inventory_id={"osc_retail_mart_4"}
          onLoaded={onLoadRewardVoucherV2}
          hasSkeleton={false}
        />
        <R18ProductPageSlider
          request={{
            inventory_id: "Promotion_Hub_Top",
            extra_infos: {
              position: "installment_homepage",
            },
          }}
          className="[&>div>div>h3]:!text-base [&>div>div>div]:!text-base empty:hidden mx-4 mb-4 p-4 pt-4.5 bg-white rounded-extra-lg"
          onLoaded={onLoadProductPageSlider}
          template={Templates.LIST11}
          containerPadding={16}
          hasSkeleton={false}
        />
      </>
    );
  }, []);

  //${allComponentsLoaded ? "grid-rows-[1fr] opacity-100 blur-0 h-auto visible" : ""}

  return (
    <>
      <section
        ref={sectionRef}
        className={`
          grid overflow-hidden transition-all duration-500 ease-in-out invisible h-0 grid-rows-[0fr] opacity-0 blur-[4px]
          
        `}>
        <div>
          {/* {renderAds} */}
        </div>
      </section>
    </>
  );
};
