import { useEffect, useState } from "react";
import { Button } from "@/components/ui/Button";
import { Service, getproposedServices } from "@/api/getProposedServices";
import { launchDeeplink } from "@/lib/ZalopaySDK/launchDeeplink";
import { useTracking } from "@/hooks/useTracking";
import { HomeScreen, ScreenId } from "../HomeTrackingId";
import { accountStore } from "@/store/accountStore";

export const RecomendedSection = () => {
  const trackEvent = useTracking(ScreenId.HomeScreen).trackEvent;
  const [services, setServices] = useState<Service[]>([]);
  const installment_account_status = accountStore.getState()?.isBound ? "bound" : "unbound";
  const handleServiceClick = (service: Service, index: number) => {
    trackEvent(HomeScreen.ClickRecommendServicesItem, {
      installment_account_status,
      service_selected: service?.name,
      service_position: index,
    });
    launchDeeplink({ zpi: service?.interaction?.zpi_url, zpa: service?.interaction?.zpa_url });
  };

  useEffect(() => {
    trackEvent(HomeScreen.LoadRecommendServices, { installment_account_status });
    (async () => {
      const result = await getproposedServices();
      if (result && result?.services) {
        setServices(result.services);
      }
    })();
  }, []);

  return (
    <section className="relative px-4">
      <div className="flex flex-col gap-4 py-4 bg-gradient-to-b from-white via-80% via-white/80 to-white/70 rounded-xxl">
        <div className="flex justify-start items-center px-4">
          <div className="flex flex-col flex-1 text-base leading-5 text-white">
            <h2 className="text-black text-base leading-5 font-bold">Dịch vụ đề xuất</h2>
          </div>
        </div>
        <div className="grid grid-cols-4">
          {services && services.length > 0
            ? services.map((service: Service, idx) => (
                <Button
                  key={idx}
                  onClick={() => handleServiceClick(service, idx)}
                  variant="ghost"
                  animation="scale"
                  className="w-full rounded-lg overflow-hidden flex flex-col gap-3 h-fit justify-start items-center p-2">
                  <div className="relative overflow-hidden">
                    <img
                      loading="lazy"
                      src={service?.content_image?.icon_url}
                      className="h-10 w-10 object-cover inset-0 image-fade-in"
                    />
                  </div>
                  <h2 className="w-full text-center text-balance text-base font-normal">{service?.name}</h2>
                </Button>
              ))
            : null}
        </div>
      </div>
    </section>
  );
};
