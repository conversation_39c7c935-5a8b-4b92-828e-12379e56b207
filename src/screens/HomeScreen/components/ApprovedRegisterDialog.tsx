import { useEffect } from "react";

import { Button } from "@/components/ui/Button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/Dialog";
import { XIcon } from "@/components/XIcon";
import { ScreenKey } from "@/constants";
import { useTracking } from "@/hooks/useTracking";
import { useNavigation } from "@/lib/navigation/buildNavigator";
import { animations, images } from "@/res";
import { OnboardingCongratPopup, ScreenId } from "@/screens/OnboardingScreen/OnboardingTrackingId";
import { appStore } from "@/store/appStore";
import loadable from "@loadable/component";

const Rive = loadable(() => import("@rive-app/react-canvas"), {
  resolveComponent: (components) => components.default,
});

const ApprovedRegisterDialog = () => {
  const trackEvent = useTracking(ScreenId.OnboardingCongratPopup).trackEvent;
  const openApprovedRegisterDialog = appStore((state) => state.openApprovedRegisterDialog);
  const navigate = useNavigation();
  const closeDialog = () => {
    trackEvent(OnboardingCongratPopup.ClickClose);
    appStore.getState().setOpenApprovedRegisterDialog(false);
  };

  const exploreClick = () => {
    trackEvent(OnboardingCongratPopup.ClickMainCTA);
    closeDialog();
    navigate.navigate(ScreenKey.HomeScreen);
  };

  useEffect(() => {
    if (openApprovedRegisterDialog) {
      trackEvent(OnboardingCongratPopup.LoadOnboardingCongratPopup);
    }
  }, [openApprovedRegisterDialog]);

  return (
    <>
      <Dialog open={openApprovedRegisterDialog}>
        <DialogContent className="bg-transparent">
          <Rive
            width="100%"
            height="100%"
            src={animations.ConfettiRiv}
            className="absolute z-[9999] top-0 left-0 pointer-events-none w-full h-full"
          />

          <div className="w-full mx-auto bg-white relative sm:max-w-[425px] p-4 rounded-xl">
            <Button
              variant="ghost"
              onClick={closeDialog}
              className="absolute right-4 top-4 p-0 h-fit rounded-sm opacity-70 transition-opacity disabled:pointer-events-none data-[state=open]:bg-dark-200 data-[state=open]:text-muted-foreground">
              <XIcon className="w-6 h-6" />
              <span className="sr-only">Close</span>
            </Button>
            <DialogHeader className="mb-6">
              <div className="mx-auto mb-6 w-fit">
                <img src={images.ImgTofiConfident} width={120} height={120} alt="tofi confident" />
              </div>
              <DialogTitle className="pb-2 text-base font-bold text-center">
                Zalopay đã tiếp nhận hồ sơ của bạn.
              </DialogTitle>
              <DialogDescription className="font-normal text-center text-text-base">
                Kết quả phê duyệt sẽ được thông báo trong ít phút nữa
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="flex flex-col items-center gap-2">
              <Button onClick={exploreClick} variant="default" animation="scale" className="w-full font-bold">
                Khám phá thêm
              </Button>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ApprovedRegisterDialog;
