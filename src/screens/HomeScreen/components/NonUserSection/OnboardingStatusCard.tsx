import isNumber from "lodash.isnumber";
import { useCallback, useEffect, useState } from "react";

import { getOnboardingRejection, RejectionAction } from "@/api/getOnboardingRejection";
import { postReinitiate } from "@/api/postReinitate";
import { CheckedIcon } from "@/components/icon/CheckedIcon";
import { ProcessingIcon } from "@/components/icon/ProcessingIcon";
import { RejectedIcon } from "@/components/icon/RejectedIcon";
import { Button } from "@/components/ui/Button";
import { Divider } from "@/components/ui/Divider";
import { Drawer, DrawerContent, DrawerFooter, DrawerHeader, DrawerTrigger } from "@/components/ui/Drawer";
import { XIcon } from "@/components/XIcon";
import { ONBOARDING_STEP, ScreenKey } from "@/constants";
import { colors } from "@/constants/colors";
import { useTracking } from "@/hooks/useTracking";
import { useNavigation } from "@/lib/navigation/buildNavigator";
import { cn } from "@/lib/utils";
import { images } from "@/res";
import { HomeScreen, ScreenId } from "@/screens/HomeScreen/HomeTrackingId";
import { ONBOARDING_STATE_INFO, OnboardingState } from "@/screens/OnboardingScreen/constant";
import { useOnboarding } from "@/screens/OnboardingScreen/useOnboarding";
import { accountStore } from "@/store/accountStore";
import { onboardingStore } from "@/store/onboardingStore";
import { OnboardingActionCode } from "@/types";
import { BindStatus } from "@/types/bindStatus";

export const OnboardingStatusCard = ({ className }: { className?: string }) => {
  const trackEvent = useTracking(ScreenId.HomeScreen).trackEvent;
  const permissionInfo = accountStore((state) => state.permissionInfo);
  const onBoardingState = onboardingStore((state) => state.onBoardingState);
  const onboardingId = onboardingStore((state) => state.onboardingId);

  //set default onboarding step info when user not bound yet
  let currentStepInfo = onBoardingState !== undefined ? ONBOARDING_STATE_INFO[onBoardingState] : {};
  if (
    permissionInfo?.bind_status &&
    [BindStatus.BIND_STATUS_UNBOUND, BindStatus.BIND_STATUS_UNKNOWN].includes(permissionInfo?.bind_status) &&
    onBoardingState !== OnboardingState.PERMISSION_REJECTED
  ) {
    currentStepInfo = ONBOARDING_STATE_INFO[OnboardingState.PERMISSION_WAITING];
  }

  const getOnboardingStatus = onboardingStore.getState().getOnboardingStatus;
  const initPermission = accountStore.getState().initPermission;

  const currentStep = currentStepInfo.step;
  const navigate = useNavigation();
  const navigateHandler = useOnboarding().navigateHandler;

  const [dialogContent, setDialogContent] = useState({
    title: "",
    description: "",
    icon: "",
    action: [] as RejectionAction[] | undefined,
  });

  const handleGetRejection = async () => {
    try {
      if (!onboardingId) {
        throw "onboarding Id is empty";
      }
      const rejectionInfo = await getOnboardingRejection(onboardingId);
      if (rejectionInfo) {
        setDialogContent({
          title: rejectionInfo?.content?.title,
          description: rejectionInfo?.content?.message,
          icon: images.ImgNotice,
          action: rejectionInfo.actions ? rejectionInfo?.actions : undefined,
        });
      }
    } catch (err) {
      setDialogContent({
        title: "Lỗi",
        description: "Không lấy được thông tin",
        icon: images.ImgNotice,
        action: [],
      });
    }
  };

  const handleReinitate = async () => {
    try {
      if (!onboardingId) {
        throw "onboarding Id is empty";
      }
      await postReinitiate({ onboarding_id: onboardingId });
      closeDialog();
      onboardingStore.getState().resetOnboarding();
      navigate.replace(ScreenKey.OnboardingScreen);
    } catch (err) {
      setDialogContent({
        title: "Lỗi",
        description: "Không thể thực hiện được yêu cầu, vui lòng thử lại.",
        icon: images.ImgNotice,
        action: [],
      });
    }
  };

  const handleNavigateButton = useCallback(async () => {
    trackEvent(HomeScreen.ClickRegistrationProgressSectionCTA, {
      is_kyc: accountStore.getState()?.isEKYC,
      permission_status: permissionInfo?.bind_status,
      current_step: onBoardingState ? OnboardingState[onBoardingState] : "",
      CTA: currentStepInfo?.card_cta,
    });

    if (onBoardingState && [OnboardingState.APPROVAL_REJECTED].includes(onBoardingState)) {
      await handleGetRejection();
      return;
    }

    if (currentStepInfo.dialog_content) {
      return setDialogContent(currentStepInfo.dialog_content);
    }
    // fallback to Onboarding if ok
    navigate.navigate(ScreenKey.OnboardingScreen);
  }, [currentStep]);

  const renderIcon = (currentStep: number, step: number) => {
    if (isNumber(currentStep) && currentStep === step) {
      if (onBoardingState && [OnboardingState.APPROVAL_REJECTED].includes(onBoardingState)) {
        return <RejectedIcon className="w-4 h-4" />;
      } else if (
        onBoardingState &&
        [
          OnboardingState.REGISTER_WAITING,
          OnboardingState.PERMISSION_WAITING,
          OnboardingState.CONTRACT_WAITING,
          OnboardingState.OTP_WAITING,
          OnboardingState.FACE_CHALLENGE_WAITING,
          OnboardingState.APPROVAL_WAITING,
        ].includes(onBoardingState)
      ) {
        return <ProcessingIcon className="w-4 h-4" />;
      } else if (onBoardingState === OnboardingState.APPROVAL_SUCCESS) {
        return <CheckedIcon className="w-4 h-4" />;
      }
    }

    if (currentStep <= 1 && step === 1) {
      if (onBoardingState === OnboardingState.PERMISSION_REJECTED) {
        return <RejectedIcon className="w-4 h-4" />;
      }
      return <ProcessingIcon className="w-4 h-4" />;
    }

    if (currentStep > step || (currentStep === 1 && step === 1)) {
      return <CheckedIcon />;
    }
    return (
      <div className="w-4 h-4 p-px">
        <div className="w-full h-full bg-white border border-blue-500 border-solid rounded-full"></div>
      </div>
    );
  };

  const closeDialog = () => {
    setDialogContent({
      title: "",
      description: "",
      icon: "",
      action: [],
    });
  };

  const handleRetryButton = async () => {
    initPermission();
    getOnboardingStatus();
  };

  useEffect(() => {
    trackEvent(HomeScreen.LoadRegistrationProgressSection, {
      is_kyc: accountStore.getState()?.isEKYC,
      permission_status: permissionInfo?.bind_status,
      current_step: onBoardingState ? OnboardingState[onBoardingState] : "",
      CTA: currentStepInfo?.card_cta,
    });
  }, []);

  if (
    !permissionInfo ||
    (permissionInfo?.bind_status === BindStatus.BIND_STATUS_ONBOARDING && onBoardingState === undefined)
  ) {
    return (
      <div className={cn("flex flex-col justify-center bg-white w-full rounded-lg", className)}>
        <div className="relative flex flex-col overflow-hidden">
          <div className="relative flex items-center gap-3 px-4 py-3 ">
            <div className="flex flex-col flex-1 text-base leading-5">
              <div className="font-bold">Không lấy được thông tin đăng ký</div>
              <span className="text-xs">Vui lòng thử lại</span>
            </div>
            <Button
              variant="outlined"
              animation="scale"
              size="tiny"
              onClick={handleRetryButton}
              className="justify-center my-auto text-xs font-bold leading-4 text-center border border-solid rounded-md">
              Thử lại
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className={cn("flex flex-col justify-center bg-white w-full rounded-lg", className)}>
        <div className="relative flex flex-col overflow-hidden">
          <div className="relative flex items-center gap-3 px-4 py-3 ">
            <img loading="lazy" src={images.ShoppingGiftRewardBox} className="w-6 h-6" />
            <div className="flex flex-col flex-1 text-base leading-5">
              <div className="font-bold">{currentStepInfo?.card_title}</div>
              <span className="text-xs">{currentStepInfo?.card_description}</span>
            </div>
            <Button
              variant="primary"
              animation="scale"
              size="tiny"
              onClick={handleNavigateButton}
              style={{ backgroundColor: currentStepInfo?.color_card_cta }}
              className="justify-center my-auto text-xs font-bold leading-4 text-center text-white border border-solid rounded-md bg-primary">
              {currentStepInfo?.card_cta}
            </Button>
          </div>
          <div className="px-4">
            <Divider thick={2} color={colors.dark[100]} type="dot" />
          </div>
          <div className="flex relative flex-col justify-center w-full text-center p-0.5 pb-2">
            <div className="flex gap-0 pr-4">
              {ONBOARDING_STEP.map((i, idx) => (
                <div key={idx} className="flex flex-col items-center flex-1 py-2 bg-white">
                  <div className="flex items-center justify-center w-full">
                    <div
                      className={cn(
                        "h-px flex-1",
                        i.step === 1 ? "invisible" : "",
                        currentStep && currentStep >= i.step ? "bg-primary" : "bg-slate-500"
                      )}></div>
                    {renderIcon(currentStep, i.step)}
                    <div
                      className={cn(
                        "h-px flex-1",
                        i.step === 3 ? "invisible" : "",
                        currentStep && currentStep >= i.step && currentStep > 1 ? "bg-primary" : "bg-slate-500"
                      )}></div>
                  </div>
                  <div className="flex flex-col px-2 mt-1">
                    <div className="text-xs leading-4">{i.title}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      <Drawer
        open={Boolean(dialogContent.title)}
        onOpenChange={(open) => {
          if (!open) {
            closeDialog();
          }
        }}
        shouldScaleBackground={false}>
        <DrawerContent className="bg-transparent">
          <div className="w-full mx-auto bg-white relative sm:max-w-[620px] p-4 rounded-t-xl">
            <DrawerTrigger>
              <Button
                variant="ghost"
                onClick={closeDialog}
                className="absolute right-4 top-4 p-0 h-fit rounded-sm opacity-70 transition-opacity disabled:pointer-events-none data-[state=open]:bg-dark-200 data-[state=open]:text-muted-foreground">
                <XIcon className="w-6 h-6" />
                <span className="sr-only">Close</span>
              </Button>
            </DrawerTrigger>
            <DrawerHeader>
              <div className="mx-auto mb-6 w-fit">
                <img src={dialogContent.icon} width={120} height={120} alt="tofi confident" />
              </div>
              <h2 className="pb-2 font-bold text-center text-lead">{dialogContent.title}</h2>
              <p className="text-base font-normal text-center">{dialogContent.description}</p>
            </DrawerHeader>
            <DrawerFooter className="!p-0 flex flex-col items-center gap-2">
              {dialogContent.action && dialogContent.action.length > 0 ? (
                dialogContent?.action.map((item: any, idx: number) => {
                  if (item?.code && item.code === OnboardingActionCode.RE_REGISTER_ONBOARDING) {
                    return (
                      <Button
                        onClick={() => handleReinitate()}
                        key={idx}
                        variant={item?.variant?.toString().toLowerCase()}
                        size="lg"
                        className="w-full">
                        {item?.title}
                      </Button>
                    );
                  }
                  return (
                    <Button
                      onClick={() => {
                        if (item?.code === OnboardingActionCode.CLOSE_NOTICE) {
                          closeDialog();
                          return;
                        } else {
                          closeDialog();
                          navigateHandler(item?.code);
                        }
                      }}
                      key={idx}
                      variant={item?.variant?.toString().toLowerCase()}
                      size="lg"
                      className="w-full">
                      {item?.title}
                    </Button>
                  );
                })
              ) : (
                <Button
                  onClick={closeDialog}
                  variant="default"
                  animation="scale"
                  size="lg"
                  className="w-full font-bold">
                  Đóng
                </Button>
              )}
            </DrawerFooter>
          </div>
        </DrawerContent>
      </Drawer>
    </>
  );
};
