import { ArrowNextIcon } from "@/components/icon/ArrowNextIcon";
import { GeneralNextIcon } from "@/components/icon/GeneralNextIcon";
import { colors } from "@/constants/colors";
import { openExternalBrowser } from "@/lib/ZalopaySDK/openExternalBrowser";

export default function ListMerchantInfo({ data }: ListMerchantInfoProps) {
  if (!Array.isArray(data) || !data.length) {
    return null;
  }
  return (
    <div className="flex flex-col gap-1 divide-y divide-blue-50 divide-solid mt-1">
      {data.map(item => (
        <button key={item.name} className="flex items-center justify-between gap-2 py-2" onClick={() => {
          openExternalBrowser(item.url);
        }}>
          <div className="flex items-center gap-2 flex-shrink-1">
            <img src={item.logo} alt={item.name} className="w-7 h-7" />
            <span className="text-base text-dark-400 text-left line-clamp-1">{item.name}</span>
          </div>
          <div className="flex items-center gap-2 flex-shrink-0">
            {item.is_offline_merchant && (
              <span className="text-tiny text-dark-400 px-2 py-0.5 bg-dark-25 rounded whitespace-nowrap">Tại cửa hàng</span>
            )}
            {item.is_online_merchant && (
              <span className="text-tiny text-green-700 px-2 py-0.5 bg-green-50 rounded whitespace-nowrap">Online</span>
            )}
            <GeneralNextIcon className="m-1.5" color={colors.blue[500]} />
          </div>
        </button>
      ))}
    </div>
  );
}

interface ListMerchantInfoProps {
  data?: {
    logo: string;
    name: string;
    is_offline_merchant: boolean;
    is_online_merchant: boolean;
    url: string;
  }[];
}
