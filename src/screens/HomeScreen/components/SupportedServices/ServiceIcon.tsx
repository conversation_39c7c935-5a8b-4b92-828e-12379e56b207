import { useEffect, useState } from 'react';
import DOMPurify from 'dompurify';

export default function ServiceIcon({ url }: ServiceIconProps) {
  const [svgContent, setSvgContent] = useState<string>('');

  useEffect(() => {
    async function fetchSvg() {
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`Failed to load SVG from ${url}`);
        }
        const text = await response.text();
        setSvgContent(text);
      } catch (error) {
        console.error(error);
        setSvgContent('');
      }
    }
    fetchSvg();
  }, [url]);

  return (
    <div
      className="w-4 h-4 text-[16px]"
      dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(svgContent) }}
    />
  );
};

interface ServiceIconProps {
  url: string;
};
