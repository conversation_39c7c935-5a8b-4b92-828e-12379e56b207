import { Button } from "@/components/ui/Button";
import { GeneralCheckIc16 } from "@zpi/looknfeel-icons";
import { colors } from "@/constants/colors";
import { launchDeeplink } from "@/lib/ZalopaySDK/launchDeeplink";
import ListMerchantInfo from "./ListMerchantInfo";
import ListMerchantLogos from "./ListMerchantLogos";
import { useEffect, useRef } from "react";
import DOMPurify from "dompurify";

export default function ServiceTabContent({ conditions, cta, merchantLogos, merchantInfos }: TabContentProps) {
  const wrapperRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  const handleServiceClick = (redirect: { zpi_url: string; zpa_url: string }) => {
    launchDeeplink({ zpa: redirect.zpa_url, zpi: redirect.zpi_url });
  };
  useEffect(() => {
    if (contentRef.current && wrapperRef.current) {
      wrapperRef.current.style.height = `${contentRef.current.clientHeight}px`;
    }
  }, [conditions, cta, merchantInfos, merchantLogos]);
  return (
    <div ref={wrapperRef} className="transition-[height] duration-500 ease-in-out">
      <div ref={contentRef}>
        <div className="flex items-start justify-between gap-0.5">
          <div className="space-y-4">
            <h2>
              <span className="font-bold">Điều kiện trả góp</span>
            </h2>
            <ul className="space-y-2">
              {conditions.map((condition, idx) => (
                <li key={idx} className="flex items-start justify-start gap-2">
                  <GeneralCheckIc16 className="flex-shrink-0 mt-px" color={colors.green[500]} />{" "}
                  <p
                    className="whitespace-pre-wrap text-dark-400"
                    dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(condition) }}
                  />
                </li>
              ))}
            </ul>
            <ListMerchantLogos data={merchantLogos} />
          </div>
          {cta && (
            <div className="space-y-2.5 flex-[1_1_35%] flex-shrink-0 flex flex-col justify-start items-center w-full">
              <img loading="lazy" src={cta.image} className="w-[96px] h-[96px] bg-contain" />
              <Button
                size="tiny"
                onClick={() =>
                  handleServiceClick({
                    zpa_url: cta?.zpa_url || "",
                    zpi_url: cta?.zpi_url || "",
                  })
                }
                className="px-3 py-2 font-bold text-tiny">
                {cta.label}
              </Button>
            </div>
          )}
        </div>
        <ListMerchantInfo data={merchantInfos} />
      </div>
    </div>
  );
}

interface TabContentProps {
  conditions: string[];
  cta?: {
    label: string;
    image: string;
    zpa_url: string;
    zpi_url: string;
  };
  merchantLogos?: string[];
  merchantInfos?: {
    logo: string;
    name: string;
    is_offline_merchant: boolean;
    is_online_merchant: boolean;
    url: string;
  }[];
}
