import ServiceItem from "./ServiceItem";

export default function SelectBottomSheetContent({ services, activeServiceIndex, onSelectService }: SelectBottomSheetContentProps) {
  return (
      <div className="flex flex-col gap-4 px-4 pt-4 pb-8 bg-primary-white border-t-2 border-t-strokeV2 border-t-solid">
        <span className="text-base font-medium">Chọn dịch vụ bạn muốn xem</span>
        <div className="flex flex-wrap gap-4">
          {services.map((service, index) => (
            <ServiceItem
              key={service.key}
              icon={service.icon}
              label={service.label}
              selected={activeServiceIndex === index}
              onClick={() => onSelectService(index)}
            />
          ))}
        </div>
      </div>
  );
}

interface SelectBottomSheetContentProps {
  services: {
    key: string;
    label: string;
    icon: string;
  }[];
  activeServiceIndex: number;
  onSelectService: (index: number) => void;
}
