import { useEffect, useRef, useState } from "react";
import ServiceItem from "./ServiceItem";
import { GlobalDrawer } from "@/components/GlobalDrawer";
import SelectBottomSheetContent from "./SelectBottomSheetContent";
import { ListIcon } from "@/components/icon/ListIcon";

export default function ServiceTabs({ data, onChange, activeIndex }: TabProps) {
  const scrollRef = useRef<HTMLDivElement | null>(null);
  const [activeTabEl, setActiveTabEl] = useState<HTMLDivElement | null>(null);
  const [maskOpacity, setMaskOpacity] = useState(0);

  useEffect(() => {
    if (scrollRef.current && activeTabEl) {
      const containerRect = scrollRef?.current?.getBoundingClientRect();
      const tabRect = activeTabEl?.getBoundingClientRect();
      const scrollOffset = activeTabEl?.offsetLeft - containerRect?.width / 2 + tabRect?.width / 2;
      scrollRef.current?.scrollTo({ left: scrollOffset, behavior: "smooth" });
    }
  }, [activeTabEl]);
  const handleTabRef = (el: any, isActive: boolean) => {
    if (isActive && el) {
      setActiveTabEl(el);
    }
  };
  function handleScroll() {
    if (!scrollRef.current) return;
    if (scrollRef.current.scrollLeft + scrollRef.current.clientWidth >= scrollRef.current.scrollWidth - 5) {
      setMaskOpacity(0);
      return;
    }
    setMaskOpacity(1);
  }
  return (
    <>
      <div className="flex items-center justify-end bg-green-25 pt-1">
        <div
          ref={scrollRef}
          onScroll={handleScroll}
          className="flex items-center justify-start gap-x-3 overflow-x-auto relative h-full scrollbar-hide">
          {data.map((item, index) => (
            <ServiceItem
              key={`tab-${index}-${item.label}`}
              label={item.label}
              icon={item.icon}
              badge={item.badge}
              selected={activeIndex === index}
              onClick={() => onChange(index)}
              ref={(el) => handleTabRef(el, activeIndex === index)}
            />
          ))}
          <div
            style={{ opacity: maskOpacity }}
            className="pointer-events-none sticky top-2 right-0 bg-gradient-to-r from-[rgba(242,255,248,0)] to-green-25 min-w-6 h-12"
          />
        </div>
        <button className="border min-w-7 h-7 border-solid border-primary rounded-full bg-primary-white flex items-center justify-center" onClick={() => {
          GlobalDrawer.open({
            title: "Dịch vụ hỗ trợ trả góp",
            children: (
              <SelectBottomSheetContent
                services={data}
                activeServiceIndex={activeIndex}
                onSelectService={(index) => {
                  onChange(index);
                  GlobalDrawer.close();
                }}
              />
            )
          })
        }}>
          <ListIcon />
        </button>
      </div>

    </>
  );
}

interface TabProps {
  data: {
    icon: string;
    label: string;
    key: string;
    badge?: string;
  }[];
  activeIndex: number;
  onChange: (index: number) => void;
}
