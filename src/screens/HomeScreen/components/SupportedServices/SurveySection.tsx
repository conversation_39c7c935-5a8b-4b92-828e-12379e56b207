import { useRef, useState } from 'react';

import { getSurveyWrapperResourceUrlWithEnv } from '@/api/core/withBaseUrl';
import { Button } from '@/components/ui/Button';
import { SurveyTypeEnum } from '@ce/survey-wrapper';
import loadable from '@loadable/component';
import { ServiceGeneralCsPrimary } from '@zpi/looknfeel-icons';

const SURVEY_SUBMITTED_LOCAL_STORAGE_KEY = "installment_feedback_submitted";
const surveyComponentName = "installment_feedback_payment";
const SurveyWrapper = loadable(() => import("@ce/survey-wrapper"), { resolveComponent: (module) => module.SurveyWrapper });

/**
 * Survey section component that displays feedback form for installment services
 * @param {Object} props
 * @param {Object} [props.metadata] - Optional tracking metadata for analytics
 * @param {string} [props.metadata.activeTab]
 * @returns {JSX.Element | null} Returns null if survey was already submitted
 */
export const SurveySection = ({ metadata } : { metadata?: any}) => {
  const [isOpenSurvey, setOpenSurvey] = useState(false);
  const shouldLoadSurvey = useRef(false);
  const toggleSurvey = () => {
    const hasSubmitted = localStorage.getItem(SURVEY_SUBMITTED_LOCAL_STORAGE_KEY);
    if (hasSubmitted === "true") {
      return; // Don't open survey if already submitted
    }
    shouldLoadSurvey.current = true;
    setOpenSurvey((prev) => !prev);
  };
  const isShowSurvey = localStorage.getItem(SURVEY_SUBMITTED_LOCAL_STORAGE_KEY) !== "true";

  if (!isShowSurvey) {
    return null;
  }
  
  return (
    <>
      <div className="flex items-start justify-start w-full px-4 py-3 mt-4 space-x-2 bg-white rounded-lg">
        <ServiceGeneralCsPrimary className="w-6 h-6" />
        <div className="flex flex-col items-start text-left space-y-0.5">
          <h3 className="font-bold">Bạn có nhu cầu trả góp dịch vụ khác?</h3>
          <span>Zalopay muốn lắng nghe ý kiến của bạn</span>

          <Button onClick={toggleSurvey} variant="link" className="px-0 h-auto pt-1.5 font-bold">
            Đánh giá ngay
          </Button>
        </div>
      </div>
      {shouldLoadSurvey.current ? (
        <SurveyWrapper
          resourceHost={getSurveyWrapperResourceUrlWithEnv()}
          type={SurveyTypeEnum.BOTTOM_SHEET}
          componentName={surveyComponentName}
          isOpen={isOpenSurvey}
          onSubmit={() => {
            toggleSurvey();
            localStorage.setItem(SURVEY_SUBMITTED_LOCAL_STORAGE_KEY, "true");
          }}
          moreInfo={metadata}
          onCancel={() => {
            toggleSurvey();
          }}
        />
      ) : null}
    </>
  );
};
