import { forwardRef } from "react";
import clsx from "clsx";
import ServiceIcon from "./ServiceIcon";

function ServiceItem({ selected, onClick, icon, label, badge }: ServiceItemProps, ref: React.Ref<HTMLDivElement>) {
  return (
    <div
      data-selected={selected.toString()}
      data-badge-content={badge?.toString()}
      className={clsx(
        "focus:outline-none flex gap-2 justify-center items-center px-3 py-2 bg-white border border-solid border-blue-25 rounded-full w-fit text-primary-dark data-[selected=true]:border-primary data-[selected=true]:text-primary whitespace-nowrap relative",
        {
          "badge after:translate-x-1.5": !!badge,
        }
      )}
      onClick={onClick}
      ref={ref}>
      <ServiceIcon url={icon} />
      <span>{label}</span>
    </div>
  );
}

export default forwardRef(ServiceItem);

interface ServiceItemProps {
  selected: boolean;
  onClick: () => void;
  icon: string;
  label: string;
  badge?: string;
}
