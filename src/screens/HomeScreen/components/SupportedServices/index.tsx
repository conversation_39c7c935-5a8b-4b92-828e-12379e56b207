import { CartIcon } from "@/components/icon/CartIcon";
import { Divider } from "@/components/ui/Divider";
import { useEffect, useState } from "react";
import { HomeScreen as HomeScreenTracking } from "../../HomeTrackingId";
import { accountStore } from "@/store/accountStore";
import ServiceTabs from "./ServiceTabs";
import ServiceTabContent from "./ServiceTabContent";
import { colors } from "@/constants/colors";
import { getSupportedServiceConfigUrlWithEnv } from "@/api/core/withBaseUrl";
import { SurveySection } from "./SurveySection";
interface Service {
  key: string;
  label: string;
  icon: string;
  conditions: string[];
  cta?: {
    label: string;
    image: string;
    zpa_url: string;
    zpi_url: string;
  };
  merchant_logos?: string[];
  merchant_infos?: {
    logo: string;
    name: string;
    is_offline_merchant: boolean;
    is_online_merchant: boolean;
    url: string;
  }[];
}

const VERSION = Date.now() / (3600000 * 3);

function SupportedServices({ trackEvent }: { trackEvent?: (eventId: string, metadata?: Record<string, any>) => void }) {
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const installment_account_status = accountStore.getState()?.isBound ? "bound" : "unbound";
  const [supportedServices, setSupportedServices] = useState<Service[]>([]);
  const activeTab = supportedServices[activeTabIndex];

  useEffect(() => {
    fetchSupportedServicesConfig(`${getSupportedServiceConfigUrlWithEnv()}?v=${VERSION}`)
      .then((data) => setSupportedServices(data.supported_services))
      .catch((error) => console.error("Error fetching supported services:", error));
  }, []);

  function handleOnActiveTabChange(index: number) {
    setActiveTabIndex(index);
    trackEvent?.(HomeScreenTracking.ClickSupportedServiceItem, {
      installment_account_status: installment_account_status,
      service_selected: supportedServices[index]?.label,
    });
  }

  if (!Array.isArray(supportedServices) || !supportedServices.length) {
    return null;
  }

  return (
    <>
      <section className="px-4 pb-4 mb-3.5">
        <div className="flex flex-col w-full p-4 rounded-lg bg-gradient-to-br from-green-25 from-80% via-green-50 via-85% to-green-500/80 relative">
          <h2 className="relative flex items-center gap-2">
            <CartIcon className="w-6 h-6 text-primary" />
            <span className="font-bold text-lead">Dịch vụ hỗ trợ trả góp</span>
          </h2>
          <ServiceTabs data={supportedServices} onChange={handleOnActiveTabChange} activeIndex={activeTabIndex} />
          <Divider color={colors.green[600]} className="w-full mt-3 mb-4" />
          <ServiceTabContent
            conditions={activeTab.conditions}
            cta={activeTab.cta}
            merchantInfos={activeTab.merchant_infos}
            merchantLogos={activeTab.merchant_logos}
          />
          <SurveySection metadata={{ activeTab: activeTab.key }} />
        </div>
      </section>
    </>
  );
}

async function fetchSupportedServicesConfig(url: string): Promise<{ supported_services: Service[] }> {
  try {
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch. Status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching JSON:", error);
    throw error;
  }
}

export default SupportedServices;
