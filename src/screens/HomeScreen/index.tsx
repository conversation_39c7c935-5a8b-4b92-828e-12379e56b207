import { useEffect } from "react";

import { APP_DISPLAY_NAME } from "@/constants";
import { colors } from "@/constants/colors";
import { useDocumentTitle } from "@/hooks/useDocumentTitle";
import { useTracking } from "@/hooks/useTracking";
import { setCSSVariable } from "@/lib/setCSSVariable";
import { accountStore } from "@/store/accountStore";
import loadable from "@loadable/component";

import { MerchantSection } from "./components/MerchantSection";
import { CIMBProvider } from "./components/CIMBProvider";
import { HomeScreen as HomeScreenTracking, ScreenId } from "./HomeTrackingId";
import SupportedServices from "./components/SupportedServices";
import { FAQSection } from "./components/FAQSection";
import { HeaderSection } from "./components/HeaderSection";
import FinButtonSOFButton from "@/components/ModuleFederation/FinButtonSOFButton";

const ApprovedRegisterDialog = loadable(() => import("./components/ApprovedRegisterDialog"));

const HomeScreen = () => {
  useDocumentTitle().setTitle(APP_DISPLAY_NAME);
  const trackEvent = useTracking(ScreenId.HomeScreen).trackEvent;
  const homeScreenBackgroundColorPropertyName = "--bg-homescreen";
  const isBound = accountStore((state) => state.isBound);

  if (isBound) {
    setCSSVariable(homeScreenBackgroundColorPropertyName, colors.white);
  } else {
    setCSSVariable(homeScreenBackgroundColorPropertyName, colors.primary.blue);
  }

  useEffect(() => {
    trackEvent(HomeScreenTracking.LoadHomepage, {
      is_kyc: accountStore.getState()?.isEKYC,
      permission_status: accountStore.getState()?.permissionInfo?.bind_status,
    });
  }, []);
  return (
    <section
      className="relative overflow-y-auto"
      style={{ backgroundColor: `var(${homeScreenBackgroundColorPropertyName}, ${colors.primary.blue})` }}>
      <HeaderSection />
      <FinButtonSOFButton
        orders={{
          app_id: "111",
          amount: "11112",
        }}
        fallback={<div>hhehehehe</div>}
      />
      <div className="max-w-2xl bg-white w-dvw rounded-t-xxl">
        <div className="flex flex-col justify-start pt-4 bg-gradient-home rounded-t-xxl">
          <MerchantSection />
          {/* <RecomendedSection /> */}
          <SupportedServices trackEvent={trackEvent} />
        </div>
        <FAQSection />
        <CIMBProvider />
      </div>
      <ApprovedRegisterDialog />
    </section>
  );
};

export default HomeScreen;
