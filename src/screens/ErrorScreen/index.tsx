import { Button } from "@/components/ui/Button";
import { cn } from "@/lib/utils";
import { closeWindow } from "@/lib/ZalopaySDK/closeWindow";
import { images } from "@/res";
import { useEffect } from "react";

const ErrorScreen = (props: any) => {
  const handleOnClose = () => {
    if (props.onClose) {
      props.onClose();
      return;
    }
    closeWindow();
  };

  useEffect(() => {
    return () => {
      props.onClose?.();
    };
  }, []);

  return (
    <>
      <div className={cn("w-full p-4 flex flex-col gap-6 items-center h-screen justify-center", props?.className)}>
        <img loading="lazy" src={images.ErrorState} className="w-[180px] h-[180px]" />
        <div className="text-center">
          <h2 className="mb-2 font-bold text-lead"><PERSON><PERSON> có lỗi xảy ra</h2>
          <p className="text-base text-dark-300">
            <PERSON>n bạn thông cảm và quay lại sau
          </p>
        </div>
        <Button onClick={handleOnClose} variant={"outlined"} size="lg" className="w-full">
          Về trang chủ
        </Button>
      </div>
    </>
  );
};

export default ErrorScreen;
