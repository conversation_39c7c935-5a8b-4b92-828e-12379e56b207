import { colors } from "@/constants/colors";
import { formatCurrency } from "@/utils/formatCurrency";
import { GeneralNoticeIc16 } from "@zpi/looknfeel-icons";
import { Button, Dialog, DialogTrigger, OverlayArrow, Popover } from "react-aria-components";

export const PenaltyTooltip = ({ outstandingAmount, penaltyAmount }: { outstandingAmount: number; penaltyAmount: number }) => {
  return (
    <DialogTrigger>
      <Button>
        <GeneralNoticeIc16 color={colors.secondary.orange} />
      </Button>
      <Popover>
        <OverlayArrow>
          <svg width="12" height="8" viewBox="0 0 12 8" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6 0L0 8H12L6 0Z" fill="#FF8D00" />
          </svg>
        </OverlayArrow>
        <Dialog className="px-3 py-2 text-base text-white rounded-lg outline-none bg-secondary-orange">
          <div>
            <ul>
              <li><PERSON><PERSON> tiền đến hạn: {formatCurrency(outstandingAmount)}</li>
              <li>Lãi phạt quá hạn: {formatCurrency(Number(penaltyAmount) || 0)}</li>
            </ul>
          </div>
        </Dialog>
      </Popover>
    </DialogTrigger>
  );
};
