import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/Button';
import { GlobalDrawer } from '@/components/GlobalDrawer';
import { getInstallmentList, Installment } from '@/api/getInstallmentsList';
import { APP_ELEMENT_ID, ScreenKey } from '@/constants';
import { images } from '@/res';
import { formatCurrency } from '@/utils/formatCurrency';
import { InstallmentDetail } from './InstallmentDetail';
import { useNavigation } from '@/lib/navigation/buildNavigator';
import { Skeleton } from '@/components/ui/Skeleton';
import { WarningCard } from '@/components/WarningCard';
import { useTracking } from '@/hooks/useTracking';
import { ScreenId, StatementScreenId } from './StatementTrackingId';

export const InstallmentList = ({ statementId, accountId }: { statementId?: string; accountId?: string }) => {
  const navigation = useNavigation();
  const trackEvent = useTracking(ScreenId.StatementScreenId).trackEvent;
  const [installments, setInstallments] = useState<Installment[] | null>();

  const handleGetInstallmentList = async () => {
    try {
      if (statementId && accountId) {
        const result = await getInstallmentList(accountId, statementId);
        if (result && result?.installments) {
          setInstallments(result.installments);
        }
      }
    } catch {
      toast.error('Lấy thông tin giao dịch không thành công');
    }
  };

  const handleClickInstallmentDetail = (installment: Installment) => {
    trackEvent(StatementScreenId.ClickTransactionItem);

    if (!installment) {
      throw 'error';
    }
    try {
      GlobalDrawer.open({
        shouldScaleBackground: true,
        title: 'Thông tin',
        children: (
          <InstallmentDetail
            installment={installment}
            onOpenLoanDetail={() => {
              GlobalDrawer.close();
              if (installment?.zp_trans_id) {
                navigation.navigate(`${ScreenKey.TransactionScreen}/${installment.zp_trans_id}`);
              } else {
                toast.error('Có lỗi khi lấy thông tin trả góp.');
              }
            }}
          />
        ),
      });
    } catch {
      toast.error('Không thể xem chi tiết giao dịch');
      return;
    }
  };

  useEffect(() => {
    (async () => await handleGetInstallmentList())();
  }, [statementId, accountId]);

  const paddingBottom = window.innerHeight - document.getElementById(APP_ELEMENT_ID)!.offsetHeight

  if (installments === null) {
    return (
      <div className="flex flex-col items-center justify-center bg-white" style={{ paddingBottom }}>
        <WarningCard />
        <Button variant="outlined" onClick={handleGetInstallmentList}>
          Thử lại
        </Button>
      </div>
    );
  }

  if (!installments) {
    return (
      <>
        <div className="flex flex-col gap-2 p-4">
          <Skeleton className="w-full h-9" />
          <Skeleton className="w-full h-9" />
          <Skeleton className="w-full h-9" />
          <Skeleton className="w-full h-9" />
          <Skeleton className="w-full h-9" />
        </div>
      </>
    );
  }

  return (
    <>
      <div id="installments">
        {installments && installments.length ? (
          installments.map((installment, idx) => (
            <Button
              variant="ghost"
              className="flex flex-col w-full h-auto gap-0 px-4 py-2 font-normal"
              onClick={() => handleClickInstallmentDetail(installment)}
              key={idx}>
              <div className="flex items-center justify-between w-full gap-2">
                <img src={images.IconTransactionPay} className="w-9 h-9" />
                <div className="flex-1 text-left">
                  <p className="truncate max-w-[220px]">{installment.transaction_desc}</p>
                </div>
                <div className="flex flex-col items-end justify-center">
                  <p className="font-bold text-black">-{formatCurrency(Number(installment.outstanding_amount))}</p>
                </div>
              </div>
            </Button>
          ))
        ) : (
          <div className="flex flex-col items-center justify-center bg-white" style={{ paddingBottom }}>
            <WarningCard
              title="Bạn không có chi tiêu trả góp trong tháng này"
              description="Khám phá thêm các dịch vụ hỗ trợ trả góp và bắt đầu chi tiêu bạn nhé."
            />
            <Button variant="outlined" onClick={() => navigation.navigate(ScreenKey.HomeScreen)}>
              Trang chủ
            </Button>
          </div>
        )}
      </div>
    </>
  );
};
