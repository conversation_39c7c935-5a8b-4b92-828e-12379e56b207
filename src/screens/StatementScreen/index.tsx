import { format, formatDate } from "date-fns";
import { WarningCard } from "@/components/WarningCard";
import { Button } from "@/components/ui/Button";
import { images } from "@/res";
import { formatCurrency } from "@/utils/formatCurrency";
import { useNavigation } from "@/lib/navigation/buildNavigator";
import { ScreenKey } from "@/constants";
import { Statement } from "@/types/statement";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Divider } from "@/components/ui/Divider";
import { GlobalDrawer } from "@/components/GlobalDrawer";
import { useDocumentTitle } from "@/hooks/useDocumentTitle";
import { PenaltyTooltip } from "./PenaltyTooltip";
import { GeneralDownIc16 } from "@zpi/looknfeel-icons";
import { colors } from "@/constants/colors";
import { StatementList } from "@/components/StatementList";
import { getStatementInfo } from "@/api/getStatementInfo";
import { StatementScreenSkeleton } from "./StatementScreenSkeleton";
import { accountStore } from "@/store/accountStore";
import { InstallmentList } from "./InstallmentList";
import { statementStore } from "@/store/statementStore";
import { useTracking } from "@/hooks/useTracking";
import { ScreenId, StatementScreenId } from "./StatementTrackingId";

const StatementScreen = () => {
  useDocumentTitle().setTitle("Sao kê thanh toán");
  const trackEvent = useTracking(ScreenId.StatementScreenId).trackEvent;
  const navigation = useNavigation();
  const accountId = accountStore.getState().accountInfo?.account_id;
  const statementId = navigation.getParam("statementId");
  const [statement, setStatement] = useState<Statement | null>(navigation.getParam("statement"));

  const handleRepaymentStatement = () => {
    trackEvent(StatementScreenId.ClickRepaymentStatement);

    if (!statement) {
      return;
    }
    navigation.navigate(ScreenKey.RepaymentScreen);
  };

  const handleGetStatmentInfo = async (statementId: string) => {
    try {
      if (!accountId) {
        throw "Empty account id";
      }
      const result = await getStatementInfo({ statementId, accountId: accountId });
      if (result.statement) {
        setStatement(result.statement);
      } else {
        setStatement(null);
      }
    } catch {
      setStatement(null);
      toast.error("Lấy thông tin giao dịch không thành công");
    }
  };

  const handleLoadStatement = (statement: Statement) => {
    trackEvent(StatementScreenId.ClickStatementItemStatementBottomSheet, { time: statement.incurred_date});
    GlobalDrawer.close();
    navigation.navigate(`${ScreenKey.StatementScreen}?statementId=${statement.id}`);
  };

  const handleLoadFilterStatementBottomSheet = () => {
    trackEvent(StatementScreenId.LoadFilterStatementBottomSheet);
  }

  const handleOpenStatementListBS = () => {
    trackEvent(StatementScreenId.ClickFilterStatement);

    if (!accountId) {
      toast.error("Không thể thực hiện yêu cầu, vui lòng thử lại sau");
    }

    GlobalDrawer.open({
      title: "Lọc sao kê",
      onClickClose: () => trackEvent(StatementScreenId.ClickCloseStatementBottomSheet),
      children: <StatementList accountId={accountId} onClick={handleLoadStatement} onLoad={handleLoadFilterStatementBottomSheet} />,
    });
  };

  useEffect(() => {
    if (!statementId) {
      setStatement(null);
      return;
    }
    (async () => await handleGetStatmentInfo(statementId))();
  }, [statementId]);

  useEffect(() => {
    trackEvent(StatementScreenId.LoadStatementScreen);
  }, []);

  if (statement === null) {
    return (
      <div className="flex flex-col items-center justify-center">
        <WarningCard />
        <Button variant="outlined" onClick={() => navigation.goBack()}>
          Trở lại
        </Button>
      </div>
    );
  }

  if (!statement) {
    return <StatementScreenSkeleton />;
  }

  const totalAmount = Number(statement.total_due_amount);
  const totalRePaid = Number(statement.total_due_repaid);
  const totalRemain = Number(statement.total_due_remaining);
  const dueDate = statement.due_date;
  const statementName = `Sao kê T${format(statement.incurred_date, "M/yyyy")}`;
  const enableRepayment = statement.id === statementStore.getState().lastestStatement?.id && totalRemain > 0;
  
  return (
    <>
      <section>
        <div className="relative p-4 pt-5 bg-primary">
          <img src={images.BGStatement} className="absolute top-0 right-0 z-0 w-fit h-fit" />
          <div className="relative z-10">
            <div className="pb-4 pt-14">
              <Button
                onClick={handleOpenStatementListBS}
                className="flex gap-1 items-center justify-center px-2 py-1 h-auto rounded-full text-base text-white bg-[#001F3E80]">
                <span>
                  <img src={images.IconNoteBold} width={16} height={16} />
                </span>
                <span>{statementName}</span>
                <span>
                  <GeneralDownIc16 color={colors.white} />
                </span>
              </Button>
            </div>
            <div className="flex flex-col gap-3 px-4 py-2 bg-white rounded-lg">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <span className="block text-tiny text-dark-300">Còn phải thanh toán</span>
                  <span className="block text-lg font-bold">{formatCurrency(totalRemain)}</span>
                </div>
                <Button disabled={!enableRepayment} onClick={handleRepaymentStatement} variant="primary" className="text-base font-bold disabled:bg-dark-25 disabled:text-dark-200">
                  Thanh toán
                </Button>
              </div>
              <Divider type="dot" />
              <div className="max-w-[310px] flex flex-wrap justify-between items-center gap-1.5">
                <div>
                  <p className="pb-1 text-tiny text-dark-300">Tổng tiền đến hạn</p>
                  <p className="text-start flex justify-start items-center gap-1.5">
                    <span>{formatCurrency(totalAmount)}</span>
                    {statement.penalty_amount && Number(statement.penalty_amount) > 0 && (
                      <PenaltyTooltip outstandingAmount={Number(statement.outstanding_amount)} penaltyAmount={Number(statement.penalty_amount)} />
                    )}
                  </p>
                </div>
                <div className="h-7.5 w-px bg-blue-50"></div>
                <div>
                  <p className="pb-1 text-tiny text-dark-300">Đã thanh toán</p>
                  <p className="text-start">{formatCurrency(totalRePaid)}</p>
                </div>
                <div className="h-7.5 p-px bg-blue-50"></div>
                <div>
                  <p className="pb-1 text-tiny text-dark-300">Đến hạn</p>
                  <p className="text-start">{dueDate ? formatDate(dueDate, "dd/MM/yyyy") : "--"}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-primary">
          <div className="bg-white w-dvw max-w-[620px] rounded-t-xxl">
            <div className="flex items-center justify-between p-4 pb-2">
              <h2 className="text-lg font-bold">Chi tiết sao kê</h2>
            </div>
            {/* WIP Filter Trans */}
            {/* <div className="max-w-[375px] flex justify-between items-center gap-2 p-2">
              <Button variant="outlined" className="px-3 font-normal w-fit">
                <p className="truncate">Tất cả</p>
              </Button>
              <Button variant="outlined" className="px-3 font-normal w-fit bg-blue-50 border-blue-50 text-dark-300">
                <p className="truncate">Giao dịch</p>
              </Button>
              <Button variant="outlined" className="px-3 font-normal w-fit bg-blue-50 border-blue-50 text-dark-300">
                <p className="truncate">Phí liên quan</p>
              </Button>
              <Button variant="outlined" className="px-3 font-normal w-fit bg-blue-50 border-blue-50 text-dark-300">
                <p className="truncate">Hoàn tiền</p>
              </Button>
            </div> */}
            <InstallmentList statementId={statement.id} accountId={accountId} />
          </div>
        </div>
      </section>
    </>
  );
};

export default StatementScreen;
