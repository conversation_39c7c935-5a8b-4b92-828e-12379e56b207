import { Skeleton } from "@/components/ui/Skeleton";
import { images } from "@/res";

export const StatementScreenSkeleton = () => {
  return (
    <section>
      <div className="bg-primary p-4 pt-5 relative">
        <img src={images.BGStatement} className="absolute top-0 right-0 z-0 w-fit h-fit" />
        <div className="relative z-10">
          <div className="pt-14 pb-4">
            <Skeleton className="rounded-full w-28 h-7" />
          </div>
          <Skeleton className="rounded-xl w-full h-36" />
        </div>
      </div>
      <div className="bg-primary">
        <div className="bg-white w-dvw max-w-[620px] rounded-t-xxl">
          <div className="flex justify-between items-center p-4 pb-2">
            <Skeleton className="rounded-lg w-36 h-7" />
          </div>
          <div className="flex flex-col gap-2 p-4">
            <Skeleton className="w-full h-9" />
            <Skeleton className="w-full h-9" />
            <Skeleton className="w-full h-9" />
            <Skeleton className="w-full h-9" />
            <Skeleton className="w-full h-9" />
          </div>
        </div>
      </div>
    </section>
  );
};
