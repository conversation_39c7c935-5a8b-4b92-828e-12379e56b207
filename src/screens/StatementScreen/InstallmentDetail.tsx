import { Installment } from "@/api/getInstallmentsList";
import { Button } from "@/components/ui/Button";
import { Divider } from "@/components/ui/Divider";
import { formatCurrency } from "@/utils/formatCurrency";

export const InstallmentDetail = ({ installment, onOpenLoanDetail, }: { installment: Installment; onOpenLoanDetail: () => void }) => {

    return (
        <>
            <div className="overflow-y-auto relative bg-background pb-20">
                <div className="p-4 w-full flex flex-col items-center">
                    <div className="w-full border border-solid border-blue-50 bg-white rounded-lg py-2">
                        <ul className="text-base font-normal">
                            <li className="px-4 py-4.5 flex justify-between items-center">
                                <span className="text-dark-500 w-1/2">Số tiền thanh toán</span>
                                <span className="text-primary text-end font-bold flex-1">{formatCurrency(Number(installment.outstanding_amount))}</span>
                            </li>
                            <li className="px-4">
                                <Divider />
                            </li>
                            <li className="px-4 py-4.5 flex justify-between items-center">
                                <span className="text-dark-300 w-1/2">Khoản đến hạn kỳ này</span>
                                <span className="text-dark-500 text-end flex-1">{installment?.outstanding_details?.total_due_amount ? formatCurrency(Number(installment.outstanding_details.total_due_amount)) : ""}</span>
                            </li>
                            <li className="px-4 py-4.5 flex justify-between items-center">
                                <span className="text-dark-300 w-1/2">Khoản quá hạn kỳ trước</span>
                                <span className="text-dark-500 text-end flex-1">
                                    {installment?.outstanding_details?.total_overdue_amount
                                        ? Number(installment.outstanding_details.total_overdue_amount) > 0
                                            ? formatCurrency(Number(installment?.outstanding_details?.total_overdue_amount))
                                            : "Chưa phát sinh"
                                        : "0đ"}
                                </span>
                            </li>
                            <li className="px-4 py-4.5 flex justify-between items-center">
                                <span className="text-dark-300 w-1/2">Lãi phạt quá hạn</span>
                                <span className="text-dark-500 text-end flex-1">
                                    {installment?.outstanding_details?.total_penalty_amount
                                        ? Number(installment.outstanding_details.total_penalty_amount) > 0
                                            ? formatCurrency(Number(installment?.outstanding_details?.total_penalty_amount))
                                            : "Chưa phát sinh"
                                        : "0đ"}
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div className="fixed bottom-0 left-0 w-full p-4 bg-white">
                    <Button size="lg" className="w-full text-lead font-bold" onClick={onOpenLoanDetail}>
                        Xem khoản trả góp
                    </Button>
                </div>
            </div>
        </>
    );
};
