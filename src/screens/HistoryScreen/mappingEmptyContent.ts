import isNumber from "lodash.isnumber";
import { ScreenKey } from "../../constants";
import { OnboardingState } from "../OnboardingScreen/constant";
import { accountStore } from "../../store/accountStore";
import { BindStatus } from "../../types/bindStatus";

export const mappingEmptyContent = (state?: OnboardingState) => {
  const permissionInfo = accountStore(state => state?.permissionInfo);

  if (
    permissionInfo?.bind_status &&
    [BindStatus.BIND_STATUS_UNBOUND, BindStatus.BIND_STATUS_UNKNOWN].includes(permissionInfo.bind_status)
  ) {
    return {
      title: "Bạn chưa có chi tiêu trả góp",
      description: `Bạn cần đăng ký tài khoản để mở tính năng trả góp. Bấm “Đăng ký ngay” để bắt đầu hành trình tận hưởng mua sắm trả góp đến 12 kỳ ngay trên Zalopay!`,
      ctaTitle: "Đăng ký ngay",
      ctaAction: ScreenKey.OnboardingScreen,
    };
  }

  if (!state && !isNumber(state)) {
    return {
      title: "Bạn chưa có chi tiêu trả góp",
      description: "Khám phá ưu đãi đặc quyền chỉ dành cho trả góp qua Zalopay và mua sắm thoả thích nhé",
      ctaTitle: "Trang chủ",
      ctaAction: ScreenKey.HomeScreen,
    };
  }

  switch (state) {
    case OnboardingState.APPROVAL_WAITING:
    case OnboardingState.PERMISSION_REJECTED:
    case OnboardingState.APPROVAL_REJECTED:
      return {
        title: "Bạn chưa có chi tiêu trả góp",
        description: `Yêu cầu mở tài khoản của bạn chưa được ngân hàng CIMB phê duyệt. Bấm “Về trang chủ" để xem thông tin chi tiết bạn nhé!`,
        ctaTitle: "Về trang chủ",
        ctaAction: ScreenKey.HomeScreen,
      };
    case OnboardingState.PERMISSION_WAITING:
      return {
        title: "Bạn chưa có chi tiêu trả góp",
        description: `Bạn cần đăng ký tài khoản để mở tính năng trả góp. Bấm “Đăng ký ngay” để bắt đầu hành trình tận hưởng mua sắm trả góp đến 12 kỳ ngay trên Zalopay!`,
        ctaTitle: "Đăng ký ngay",
        ctaAction: ScreenKey.OnboardingScreen,
      };
    case OnboardingState.REGISTER_WAITING:
    case OnboardingState.CONTRACT_WAITING:
    case OnboardingState.FACE_CHALLENGE_WAITING:
    case OnboardingState.OTP_WAITING:
      return {
        title: "Bạn chưa có chi tiêu trả góp",
        description: `Bạn chưa hoàn tất gửi yêu cầu mở tài khoản trả góp. Bấm “Tiếp tục đăng ký" để tiếp tục hành trình tận hưởng mua sắm trả góp đến 12 kỳ ngay trên Zalopay!`,
        ctaTitle: "Tiếp tục đăng ký",
        ctaAction: ScreenKey.OnboardingScreen,
      };

    default:
      return {
        title: "Bạn chưa có chi tiêu trả góp",
        description: "Khám phá ưu đãi đặc quyền chỉ dành cho trả góp qua Zalopay và mua sắm thoả thích nhé",
        ctaTitle: "Trang chủ",
        ctaAction: ScreenKey.HomeScreen,
      };
  }
};
