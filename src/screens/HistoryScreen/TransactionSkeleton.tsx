import { Divider } from "@/components/ui/Divider";
import { Skeleton } from "@/components/ui/Skeleton";

export const TransactionSkeleton = () => {
  return (
    <div className="animate-force-in flex flex-col items-center gap-2 py-4 mt-12 h-[calc(100vh-3rem)] overflow-y-auto">
      <div className="w-full px-4">
        <div className="bg-white rounded-lg">
          <div className="px-4 pt-4">
            <h2 className="pb-2 text-base">
              <Skeleton className="w-24 h-5" />
            </h2>
            <Divider type="dot" />
          </div>
          <div className="px-4">
            <div className="flex items-center justify-between gap-2 py-4">
              <Skeleton className="rounded-full h-9 w-9" />
              <div className="flex-1">
                <p className="pb-1 text-base truncate max-w-40">
                  <Skeleton className="h-5 w-28" />
                </p>
                <span className="text-tiny text-dark-300">
                  <Skeleton className="h-3.5 w-16" />
                </span>
              </div>
              <div className="flex flex-col items-end justify-center gap-1">
                <p className="font-bold text-black">
                  <Skeleton className="w-24 h-5" />
                </p>
                <Skeleton className="w-20 h-5" />
              </div>
            </div>
          </div>
          <div className="px-4">
            <div className="flex items-center justify-between gap-2 py-4">
              <Skeleton className="rounded-full h-9 w-9" />
              <div className="flex-1">
                <p className="pb-1 text-base truncate max-w-40">
                  <Skeleton className="h-5 w-28" />
                </p>
                <span className="text-tiny text-dark-300">
                  <Skeleton className="h-3.5 w-16" />
                </span>
              </div>
              <div className="flex flex-col items-end justify-center gap-1">
                <p className="font-bold text-black">
                  <Skeleton className="w-24 h-5" />
                </p>
                <Skeleton className="w-20 h-5" />
              </div>
            </div>
          </div>
          <div className="px-4">
            <div className="flex items-center justify-between gap-2 py-4">
              <Skeleton className="rounded-full h-9 w-9" />
              <div className="flex-1">
                <p className="pb-1 text-base truncate max-w-40">
                  <Skeleton className="h-5 w-28" />
                </p>
                <span className="text-tiny text-dark-300">
                  <Skeleton className="h-3.5 w-16" />
                </span>
              </div>
              <div className="flex flex-col items-end justify-center gap-1">
                <p className="font-bold text-black">
                  <Skeleton className="w-24 h-5" />
                </p>
                <Skeleton className="w-20 h-5" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="w-full px-4">
        <div className="bg-white rounded-lg">
          <div className="px-4 pt-4">
            <h2 className="pb-2 text-base">
              <Skeleton className="w-24 h-5" />
            </h2>
            <Divider type="dot" />
          </div>
          <div className="px-4">
            <div className="flex items-center justify-between gap-2 py-4">
              <Skeleton className="rounded-full h-9 w-9" />
              <div className="flex-1">
                <p className="pb-1 text-base truncate max-w-40">
                  <Skeleton className="h-5 w-28" />
                </p>
                <span className="text-tiny text-dark-300">
                  <Skeleton className="h-3.5 w-16" />
                </span>
              </div>
              <div className="flex flex-col items-end justify-center gap-1">
                <p className="font-bold text-black">
                  <Skeleton className="w-24 h-5" />
                </p>
                <Skeleton className="w-20 h-5" />
              </div>
            </div>
          </div>
          <div className="px-4">
            <div className="flex items-center justify-between gap-2 py-4">
              <Skeleton className="rounded-full h-9 w-9" />
              <div className="flex-1">
                <p className="pb-1 text-base truncate max-w-40">
                  <Skeleton className="h-5 w-28" />
                </p>
                <span className="text-tiny text-dark-300">
                  <Skeleton className="h-3.5 w-16" />
                </span>
              </div>
              <div className="flex flex-col items-end justify-center gap-1">
                <p className="font-bold text-black">
                  <Skeleton className="w-24 h-5" />
                </p>
                <Skeleton className="w-20 h-5" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
