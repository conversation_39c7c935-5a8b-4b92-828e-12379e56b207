import { startOfMonth, isBefore, addMonths, format } from "date-fns";
import { XIcon } from "../../components/XIcon";
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle, DrawerTrigger } from "../../components/ui/Drawer";
import { ReactNode, useEffect } from "react";
import { Button } from "../../components/ui/Button";

export const MonthSelector = ({
  children,
  className,
  minDate,
  maxDate,
  selectedMonth,
  handleMonthSelect,
  onClick,
  onLoaded,
}: {
  children: ReactNode | JSX.Element;
  className?: string;
  minDate: Date;
  maxDate: Date;
  selectedMonth: string;
  handleMonthSelect: (month: string) => void;
  onClick?: () => void;
  onLoaded?: () => void;
}) => {
  const months = [];

  for (let d = startOfMonth(minDate); isBefore(d, addMonths(maxDate, 1)); d = addMonths(d, 1)) {
    months.push(format(d, "MM/yyyy")); // e.g. "12/2023"
  }

  // Group months by year
  const groupedMonths: { [key: string]: string[] } = months.reduce((acc: { [key: string]: string[] }, month) => {
    const [monthName, year] = month.split("/");
    if (!acc[year]) {
      acc[year] = [];
    }
    acc[year].push(monthName);
    return acc;
  }, {});

  return (
    <Drawer direction="bottom" shouldScaleBackground={false}>
      <DrawerTrigger
        className={className}
        onClick={() => {
          onClick?.();
          onLoaded?.();
        }}>
        {children}
      </DrawerTrigger>
      <DrawerContent className="bg-white bottom-0 left-0 right-0 !pointer-events-auto h-[75%]">
        <div className="w-full mx-auto flex flex-col overflow-auto overflow-y-auto">
          <DrawerHeader className="w-full p-0 fixed top-0 left-0 z-10 bg-white rounded-t-extra-lg">
            <div className="pt-1.5 pb-3 px-4 flex flex-col gap-3 items-center border-b border-solid border-dark-25">
              <div className="bg-dark-50 w-10 h-1 rounded-full"></div>
              <div className="w-full flex flex-row justify-between animate-force-in [--force-in-delay:200ms]">
                <div className="invisible">left</div>
                <DrawerTitle>Chọn thời gian</DrawerTitle>
                <DrawerTrigger>
                  <XIcon />
                </DrawerTrigger>
              </div>
            </div>
          </DrawerHeader>

          <div className="mt-[60px]">
            <div className="p-4">
              {Object.keys(groupedMonths)
                .reverse()
                .map(year => (
                  <div className="pb-6" key={year}>
                    <h3 className="pb-3 text-lead font-bold">{year}</h3>
                    <div className="grid grid-flow-row grid-cols-3 gap-4">
                      {groupedMonths[year].map(month => {
                        const isSelected = selectedMonth.includes(`${month}/${year}`);
                        return (
                          <DrawerTrigger key={month + year}>
                            <Button
                              className="rounded-full w-full text-lead font-normal truncate"
                              onClick={() => handleMonthSelect(`${month}/${year}`)}
                              variant={isSelected ? "outlined" : "secondary"}>
                              Tháng {month}
                            </Button>
                          </DrawerTrigger>
                        );
                      })}
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
};
