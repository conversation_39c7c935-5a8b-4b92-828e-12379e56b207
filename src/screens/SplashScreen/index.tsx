import { images } from "@/res";
import packageJson from "../../../package.json";
import AnimatedGradientText from "@/components/ui/AnimatedGradientText";

const SplashScreen = () => {
  return (
    <>
      <section className="flex flex-col items-center justify-between h-screen pt-8 pb-6 bg-transparent">
        <div></div>
        <div className="flex flex-col items-center justify-center gap-6 animate-force-in">
          <img
            loading="eager"
            src={images.InstallmentLogo}
            className="object-contain w-20 h-20"
            alt="Installment logo"
          />
          <div className="text-center">
            <AnimatedGradientText className="text-xl font-bold" text="Trả góp" />
          </div>
        </div>
        <div className="flex flex-col items-center justify-center gap-3">
          <div className="w-full text-center text-gray-400 text-lead">
            <span className="block">{`${packageJson.version}`}</span>
          </div>
        </div>
      </section>
    </>
  );
};

export default SplashScreen;
