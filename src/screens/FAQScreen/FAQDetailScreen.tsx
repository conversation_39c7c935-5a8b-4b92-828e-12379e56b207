import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";

import { useDocumentTitle } from "@/hooks/useDocumentTitle";

import InstructionSection from "./components/InstructionSection";
import QuestionSection from "./components/QuestionSection";

const FAQDetailScreen: React.FC = () => {
  useDocumentTitle().setTitle("Câu hỏi thường gặp");
  const location = useLocation();
  const [currentServiceTag, setCurrentServiceTag] = useState("");

  useEffect(() => {
    const serviceTag = new URLSearchParams(location.search).get("serviceTag");
    setCurrentServiceTag(serviceTag ?? "");
  }, [location]);

  if (!currentServiceTag.trim()) return null;

  return (
    <main className="p-4 w-full min-h-screen bg-background flex flex-col gap-4">
      <InstructionSection serviceTag={currentServiceTag} />
      <QuestionSection serviceTag={currentServiceTag} title="Câu hỏi thường gặp" />
    </main>
  );
};

export default FAQDetailScreen;
