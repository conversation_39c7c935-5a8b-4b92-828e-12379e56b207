import { useState, useEffect, FC } from "react";

import images from "../assets/images";
import { lampImage } from "../assets/icons";
import MockupPhoneFrame from "./MockupPhoneFrame";

export type FAQInstruction = {
  id: string | number;
  content: string;
  image: string;
};

type FAQService = {
  service_tag: string;
  name: string;
  title: string;
  instructions: FAQInstruction[];
};

// services that have instuctions
const fqaServices: FAQService[] = [
  {
    service_tag: "inst_registration_cimb",
    name: "Đăng ký tài khoản",
    title: "Hướng dẫn đăng ký tài khoản",
    instructions: [
      {
        id: 1,
        content: 'Bấm "Đăng ký ngay"',
        image: images.Account1,
      },
      {
        id: 2,
        content: 'Kiểm tra thông tin đăng ký, điền bổ sung và bấm "Tiếp tục"',
        image: images.Account2,
      },
      {
        id: 3,
        content: "<PERSON>ấm <PERSON>ụp ảnh và xác thực khuôn mặt",
        image: images.Account3,
      },
      {
        id: 4,
        content: "Chờ duyệt hồ sơ",
        image: images.Account4,
      },
      {
        id: 5,
        content: "Kiểm tra hạn mức được duyệt và bắt đầu chi tiêu bằng Trả góp",
        image: images.Account5,
      },
    ],
  },
  {
    service_tag: "inst_payment_cimb",
    name: "Mua sắm trả góp",
    title: "Hướng dẫn mua sắm trả góp",
    instructions: [
      {
        id: 1,
        content: 'Ở bước xác nhận giao dịch, chọn "Xem tất cả" phương thức thanh toán',
        image: images.Payment1,
      },
      {
        id: 2,
        content: "Chọn Trả góp",
        image: images.Payment2,
      },
      {
        id: 3,
        content: "Chọn kỳ hạn trả góp",
        image: images.Payment3,
      },
      {
        id: 4,
        content: 'Chọn "Xác nhận" để thanh toán',
        image: images.Payment4,
      },
    ],
  },
  {
    service_tag: "inst_repayment_cimb",
    name: "Thanh toán dư nợ",
    title: "Hướng dẫn thanh toán dư nợ",
    instructions: [
      {
        id: 1,
        content: 'Ở trang chủ Trả góp, bấm "Thanh toán ngay"',
        image: images.Repay1,
      },
      {
        id: 2,
        content: 'Kiểm tra số tiền đến hạn, nhập số tiền và bấm "Thanh toán"',
        image: images.Repay2,
      },
      {
        id: 3,
        content: "Xác nhận thanh toán dư nợ",
        image: images.Repay3,
      },
    ],
  },
];

type InstructionSectionProps = {
  serviceTag: string;
};

const InstructionSection: FC<InstructionSectionProps> = ({ serviceTag }) => {
  const [currentInstruction, setCurrentInstruction] = useState<FAQInstruction | undefined>();
  const service = fqaServices.find((service) => service.service_tag === serviceTag);

  const handleSelectInstruction = (instruction: FAQInstruction) => {
    if (instruction) setCurrentInstruction(instruction);
  };

  useEffect(() => {
    if (serviceTag) {
      setCurrentInstruction(fqaServices.find((service) => service.service_tag === serviceTag)?.instructions?.[0]);
    }
  }, [serviceTag]);

  if (!service) return null;

  return (
    <>
      <section className="flex flex-col w-full gap-4 p-4 bg-white rounded-xl">
        <h2 className="font-bold text-dark-500 text-lead">{service?.title}</h2>
        <div className="w-full rounded-lg py-3 px-4 flex items-center justify-center gap-3 bg-[#D3EEFF]">
          <img className="object-contain w-6 h-6" src={lampImage} alt="Instruction icon" loading="lazy" />
          <span className="text-base text-dark-500">Bấm từng bước để xem hình ảnh minh hoạ</span>
        </div>
        <div className="flex gap-4 h-fit">
          <MockupPhoneFrame>
            <img
              key={currentInstruction?.id}
              className="w-full h-full animate-image-show"
              src={currentInstruction?.image}
              alt={currentInstruction?.content}
              loading="lazy"
            />
          </MockupPhoneFrame>
          <ul className="flex flex-col items-center w-full gap-4">
            {service.instructions.map((instruction: FAQInstruction) => (
              <li
                className="flex w-full gap-2"
                key={instruction?.id}
                onClick={() => handleSelectInstruction(instruction)}>
                <span
                  className={`w-6 h-6 rounded-full p-2 flex items-center justify-center ${
                    currentInstruction?.id === instruction.id
                      ? "text-primary font-bold bg-[#D3EEFF] tracking-tighter"
                      : "text-base text-dark-300 bg-dark-50"
                  } text-dark-300`}>
                  {instruction.id}
                </span>
                <span
                  className={`flex-1 text-base h-fit ${
                    currentInstruction?.id === instruction.id
                      ? "text-primary font-bold tracking-tighter"
                      : "text-dark-500"
                  }`}>
                  {instruction.content}
                </span>
              </li>
            ))}
          </ul>
        </div>
      </section>
    </>
  );
};

export default InstructionSection;
