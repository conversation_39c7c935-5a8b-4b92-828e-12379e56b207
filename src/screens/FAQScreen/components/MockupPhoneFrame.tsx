import { FC, ReactNode } from "react";
import { cn } from "@/lib/utils";

type MockupPhoneFrameProps = {
  children: ReactNode;
  classNames?: string;
};

const MockupPhoneFrame: FC<MockupPhoneFrameProps> = ({ children, classNames = "" }) => {
  return (
    <div
      className={cn(
        "relative w-full flex items-center justify-center bg-dark-500 p-2 aspect-[9/19.5] rounded-3xl",
        classNames
      )}>
      <div className="absolute z-[1] top-3 w-[25%] h-[3%] bg-dark-500 rounded-xl"></div>
      <div className="relative flex items-center justify-center w-full h-full overflow-hidden rounded-2xl">
        {children}
      </div>
    </div>
  );
};

export default MockupPhoneFrame;
