import { useEffect, useState, Fragment, FC } from "react";

import { GeneralNextIcon } from "@/components/icon/GeneralNextIcon";
import { Divider } from "@/components/ui/Divider";
import { colors } from "@/constants/colors";
import { getFAQQuestions } from "@/api/getFAQQuestions";
import { FAQQuestion, buildQuestionDirectionUrl } from "@/api/getFAQQuestions";
import { navigateTo } from "@/lib/ZalopaySDK";

type QuestionSectionProps = {
  serviceTag: string;
  title?: string;
};

const QuestionSection: FC<QuestionSectionProps> = ({ serviceTag, title }) => {
  const [questions, setQuestions] = useState<FAQQuestion[]>([]);

  const handleQuestionClick = (questionId: string) => {
    const directionUrl = buildQuestionDirectionUrl(questionId, "?moreInfo=undefined&provider=instalment&source=2");
    navigateTo({ url: directionUrl });
  };

  useEffect(() => {
    const handleFetchQuestion = async () => {
      if (!serviceTag.trim()) return;

      try {
        const response = await getFAQQuestions({ tags: serviceTag });
        setQuestions(response?.data ?? []);
      } catch (error) {
        console.error(error);
      }
    };

    handleFetchQuestion();
  }, [serviceTag]);

  if (!serviceTag.trim()) null;
  if (questions.length === 0) return null;
  return (
    <section className="flex flex-col w-full gap-4 p-3 bg-white rounded-lg">
      <h2 className="font-bold text-dark-500 text-lead">{title ?? "Câu hỏi thường gặp"}</h2>
      {questions.length ? (
        <div className="border border-solid rounded-xl border-[#D3EEFF] overflow-hidden">
          {questions.map((question, index) => (
            <Fragment key={question?.id}>
              {index !== 0 && <Divider color="#D3EEFF" type="line" className="px-4" />}
              <button
                className="flex items-center justify-between w-full gap-3 pt-3 pb-2 pl-4 pr-4 bg-white"
                onClick={() => handleQuestionClick(question?.id.toString())}>
                <span className="text-base text-dark-500 leading-[22px] text-left">
                  {`${++index}. ${question?.title}`}
                </span>
                <span className="flex items-center justify-center w-6 h-6">
                  <GeneralNextIcon color={colors.dark[500]} />
                </span>
              </button>
            </Fragment>
          ))}
        </div>
      ) : null}
    </section>
  );
};

export default QuestionSection;
