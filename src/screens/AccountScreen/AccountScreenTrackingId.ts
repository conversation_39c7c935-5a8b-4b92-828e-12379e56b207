export const enum ScreenId {
  AccountScreen = "6413",
}

export const enum AccountScreenId {
  //account not ready to use
  LoadAccountScreenNonRegister = "001", //state = not_registered, in_onboarding, in_approval, rejected
  ClickCTARegister = "002", //button_name = register_now, continue_to_register, mainpage | state = not_registered, in_onboarding, in_approval, rejected
  //account ready to use
  LoadAccountScreen = "003", //status = lock/unlock
  ClickDetailInfo = "004",
  LoadDetailInfoBS = "005",
  CloseCTADetailInfoBS = "006",
  CloseButtonDetailInfoBS = "007",
  ClickViewContract = "008",
  ClickTransionSectionItem = "009", //button_name = transhis, statement	
  LoadStatementBS= "010",
  // ClickYearItemStatementBS = "011",
  ClickStatementItemBS = "012", //time = <month_value>_<year_value>
  // ClickCloseCTAStatementBS = "013",
  ClickCloseBtnStatementBS = "014",
  ClickManageAccountSectionItem = "015", //button_name = lock, unlock, close
  ClickManagePolicySectionItem = "016", //button_name = privacy, tnc, faq	
}
