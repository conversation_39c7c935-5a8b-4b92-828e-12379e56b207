import { memo } from "react";
import { Divider } from "@/components/ui/Divider";
import { GeneralNextIcon } from "@/components/icon/GeneralNextIcon";
import { images } from "@/res";

const ManageAccountSection = () => {
  return (
    <ul className="bg-white rounded-lg">
      <li className="pt-4 space-y-2">
        <div className="px-4 flex justify-between items-start">
          <h3 className="font-bold">Quản lý tài khoản</h3>
        </div>
        <Divider type="dot" />
      </li>
      <li className="p-4 flex justify-between items-center">
        <div className="flex justify-start gap-2.5 items-center">
          <img src={images.IconLock} width={24} height={24} />
          <span>Khóa tài khoản</span>
        </div>
        <GeneralNextIcon width={12} height={12} />
      </li>
      <li className="p-4 flex justify-between items-center">
        <div className="flex justify-start gap-2.5 items-center">
          <img src={images.IconClipboardClose} width={24} height={24} />
          <span>Đóng tài khoản</span>
        </div>
        <GeneralNextIcon width={12} height={12} />
      </li>
    </ul>
  );
};

export default memo(ManageAccountSection);
