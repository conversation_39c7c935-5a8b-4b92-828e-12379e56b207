import { memo } from "react";
import { Divider } from "@/components/ui/Divider";
import { GeneralNextIcon } from "@/components/icon/GeneralNextIcon";
import { images } from "@/res";
import { ZLP_CS_URL, ZLP_CS_DEEPLINK, ZLP_PRIVACY_POLICY, APP_NAME } from "@/constants";
import { launchDeeplink } from "@/lib/ZalopaySDK/launchDeeplink";
import { openInternalBrowser } from "@/lib/ZalopaySDK/openInternalBrowser";
import loadable from "@loadable/component";
import { AccountScreenId } from "../AccountScreenTrackingId";
import { GlobalDrawer } from "@/components/GlobalDrawer";

const PolicyDrawer = loadable(() => import("@/components/PolicyDrawer"));

const PolicySection = ({ onTrack }: { onTrack?: (eventId: string, metadata?: Record<string, any>) => void }) => {
  const handleOpenPrivacyPolicy = () => {
    onTrack?.(AccountScreenId.ClickManagePolicySectionItem, { button_name: "privacy" });
    openInternalBrowser(ZLP_PRIVACY_POLICY);
  };

  const handleOpenTermsOfUse = () => {
    onTrack?.(AccountScreenId.ClickManagePolicySectionItem, { button_name: "tnc" });
    GlobalDrawer.open({
      title: "Chính sách và điều khoản",
      children: <PolicyDrawer />,
    });
  };

  const handleOpenFaq = () => {
    onTrack?.(AccountScreenId.ClickManagePolicySectionItem, { button_name: "faq" });
    const tag = "installment_top_faq";
    return launchDeeplink({
      zpi: `${ZLP_CS_URL}?serviceTag=${tag}&provider=${APP_NAME}`,
      zpa: `${ZLP_CS_DEEPLINK}?target=solutions/${tag}&provider=${APP_NAME}`,
    });
  };

  return (
    <>
      <ul className="bg-white rounded-lg text-base">
        <li className="pt-4 space-y-2">
          <div className="px-4 flex justify-between items-start">
            <h3 className="text-lead font-bold">Chính sách và điều khoản</h3>
          </div>
          <Divider type="dot" className="px-4" />
        </li>
        <li>
          <button className="w-full px-4 py-3 flex justify-between items-center" onClick={handleOpenPrivacyPolicy}>
            <div className="flex justify-start gap-2.5 items-center">
              <img src={images.IconSecurityUser} className="w-6 h-6" />
              <span>Chính sách bảo mật</span>
            </div>
            <GeneralNextIcon className="w-3 h-3" />
          </button>
        </li>
        <li>
          <button onClick={handleOpenTermsOfUse} className="w-full px-4 py-3 flex justify-between items-center">
            <div className="flex justify-start gap-2.5 items-center">
              <img src={images.IconBook} className="w-6 h-6" />
              <span>Điều khoản sử dụng</span>
            </div>
            <GeneralNextIcon className="w-3 h-3" />
          </button>
        </li>
        <li>
          <button className="w-full px-4 py-3 flex justify-between items-center" onClick={handleOpenFaq}>
            <div className="flex justify-start gap-2.5 items-center">
              <img src={images.IconMessageQuestion} className="w-6 h-6" />
              <span>Câu hỏi thường gặp</span>
            </div>
            <GeneralNextIcon className="w-3 h-3" />
          </button>
        </li>
      </ul>
    </>
  );
};

export default memo(PolicySection);
