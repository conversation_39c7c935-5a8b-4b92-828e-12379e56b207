import { useEffect } from "react";
import { Link } from "react-router-dom";
import { ScreenKey } from "@/constants";
import { images } from "@/res";
import { buildPath } from "@/utils/buildPath";
import { Button } from "@/components/ui/Button";

export const EmptyAccountCard = (props: {
    onLoad?: () => void;
    onClickAction?: () => void;
    title?: string;
    description?: string;
    ctaTitle?: string;
    ctaAction?: string;
  }) => {
    useEffect(() => {
      props?.onLoad?.();
      document.getElementsByTagName("body")[0].style.setProperty("overflow", "hidden");
      return () => {
        document.getElementsByTagName("body")[0].style.removeProperty("overflow");
      };
    },[]);
    
    return (
      <div className="flex flex-col items-center justify-center w-full h-screen max-h-dvh">
        <section className="flex flex-col items-center justify-center w-full gap-6 animate-force-in">
          <div className="flex flex-col items-center self-stretch justify-center px-8 text-center">
            <img src={images.CreateAccountBanner} width={180} height={180} />
            <div className="mt-2 font-bold text-lead text-sky-950">
              {props?.title ? props?.title : "Bạn chưa có thông tin tài khoản"}
            </div>
            <div className="mt-2 text-base text-slate-500">
              {props?.description
                ? props?.description
                : "Bạn cần đăng ký tài khoản để mở tính năng trả góp. Bấm “Đăng ký ngay” để bắt đầu hành trình tận hưởng mua sắm trả góp đến 12 kỳ ngay trên Zalopay!"}
            </div>
          </div>
          <Link viewTransition to={buildPath(props?.ctaAction ? props?.ctaAction : ScreenKey.OnboardingScreen)} replace>
            <Button onClick={() => props?.onClickAction?.()} variant="primary" className="text-base">{props?.ctaTitle ? props?.ctaTitle : "Đăng ký ngay"}</Button>
          </Link>
        </section>
      </div>
    );
  };