import { memo, useEffect } from "react";
import { Button } from "@/components/ui/Button";
import { images } from "@/res";
import { FullInfoUser } from "@/components/FullInfoUser";
import { DrawerTrigger } from "@/components/ui/Drawer";
import { accountStore } from "@/store/accountStore";
import { AccountScreenId } from "../AccountScreenTrackingId";
import { GlobalDrawer } from "@/components/GlobalDrawer";
import debounce from "lodash.debounce";


const SummaryInfo = ({ onTrack }: { onTrack?: (eventId: string, metadata?: Record<string, any>) => void }) => {
  const baseUserInfo = window?.__USER_INFO__ ? JSON.parse(window?.__USER_INFO__) : undefined;

  const handleShowDetailUserInfo = () => {
    onTrack?.(AccountScreenId.ClickDetailInfo);
    GlobalDrawer.open({
      title: "Chi tiết thông tin cá nhân",
      onClose: debounce(() => onTrack?.(AccountScreenId.CloseButtonDetailInfoBS), 300),
      children: (
        <DetailUserInfo
          onLoad={() => onTrack?.(AccountScreenId.LoadDetailInfoBS)}
          onClose={() => onTrack?.(AccountScreenId.CloseCTADetailInfoBS)}
        />
      ),
    });
  };

  return (
    <div className="relative bg-primary">
      <img loading="eager" src={images.BGAccount} className="object-cover w-full h-[84px]" />
      <div className="absolute z-10 left-0 top-0 w-full px-4 pt-2 pb-4">
        <div className="flex justify-between items-center">
          <div className="flex justify-start items-center gap-3 text-white">
            <div className="w-[60px] h-[60px] rounded-full bg-slate-400 border border-solid border-white flex justify-center items-center">
              {baseUserInfo?.avatar ? (
                <img src={baseUserInfo.avatar} className="w-full h-full rounded-full" />
              ) : baseUserInfo?.display_name ? (
                baseUserInfo?.display_name[0]
              ) : (
                ""
              )}
            </div>
            <div className="space-y-1">
              <h2 className="font-bold">{baseUserInfo?.display_name}</h2>
              <p>{baseUserInfo?.phone}</p>
            </div>
          </div>
          <Button
            onClick={handleShowDetailUserInfo}
            variant="outlined"
            size="sm"
            className="bg-white text-base font-normal">
            Xem chi tiết
          </Button>
        </div>
      </div>
    </div>
  );
};

export default memo(SummaryInfo);

const DetailUserInfo = ({ onLoad, onClose }: { onLoad: () => void; onClose: () => void }) => {
  const onboardingInfo = accountStore(state => state.onboardingInfo);
  const getOnboardingInfo = accountStore.getState().getOnboardingInfo;

  useEffect(() => {
    if (!onboardingInfo) {
      getOnboardingInfo();
    }
    onLoad?.();
  }, []);
  return (
    <>
      <div className="px-4 py-3 bg-background flex flex-col gap-4 overflow-y-auto">
        <FullInfoUser profileInfo={onboardingInfo} includeIdType={false} />
      </div>
      <div className="bg-white p-4 gap-3">
        <DrawerTrigger
          onClick={onClose}
          className="active:scale-[98%] duration-300 bg-primary text-white text-[16px] font-semibold w-full py-2.5 rounded-[8px]">
          Đóng
        </DrawerTrigger>
      </div>
    </>
  );
};
