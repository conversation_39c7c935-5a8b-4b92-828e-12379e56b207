import { memo } from "react";
import { Divider } from "@/components/ui/Divider";
import { GeneralNextIcon } from "@/components/icon/GeneralNextIcon";
import { GeneralCalendarSecondary, GeneralHistoryIc24 } from "@zpi/looknfeel-icons";
import { colors } from "@/constants/colors";
import { GlobalDrawer } from "@/components/GlobalDrawer";
import { StatementList } from "@/components/StatementList";
import { toast } from "sonner";
import { accountStore } from "@/store/accountStore";
import { Statement } from "@/types/statement";
import { useNavigation } from "@/lib/navigation/buildNavigator";
import { ScreenKey } from "@/constants";
import { AccountScreenId } from "../AccountScreenTrackingId";
import debounce from "lodash.debounce";


const TransactionSection = ({ onTrack }: { onTrack?: (eventId: string, metadata?: Record<string, any>) => void }) => {
  const accountId = accountStore.getState().accountInfo?.account_id;
  const navigation = useNavigation();

  const handleOpenHistoryScreen = () => {
    onTrack?.(AccountScreenId.ClickTransionSectionItem, { button_name: "transhis" });
    return navigation.navigate(ScreenKey.HistoryScreen);
  };

  const handleLoadStatement = (statement: Statement) => {
    onTrack?.(AccountScreenId.ClickStatementItemBS, { time: statement.incurred_date });
    GlobalDrawer.close();
    navigation.navigate(`${ScreenKey.StatementScreen}?statementId=${statement.id}`);
  };

  const handleOpenStatementListBS = () => {
    onTrack?.(AccountScreenId.ClickTransionSectionItem, { button_name: "statement" });
    if (!accountId) {
      toast.error("Không thể thực hiện yêu cầu, vui lòng thử lại sau");
    }

    GlobalDrawer.open({
      shouldScaleBackground: false,
      title: "Lọc sao kê",
      onClose: debounce(() => onTrack?.(AccountScreenId.ClickCloseBtnStatementBS), 300),
      children: (
        <StatementList
          onLoad={() => onTrack?.(AccountScreenId.LoadStatementBS)}
          accountId={accountId}
          onClick={handleLoadStatement}
        />
      ),
    });
  };

  return (
    <ul className="bg-white rounded-lg text-base">
      <li className="pt-4 space-y-2">
        <div className="px-4 flex justify-between items-start">
          <h3 className="text-lead font-bold">Thông tin giao dịch</h3>
        </div>
        <Divider type="dot" className="px-4" />
      </li>
      <li>
        <button onClick={handleOpenHistoryScreen} className="w-full px-4 py-3 flex justify-between items-center">
          <div className="flex justify-start gap-2.5 items-center">
            <GeneralHistoryIc24 viewBox="0 0 24 24" className="w-6 h-6" color={colors.primary.blue} />
            <span>Lịch sử giao dịch</span>
          </div>
          <GeneralNextIcon className="w-3 h-3" />
        </button>
      </li>
      <li>
        <button onClick={handleOpenStatementListBS} className="w-full px-4 py-3 flex justify-between items-center">
          <div className="flex justify-start gap-2.5 items-center">
            <GeneralCalendarSecondary className="w-6 h-6" color={colors.primary.blue} />
            <span>Lịch sử sao kê</span>
          </div>
          <GeneralNextIcon className="w-3 h-3" />
        </button>
      </li>
    </ul>
  );
};

export default memo(TransactionSection);
