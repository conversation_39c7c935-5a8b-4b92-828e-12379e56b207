import { format } from "date-fns";
import { memo, useCallback, useEffect, useState } from "react";
import { toast } from "sonner";

import { ContractResponse } from "@/api/getContract";
import { Button } from "@/components/ui/Button";
import { Divider } from "@/components/ui/Divider";
import { Skeleton } from "@/components/ui/Skeleton";
import { accountStore } from "@/store/accountStore";
import { formatCurrency } from "@/utils/formatCurrency";

import { AccountScreenId } from "../AccountScreenTrackingId";
import { GlobalDrawer } from "@/components/GlobalDrawer";
import { LoadingIcon } from "@/components/ui/LoadingIcon";
import { PdfFileViewer } from "@/components/PdfFileViewer";
import { openPDFViewer } from "@/lib/ZalopaySDK/navigator/openPDFViewer";
import { appStore } from "@/store/appStore";
import { OS, PLATFORM } from "@/constants";

const ContractSection = ({ onTrack }: { onTrack?: (eventId: string, metadata?: Record<string, any>) => void }) => {
  const accountInfo = accountStore(state => state.accountInfo);
  const onboardingId = accountStore(state => state.permissionInfo?.bind_info?.onboarding_id);
  const contractInfo = accountStore(state => state.contractInfo);
  const getContractInfo = accountStore.getState().getContractInfo;
  const [loading, setLoading] = useState(false);
  const [showContract, setShowContract] = useState(false);
  const platform = appStore.getState().appInfo?.platform?.toUpperCase();
  const os = appStore.getState().appInfo?.os?.toUpperCase();
  const isAndroid = os === OS.ANDROID;
  const isZPI = platform === PLATFORM.ZPI;

  const handleOpenPDFContract = useCallback(async () => {
    if (loading) {
      return;
    }
    if (!contractInfo?.signed_contract_url) {
      toast.error("Không tải được thông tin, vui lòng thử lại sau.");
      setShowContract(false);
      return;
    }
    if (isAndroid && isZPI) {
      GlobalDrawer.open({
        title: "Hợp đồng",
        onClose: () => {
          setShowContract(false);
          GlobalDrawer.close();
        },
        children: (
          <div className="w-full h-full overflow-y-auto">
            {contractInfo?.signed_contract_url ? (
              <PdfFileViewer url={contractInfo.signed_contract_url} height="calc(100vh - 160px)" />
            ) : (
              <div className="flex items-center justify-center w-full h-full p-4 text-center">
                Không tải được thông tin, vui lòng thử lại sau.
              </div>
            )}
          </div>
        ),
      });
    }

    openPDFViewer({
      url: contractInfo?.signed_contract_url || "",
      height: 80,
    })
      .then((response) => console.log(response))
      .catch((error) => console.error(error.errorCode));

  }, [contractInfo, loading]);

  const handleOpenContract = useCallback(async () => {
    if (loading) {
      return;
    }
    onTrack?.(AccountScreenId.ClickViewContract);
    try {
      setLoading(true);
      if (!onboardingId) {
        throw "Empty OnboaringId";
      }
      if (contractInfo?.signed_contract_url) {
        handleOpenPDFContract()
        return;
      }
      
      await getContractInfo(onboardingId);
      handleOpenPDFContract()
    } catch {
      toast.error("Đã có lỗi khi lấy thông tin hợp đồng, vui lòng thử lại sau.");
    } finally {
      setLoading(false);
    }
  }, [contractInfo, loading]);


  return (
    <>
      <ul className="text-base bg-white rounded-lg">
        <li className="pt-4 space-y-2">
          <div className="flex items-start justify-between px-4">
            <h3 className="font-bold text-lead">Thông tin hợp đồng</h3>
            {onboardingId ? (
              <Button onClick={handleOpenContract} variant="link" className="h-auto p-0 text-base font-normal">
                {loading ? <LoadingIcon className="text-blue-500 duration-300" /> : <p>Xem hợp đồng</p>}
              </Button>
            ) : null}
          </div>
          <Divider type="dot" className="px-4" />
        </li>
        <li className="flex items-center justify-between p-4">
          <span className="text-dark-300">Họ và tên</span>
          <span>
            {accountInfo && accountInfo?.partner_account_name ? (
              accountInfo?.partner_account_name
            ) : (
              <Skeleton className="w-[72px] h-[17px] my-0.5" />
            )}
          </span>
        </li>
        <li className="flex items-center justify-between p-4">
          <span className="text-dark-300">Số tài khoản</span>
          <span>
            {accountInfo && accountInfo?.partner_account_number ? (
              accountInfo?.partner_account_number
            ) : (
              <Skeleton className="w-[72px] h-[17px] my-0.5" />
            )}
          </span>
        </li>
        <li className="flex items-center justify-between p-4">
          <span className="text-dark-300">Hạn mức được cấp</span>
          <span>
            {accountInfo && accountInfo?.installment_balance ? (
              formatCurrency(Number(accountInfo?.installment_limit))
            ) : (
              <Skeleton className="w-[72px] h-[17px] my-0.5" />
            )}
          </span>
        </li>
        <li className="flex items-center justify-between p-4">
          <span className="text-dark-300">Ngày hiệu lực</span>
          <span>
            {accountInfo && accountInfo?.created_at ? (
              format(new Date(accountInfo.created_at), "dd/MM/yyyy")
            ) : (
              <Skeleton className="w-[142px] h-4 my-0.5" />
            )}
          </span>
        </li>
        <li className="flex items-center justify-between p-4">
          <span className="text-dark-300">Ngày sao kê</span>
          <span>
            {accountInfo && accountInfo?.installment_term?.stmt_incur_date_text ? (
              accountInfo?.installment_term?.stmt_incur_date_text
            ) : (
              <Skeleton className="w-[142px] h-4 my-0.5" />
            )}
          </span>
        </li>
        <li className="flex items-center justify-between p-4">
          <span className="text-dark-300">Ngày đến hạn thanh toán</span>
          <span>
            {accountInfo && accountInfo?.installment_term?.stmt_due_date_text ? (
              accountInfo?.installment_term?.stmt_due_date_text
            ) : (
              <Skeleton className="w-[82px] h-4 my-0.5" />
            )}
          </span>
        </li>
        {/* <li className="flex items-center justify-between p-4">
          <span>Phí chuyển đổi</span>
          <span>
            {accountInfo && accountInfo?.installment_term?.feeExplanation ? (
              accountInfo?.installment_term?.feeExplanation
            ) : (
              <Skeleton className="w-[122px] h-[16px] my-0.5" />
            )}
          </span>
        </li> */}
      </ul>
    </>
  );
};

export default memo(ContractSection);
