import { useEffect, useState } from "react";

import { Button } from "@/components/ui/Button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/Dialog";
import { XIcon } from "@/components/XIcon";
import { UTMCampaign } from "@/constants";
import { animations, images } from "@/res";
import { utmStore } from "@/store/utmStore";
import loadable from "@loadable/component";

const Rive = loadable(() => import("@rive-app/react-canvas"), {
  resolveComponent: (components) => components.default,
});

const CongratsDialog = () => {
  const [open, setOpen] = useState(utmStore.getState().utmCampaign === UTMCampaign.NewOnboarding);
  useEffect(() => {
    utmStore.getState().setUtmCampaign("");
    return utmStore.getState().setUtmCampaign("");
  }, []);

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="bg-transparent">
          <div className="w-full mx-auto bg-white relative sm:max-w-[425px] p-4 rounded-xl">
            <Rive
              width="100%"
              height="100%"
              src={animations.ConfettiRiv}
              className="absolute z-[9999] top-0 left-0 pointer-events-none w-full h-full"
            />
            <Button
              variant="ghost"
              onClick={() => setOpen(false)}
              className="absolute right-4 top-4 p-0 h-fit rounded-sm opacity-70 transition-opacity disabled:pointer-events-none data-[state=open]:bg-dark-200 data-[state=open]:text-muted-foreground">
              <XIcon className="w-6 h-6" />
              <span className="sr-only">Close</span>
            </Button>
            <DialogHeader>
              <div className="mx-auto mb-6 w-fit">
                <img src={images.ImgTofiConfident} width={120} height={120} alt="tofi confident" />
              </div>
              <DialogTitle className="pb-2 text-base font-bold text-center">
                Bạn đã mở thành công tài khoản trả góp tại Ngân hàng CIMB và liên kết thành công với ví Zalopay
              </DialogTitle>
              <DialogDescription className="text-base font-normal text-center">
                Bạn có thể bắt đầu chi tiêu trả góp ngay hôm nay để tận hưởng lãi suất hấp dẫn từ ngân hàng CIMB nhé!
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="flex flex-col items-center gap-2 mt-4">
              <Button
                onClick={() => {
                  setOpen(false);
                }}
                variant="ghost"
                animation="scale"
                className="w-full font-bold text-primary">
                Đóng
              </Button>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default CongratsDialog;
