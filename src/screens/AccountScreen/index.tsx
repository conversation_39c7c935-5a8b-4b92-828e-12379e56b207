import { useEffect } from "react";

import { useDocumentTitle } from "@/hooks/useDocumentTitle";
import { useTracking } from "@/hooks/useTracking";
import { accountStore } from "@/store/accountStore";
import { onboardingStore } from "@/store/onboardingStore";
import loadable from "@loadable/component";

import { AccountScreenId, ScreenId } from "./AccountScreenTrackingId";
import ContractSection from "./components/ContractSection";
import { EmptyAccountCard } from "./components/EmptyAccountCard";
import PolicySection from "./components/PolicySection";
import SummaryInfo from "./components/SummaryInfo";
import TransactionSection from "./components/TransactionSection";
import { mappingEmptyContent } from "./mappingEmptyContent";
import packageJson from "../../../package.json";

const CongratsDialogLazy = loadable(() => import("./components/CongratsDialog"));

const AccountScreen = () => {
  useDocumentTitle().setTitle("Tài khoản");
  const trackEvent = useTracking(ScreenId.AccountScreen).trackEvent;
  const isBound = accountStore.getState()?.isBound;

  if (!isBound) {
    const onBoardingState = onboardingStore.getState().onBoardingState;
    const emptyContent = mappingEmptyContent(onBoardingState);
    return (
      <EmptyAccountCard
        onLoad={() => trackEvent(AccountScreenId.LoadAccountScreenNonRegister, { state: emptyContent.state })}
        onClickAction={() =>
          trackEvent(AccountScreenId.ClickCTARegister, {
            button_name: emptyContent?.ctaTitle,
            state: emptyContent.state,
          })
        }
        {...emptyContent}
      />
    );
  }

  useEffect(() => {
    if (isBound) {
      trackEvent(AccountScreenId.LoadAccountScreen, { status: "unlock" }); // current lock/unlock account not implement yet
    }
  }, []);

  return (
    <>
      <section className="w-full animate-force-in bg-background h-dvh">
        <SummaryInfo onTrack={trackEvent} />
        <div className="bg-primary">
          <div className="flex flex-col gap-4 p-4 pb-32 bg-background rounded-t-2xl">
            <ContractSection onTrack={trackEvent} />
            <TransactionSection onTrack={trackEvent} />
            {/* <ManageAccountSection /> */}
            <PolicySection onTrack={trackEvent} />
            <div className="flex justify-center w-full p-4">
              <span className="block text-dark-300">Phiên bản {packageJson.version}</span>
            </div>
          </div>
        </div>
      </section>
      <CongratsDialogLazy />
    </>
  );
};

export default AccountScreen;
