import { useEffect } from "react";

import { Button } from "@/components/ui/Button";
import { Drawer, DrawerContent, DrawerDescription, DrawerTitle } from "@/components/ui/Drawer";
import { XIcon } from "@/components/XIcon";
import { useTracking } from "@/hooks/useTracking";
import { images } from "@/res";
import { accountStore } from "@/store/accountStore";
import { onboardingStore } from "@/store/onboardingStore";
import { OnboardingActionCode } from "@/types";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";

import { OnboardingNoticeBS, ScreenId } from "../OnboardingTrackingId";
import { useOnboarding } from "../useOnboarding";
import LinkAccount from "./LinkAccount";


export const NoticeBottomSheet = () => {
  const content = onboardingStore((state) => state?.notice?.content);
  const callback = onboardingStore((state) => state?.notice?.callback);
  const metadata = onboardingStore((state) => state?.notice?.metadata);
  const setNotice = onboardingStore.getState().setNotice;

  const { navigateHandler } = useOnboarding();
  const trackEvent = useTracking(metadata?.screenId || ScreenId.OnboardingBasicInfo).trackEvent;
  const closeBottomSheet = () => {
    setNotice(undefined);
  };

  const handleTrackAction = () => {
    if (metadata && metadata?.screenId) {
      const trackingMetadata = {
        error_code: content?.action?.[0]?.code,
        error_msg: content?.content?.message,
      };
      try {
        trackEvent(OnboardingNoticeBS.ClickMainCTA, trackingMetadata);
      } catch {}
    }
  };

  const handleAction = (item: any) => {
    closeBottomSheet();
    handleTrackAction();
    if (item?.code === OnboardingActionCode.RETRY) {
      let timer;
      timer = setTimeout(() => {
        callback?.();
      }, 500);
      timer = undefined;
    } else if (item?.code === OnboardingActionCode.CLOSE_NOTICE) {
      return;
    } else {
      navigateHandler(item?.code);
    }
  };

  //Link Type 3
  const handleResubmit = () => {
    // resubmit when link account success
    closeBottomSheet();
    let timer;
    timer = setTimeout(() => {
      callback?.();
    }, 500);
    timer = undefined;
  };

  const open = !!content;

  useEffect(() => {
    if (open && metadata && metadata?.screenId) {
      try {
        const trackingMetadata = {
          error_code: content?.action?.[0]?.code,
          error_msg: content?.content?.message,
          ...(ScreenId.OnboardingBasicInfo
            ? {
                kyc_level: onboardingStore.getState()?.profileInfo?.kyc_level,
                permission_status: accountStore.getState()?.permissionInfo?.bind_status,
              }
            : {}),
        };
        trackEvent(OnboardingNoticeBS.LoadNoticeBS, trackingMetadata);
      } catch {}
    }
  }, [open]);

  

  const hasLinkType3 = content?.action?.some(
    (item: { code: OnboardingActionCode }) => item.code === OnboardingActionCode.LINK_ACCOUNT
  );

  return (
    <Drawer repositionInputs={false} open={open} shouldScaleBackground={false} onOpenChange={(open) => !open && closeBottomSheet()}>
      <DrawerContent className="bg-transparent">
        <VisuallyHidden>
          <DrawerTitle></DrawerTitle>
          <DrawerDescription></DrawerDescription>
        </VisuallyHidden>
        <div className="w-full mx-auto bg-white relative sm:max-w-[425px] rounded-t-xl">
          <Button
            variant="ghost"
            onClick={closeBottomSheet}
            className="absolute right-4 top-4 p-0 h-fit rounded-sm opacity-70 transition-opacity disabled:pointer-events-none data-[state=open]:bg-dark-200 data-[state=open]:text-muted-foreground">
            <XIcon className="w-6 h-6" />
            <span className="sr-only">Close</span>
          </Button>
          <>
            {hasLinkType3 ? (
              <LinkAccount onResubmit={handleResubmit} />
            ) : (
              <CommonNotice content={content} onAction={handleAction} />
            )}
          </>
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default NoticeBottomSheet;

const CommonNotice = ({ content, onAction }: { content: any; onAction: (e: any) => void }) => {
  return (
    <>
      <div className="grid gap-1.5 p-4 text-center sm:text-left pb-6 pt-16">
        <div className="mx-auto mb-6 w-fit">
          <img src={images.ImgNotice} className="w-[180px] h-[180px]" />
        </div>
        <h2 className="pb-2 font-bold text-center text-lead">{content?.content?.title}</h2>
        <p className="text-base font-normal text-center">{content?.content.message}</p>
      </div>
      <div className="flex flex-col items-center gap-2 p-4 mt-auto">
        {content?.action && content?.action.length > 0
          ? content?.action.map((item: any, idx: number) => {
              return (
                <Button
                  onClick={() => onAction(item)}
                  key={idx}
                  variant={item?.variant?.toString().toLowerCase()}
                  className="max-w-[280px] w-full py-3 text-base font-bold">
                  {item?.title}
                </Button>
              );
            })
          : null}
      </div>
    </>
  );
};
