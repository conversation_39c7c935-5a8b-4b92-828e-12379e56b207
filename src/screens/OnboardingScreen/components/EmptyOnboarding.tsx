import { Button } from "@/components/ui/Button"
import { ScreenKey } from "@/constants";
import { useNavigation } from "@/lib/navigation/buildNavigator"
import { images } from "@/res"

const EmptyOnboarding = () => {
    const navigation = useNavigation();
    return <>
    <section>
    <div className="flex flex-col justify-center items-center px-8 gap-6 pt-16">
        <img src={images.ImgMaintenanceAll} width={180} height={180} />
        <div className="text-center">
          <h2 className="text-lead font-bold">Ôi ở đây trống không</h2>
          <p className="text-base text-dark-300">
            Khám phá ưu đãi đặc quyền chỉ dành cho trả góp qua Zalopay và mua sắm thoả thích nhé
          </p>
        </div>
        <Button className="w-full" variant="outlined" onClick={() => navigation.replace(ScreenKey.SplashScreen)}>
          Trở lại
        </Button>
      </div>
    </section>
    </>
}

export default EmptyOnboarding;