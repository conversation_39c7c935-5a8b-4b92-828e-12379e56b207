import { useEffect, useLayoutEffect, useState } from "react";
import { Button } from "../../../components/ui/Button";
import { onboardingStore } from "../../../store/onboardingStore";
import { OnboardingState } from "../constant";
import { utmStore } from "@/store/utmStore";
import { OnboardingActionCode } from "@/types";
import { toast } from "sonner";

export const OnboardingDevUI = () => {
  const setOnboardingState = onboardingStore.getState().setOnBoardingState;
  const [open, setOpen] = useState(false);
  const [isVisible, setVisible] = useState(false);
  const setOB = (value: OnboardingState) => {
    setOpen(false);
    setOnboardingState(value);
  };

  const triggerType3Case = () => {
    onboardingStore.getState().setNotice({
      content: {
        action: [
          {
          code: OnboardingActionCode.LINK_ACCOUNT,
          title: "Type 3 test"
          }
        ]
      },
      callback: () => {
        toast.success("Test Type 3 done!");
      },
    });
  };
  useEffect(() => {
    (async () => {
      try {
        const cacheKey = window.crypto.getRandomValues(new Int16Array(1))[0] || 1;
        const response = await fetch(
          `https://simg.zalopay.com.vn/fs/Installment/data/white_list.json?cacheId=${cacheKey}`
        );
        const result = await response.json();
        const userID = JSON.parse(window?.__USER_INFO__)?.zalopay_id;
        if (result && result?.white_list && result?.white_list.includes(userID.toString())) {
          setVisible(true);
        }
      } catch {}
    })();
  }, []);

  useEffect(() => {
    const utmCampaign = utmStore.getState().utmCampaign;
    if (utmCampaign && utmCampaign === "test_type_3") {
      triggerType3Case();
    }
  }, []);

  if (!isVisible) {
    return null;
  }
  return (
    <div className="absolute top-0 right-0 z-[999999]">
      <Button className="px-2 py-1 bg-red-400" onClick={() => setOpen((open) => !open)}>
        Devtool
      </Button>
      {open ? (
        <div className="flex flex-col gap-4 px-4 py-3 overflow-y-auto bg-background rounded-xl">
          <div className="flex flex-col gap-2 mx-auto">
            <Button variant="link" onClick={() => setOB(OnboardingState.PERMISSION_WAITING)}>
              INIT
            </Button>
            <Button variant="link" onClick={() => setOB(OnboardingState.REGISTER_WAITING)}>
              Add Infomation
            </Button>
            <Button variant="link" onClick={() => setOB(OnboardingState.FACE_CHALLENGE_WAITING)}>
              Face Challenge
            </Button>
            <Button variant="link" onClick={() => setOB(OnboardingState.OTP_WAITING)}>
              OTP
            </Button>
            <Button variant="link" onClick={triggerType3Case}>
              Type3 
            </Button>
          </div>
        </div>
      ) : null}
    </div>
  );
};

export default OnboardingDevUI;
