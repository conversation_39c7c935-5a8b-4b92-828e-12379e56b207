import { Button } from "../../../../components/ui/Button";
import { LoadingIcon } from "../../../../components/ui/LoadingIcon";
import { useTracking } from "../../../../hooks/useTracking";
import { images } from "../../../../res";
import { appStore } from "../../../../store/appStore";
import { onboardingStore } from "../../../../store/onboardingStore";
import { OnboardingAction, OnboardingActionCode } from "../../../../types";
import { ScreenId, OnboardingBasicInfo } from "../../OnboardingTrackingId";
import { useOnboarding } from "../../useOnboarding";
import { InfoCard } from "./InfoCard";
import { accountStore } from "../../../../store/accountStore";
import { AppAnimation } from "../../../../components/ui/AppAnimation";

export const NonEKYCCard = () => {
  const trackEvent = useTracking(ScreenId.OnboardingBasicInfo).trackEvent;
  const kycStatus = accountStore((state) => state.permissionInfo?.kyc_status);
  const loading = appStore((state) => state.loading);
  const navigateHandler = useOnboarding().navigateHandler;

  const handlePressCTA = (action: OnboardingAction) => {
    const metadata = {
      kyc_level: onboardingStore.getState()?.profileInfo?.kyc_level,
      permission_status: accountStore.getState()?.permissionInfo?.bind_status,
      action_type: action.title,
      routing_action: accountStore.getState()?.permissionInfo?.kyc_status.notice?.actions,
    };
    if (action.code === OnboardingActionCode.NAVIGATE_TO_HOME) {
      trackEvent(OnboardingBasicInfo.ClickSubCTA, metadata);
    } else {
      trackEvent(OnboardingBasicInfo.ClickMainCTA, metadata);
    }

    navigateHandler(action.code);
  };

  if (!kycStatus) {
    return null;
  }

  return (
    <div className="bg-white p-3.5 mx-4 rounded-xxl">
      <div className="flex items-start justify-start gap-1">
        <img src={images.IconGeneralWarning} width={24} height={24} alt="warning icon" />
        <h1 className="font-bold text-lead">
          {kycStatus?.notice?.content?.title
            ? kycStatus?.notice?.content?.title
            : "Vui lòng xác thực thông tin cá nhân"}
        </h1>
      </div>
      <div className="mt-2">
        <p className="text-base text-dark-300">
          {kycStatus?.notice?.content?.message
            ? kycStatus?.notice?.content?.message
            : "Dựa trên thông tin này, chúng tôi sẽ tìm các gói vay phù hợp nhất dành cho bạn!"}
        </p>
      </div>
      <div className="h-4"></div>
      <InfoCard />
      <div className="h-6"></div>
      <div className="flex gap-3 flex-nowrap">
        {kycStatus?.notice?.actions?.length ? (
          <>
            {kycStatus?.notice?.actions?.map((action: OnboardingAction, idx: number) => {
              return (
                <Button
                  onClick={() => handlePressCTA(action)}
                  key={idx}
                  className="flex-1 active:scale-[98%] duration-300 rounded-lg"
                  variant={action?.variant?.toString().toLowerCase() as any}>
                  {loading && action.code !== OnboardingActionCode.NAVIGATE_TO_HOME ? <LoadingIcon /> : action.title}
                </Button>
              );
            })}
          </>
        ) : null}
      </div>
    </div>
  );
};

export default NonEKYCCard;
