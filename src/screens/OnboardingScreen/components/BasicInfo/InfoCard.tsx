import { FullInfoUser } from "@/components/FullInfoUser";
import { GlobalDrawer } from "@/components/GlobalDrawer";
import { Button } from "@/components/ui/Button";
import { Divider } from "@/components/ui/Divider";
import { DrawerFooter, DrawerTrigger } from "@/components/ui/Drawer";
import { LoadingIcon } from "@/components/ui/LoadingIcon";
import { Skeleton } from "@/components/ui/Skeleton";
import { UMAuthType } from "@/constants";
import { colors } from "@/constants/colors";
import { useTracking } from "@/hooks/useTracking";
import { ID_TYPE } from "@/screens/OnboardingScreen/constant";
import { accountStore } from "@/store/accountStore";
import { appStore } from "@/store/appStore";
import { onboardingStore } from "@/store/onboardingStore";
import { ProfileIssue } from "@/types";
import { KycStatus } from "@/types/kycStatus";
import { ProfileInfo } from "@/types/profileInfo";
import { renderStringValue } from "@/utils/renderStringValue";
import { format } from "date-fns";
import debounce from "lodash.debounce";
import { useCallback, useEffect } from "react";
import { OnboardingBasicInfo, ScreenId } from "../../OnboardingTrackingId";
import { useOnboarding } from "../../useOnboarding";

export const InfoCard = () => {
  const loading = appStore(state => state.loading);
  const trackEvent = useTracking(ScreenId.OnboardingBasicInfo).trackEvent;
  const { profileInfo, profileIssues } = onboardingStore(state => ({
    profileInfo: state.profileInfo,
    profileIssues: state.profileIssues,
  }));
  const permissionInfo = accountStore(state => state.permissionInfo);
  const isEKYC = accountStore(state => state.isEKYC);
  const { handleAuthChallenge, onBasicInfoConfirm } = useOnboarding();
  const metadata = {
    kyc_level: profileInfo?.kyc_level,
    permission_status: permissionInfo?.bind_status,
    routing_action: accountStore.getState()?.permissionInfo?.kyc_status.notice?.actions,
  };

  const checkInvalidProfileIssue = (profileIssues: ProfileIssue[]) => {
    const invalidCode = ["PROFILE_KYC_NFC_MISSING", "AGE_NOT_IN_RANGE"];
    for (let item of profileIssues) {
      if (invalidCode.includes(item.code)) {
        return true;
      }
    }

    return false;
  };
  const isDisableSubmit =
    !isEKYC || (profileIssues && profileIssues?.length && checkInvalidProfileIssue(profileIssues));

  const handleLoadDetail = () => {
    trackEvent(OnboardingBasicInfo.LoadDetailUserInfo, metadata);
  };

  const handleOpenKYC = useCallback(() => {
    const kycStatus = permissionInfo?.kyc_status?.status;
    trackEvent(OnboardingBasicInfo.ClickUpdateUserInfo, metadata);

    if (
      kycStatus &&
      [KycStatus.KYC_STATUS_PROCESSING, KycStatus.KYC_STATUS_APPROVE, KycStatus.KYC_STATUS_REJECT].includes(kycStatus)
    ) {
      handleAuthChallenge(UMAuthType.Adjust_NFC);
    } else {
      handleAuthChallenge(UMAuthType.KYC_NFC);
    }
    GlobalDrawer.close();
  }, [permissionInfo, profileInfo]);

  const closeDetail = () => {
    trackEvent(OnboardingBasicInfo.ClickCTAUserInfo, { ...metadata, action_type: "Đóng" });
    GlobalDrawer.close();
  };

  const handleConfirmProfile = () => {
    trackEvent(OnboardingBasicInfo.ClickCTAUserInfo, { ...metadata, action_type: "Tiếp tục" });
    onBasicInfoConfirm();
    GlobalDrawer.close();
  };

  const handlePressViewDetail = () => {
    trackEvent(OnboardingBasicInfo.ClickViewDetailButton, metadata);
    GlobalDrawer.open({
      title: "Chi tiết thông tin cá nhân",
      onClose: debounce(closeDetail, 300),
      children: (
        <ShowFullInfo
          onLoad={handleLoadDetail}
          profileInfo={profileInfo}
          isDisableSubmit={Boolean(isDisableSubmit)}
          loading={loading}
          handleOpenKYC={handleOpenKYC}
          handleConfirmProfile={handleConfirmProfile}
        />
      ),
    });
  };

  return (
    <>
      <div className="border border-solid border-dark-50 rounded-lg p-2.5">
        <div className="flex justify-between">
          <span className="text-xs text-dark-300">Họ và tên</span>
          <span className="text-xs text-dark-300">Ngày sinh</span>
        </div>
        <div className="flex justify-between">
          <span>
            {profileInfo?.full_name ? (
              profileInfo?.full_name
            ) : loading ? (
              <Skeleton className="w-[85px] h-[17px] my-0.5" />
            ) : (
              "-"
            )}
          </span>
          <span>
            {profileInfo?.date_of_birth ? (
              format(new Date(profileInfo?.date_of_birth), "dd/MM/yyyy")
            ) : loading ? (
              <Skeleton className="w-[72px] h-[17px] my-0.5" />
            ) : (
              "-"
            )}
          </span>
        </div>

        <div>
          <Divider color={colors.dark[200]} type="dot" />
        </div>

        <div className="flex justify-between">
          <span className="text-xs text-dark-300">Số điện thoại</span>
          <span className="text-xs text-dark-300">Số {profileInfo?.id_type ? ID_TYPE[profileInfo.id_type] : "Số giấy tờ" }</span>
        </div>
        <div className="flex justify-between">
          <span>
            {profileInfo?.phone_number ? (
              profileInfo?.phone_number
            ) : loading ? (
              <Skeleton className="w-[92px] h-[17px] my-0.5" />
            ) : (
              "-"
            )}
          </span>
          <span>
            {profileInfo?.id_number ? (
              <>{profileInfo?.id_number ? profileInfo?.id_number : "-"}</>
            ) : loading ? (
              <Skeleton className="w-[95px] h-[17px] my-0.5" />
            ) : (
              "-"
            )}
          </span>
        </div>
        <div className="w-full pt-2.5 flex justify-center items-center text-primary active:scale-[98%] duration-300">
          <Button variant="ghost" className="h-auto p-0" onClick={handlePressViewDetail}>Xem chi tiết</Button>
        </div>
      </div>
    </>
  );
};

const ShowFullInfo = ({
  profileInfo,
  isDisableSubmit,
  loading,
  handleOpenKYC,
  handleConfirmProfile,
  onLoad,
}: {
  profileInfo?: ProfileInfo;
  isDisableSubmit: boolean;
  loading: boolean;
  handleOpenKYC: () => void;
  handleConfirmProfile: () => void;
  onLoad?: () => void;
 
}) => {
  useEffect(() => {
    onLoad?.();
  }, []);

  return (
    <>
      <div className="flex flex-col gap-4 p-4 pb-8 overflow-y-auto bg-background">
        <div className="flex items-center justify-between px-4 py-3 bg-white rounded-lg">
          {profileInfo ? (
            <div className="flex flex-col items-start justify-center">
              <span className="text-base">{renderStringValue(profileInfo?.full_name)}</span>
              <span className="text-base text-dark-300">{renderStringValue(profileInfo?.phone_number)}</span>
            </div>
          ) : (
            <div></div>
          )}
          <DrawerTrigger asChild>
            <Button
              variant="outlined"
              onClick={handleOpenKYC}
              className="px-3 py-2 w-fit active:scale-[98%] duration-300 text-primary text-base text-left">
              Cập nhật thông tin
            </Button>
          </DrawerTrigger>
        </div>
        <FullInfoUser profileInfo={profileInfo} />
      </div>

      {isDisableSubmit ? null : (
        <DrawerFooter className="gap-3">
          <p>Bằng việc chọn Tiếp tục, tôi xác nhận thông tin trên chính xác</p>
          <Button onClick={handleConfirmProfile} animation="scale" size="lg" className="font-bold text-white text-lead">
            {loading ? <LoadingIcon /> : "Tiếp tục"}
          </Button>
        </DrawerFooter>
      )}
    </>
  );
};
