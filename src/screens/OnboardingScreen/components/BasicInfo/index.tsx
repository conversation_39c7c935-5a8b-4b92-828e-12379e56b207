import { useEffect, useMemo, useRef } from "react";
import { images } from "@/res";
import { useOnboarding } from "@/screens/OnboardingScreen/useOnboarding";
import { EmblaOptionsType } from "embla-carousel";
import { utmStore } from "@/store/utmStore";
import { UTMSource } from "@/constants";
import { OnboardingBasicInfo, ScreenId } from "@/screens/OnboardingScreen/OnboardingTrackingId";
import { useTracking } from "@/hooks/useTracking";
import loadable from "@loadable/component";
import { accountStore } from "@/store/accountStore";
import { onboardingStore } from "@/store/onboardingStore";

const NonEKYCCard = loadable(() => import("./NonEKYCCard"));
const BasicInfoCard = loadable(() => import("./BasicInfoCard"));
const Stories = loadable(() => import("@/components/ui/Stories"));

const BasicInfo = () => {
  const trackEvent = useTracking(ScreenId.OnboardingBasicInfo).trackEvent;
  const OPTIONS: EmblaOptionsType = {
    loop: true,
  };
  const SLIDES: string[] = useMemo(() => [images.ImageStory1, images.ImageStory2, images.ImageStory3], []);
  const onBasicInfoConfirm = useOnboarding().onBasicInfoConfirm;
  const isEKYC = accountStore((state) => state.isEKYC);

  const swipeCount = useRef(0);

  const handleSlideOnSwipeIntro = () => {
    swipeCount.current++;
    trackEvent(OnboardingBasicInfo.SwipeIntro, { swipe_count: swipeCount.current });
  };

  useEffect(() => {
    const trackingMetadata = {
      kyc_level: onboardingStore.getState()?.profileInfo?.kyc_level,
      permission_status: accountStore.getState()?.permissionInfo?.bind_status,
      routing_action: accountStore.getState()?.permissionInfo?.kyc_status.notice?.actions,
    };
    trackEvent(OnboardingBasicInfo.LoadLandingPage, trackingMetadata);

    const utmSource = utmStore.getState().utmSource;
    if (isEKYC && utmSource && utmSource === UTMSource.KycNfcVerified) {
      utmStore.getState().setUtmSource("");
      onBasicInfoConfirm();
    }
  }, []);

  return (
    <div className="relative max-h-screen min-h-screen bg-primary">
      <Stories slides={SLIDES} options={OPTIONS} autoplay onSwipe={handleSlideOnSwipeIntro} />
      <div className="absolute z-[4] w-full bottom-6 left-0">
        {isEKYC ? <BasicInfoCard onConfirm={onBasicInfoConfirm} /> : <NonEKYCCard />}
      </div>
    </div>
  );
};

export default BasicInfo;
