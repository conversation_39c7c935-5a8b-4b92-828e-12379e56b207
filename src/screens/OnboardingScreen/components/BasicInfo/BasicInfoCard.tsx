import { Button } from "@/components/ui/Button";
import { LoadingIcon } from "@/components/ui/LoadingIcon";
import { ZLP_PRIVACY_POLICY } from "@/constants";
import { useTracking } from "@/hooks/useTracking";
import { openInternalBrowser } from "@/lib/ZalopaySDK/openInternalBrowser";
import { images } from "@/res";
import { accountStore } from "@/store/accountStore";
import { appStore } from "@/store/appStore";
import { onboardingStore } from "@/store/onboardingStore";
import { OnboardingAction, OnboardingActionCode } from "@/types";
import { ScreenId, OnboardingBasicInfo } from "@/screens/OnboardingScreen/OnboardingTrackingId";
import { useOnboarding } from "@/screens/OnboardingScreen/useOnboarding";
import { InfoCard } from "./InfoCard";

export const BasicInfoCard = ({ onConfirm }: { onConfirm: () => void }) => {
  const trackEvent = useTracking(ScreenId.OnboardingBasicInfo).trackEvent;
  const navigateHandler = useOnboarding().navigateHandler;
  const profileIssues = onboardingStore((state) => state.profileIssues);
  const loading = appStore((state) => state.loading);

  const trackingMetadata = {
    kyc_level: onboardingStore.getState()?.profileInfo?.kyc_level,
    permission_status: accountStore.getState()?.permissionInfo?.bind_status,
    routing_action: accountStore.getState()?.permissionInfo?.kyc_status.notice?.actions,
  };

  const handlePressCTA = (action: OnboardingAction) => {
    const actionCode = action.code;

    if (actionCode === OnboardingActionCode.NAVIGATE_TO_HOME) {
      trackEvent(OnboardingBasicInfo.ClickSubCTA, trackingMetadata);
    } else {
      trackEvent(OnboardingBasicInfo.ClickMainCTA, trackingMetadata);
    }

    navigateHandler(actionCode);
  };

  const handleOnConfirm = () => {
    trackEvent(OnboardingBasicInfo.ClickMainCTA, trackingMetadata);
    onConfirm?.();
  };

  const handleExploreMore = () => {
    trackEvent(OnboardingBasicInfo.ClickSubCTA, trackingMetadata);
    navigateHandler(OnboardingActionCode.NAVIGATE_TO_HOME);
  };

  const handleClickZLPTnc = () => {
    trackEvent(OnboardingBasicInfo.ClickZLPTnC, trackingMetadata);
    openInternalBrowser(ZLP_PRIVACY_POLICY);
  };

  return (
    <div className="bg-white p-3.5 mx-4 rounded-xxl">
      <div className="flex items-center justify-between">
        <div className="flex items-center justify-start gap-1">
          <img
            src={profileIssues?.length ? images.IconGeneralWarning : images.IconTips}
            width={24}
            height={24}
            alt="tips-icon"
          />
          <h2 className="font-bold leading-6 text-lead">
            {profileIssues?.length && profileIssues[0]?.notice?.content?.title
              ? profileIssues[0]?.notice?.content?.title
              : "Mở đặc quyền trả góp ngay thôi!"}
          </h2>
        </div>
      </div>
      <div className="mt-2 mb-4">
        <p className="text-dark-300">
          {profileIssues?.length && profileIssues[0]?.notice?.content?.message
            ? profileIssues[0]?.notice?.content?.message
            : ""}
        </p>
      </div>

      <InfoCard />
      <div className="h-5 pb-0.5"></div>
      {profileIssues?.length && profileIssues[0]?.code ? (
        <>
          <div className="flex gap-3 flex-nowrap">
            {profileIssues[0]?.notice?.actions?.length ? (
              <>
                {profileIssues[0]?.notice?.actions.map((action: OnboardingAction, idx: number) => {
                  return (
                    <Button
                      onClick={() => handlePressCTA(action)}
                      key={idx}
                      className="flex-1 active:scale-[98%] duration-300 rounded-lg text-base"
                      variant={action?.variant?.toString().toLowerCase() as any}>
                      {loading && action.code !== OnboardingActionCode.NAVIGATE_TO_HOME ? (
                        <LoadingIcon />
                      ) : (
                        <p>{action.title}</p>
                      )}
                    </Button>
                  );
                })}
              </>
            ) : null}
          </div>
        </>
      ) : (
        <>
          <p className="text-xs">
            Bằng cách nhấn “Đăng ký ngay”, tôi đồng ý với các{" "}
            <button className="text-primary" onClick={handleClickZLPTnc}>
              Chính sách
            </button>{" "}
            <button className="text-primary" onClick={handleClickZLPTnc}>
              của Zalopay
            </button>{" "}
            và chia sẻ thông tin với các đối tác của Zalopay.
          </p>
          <div className="h-6"></div>
          <div className="flex gap-3 flex-nowrap">
            <Button
              onClick={handleExploreMore}
              variant="outlined"
              className="flex-1 active:scale-[98%] duration-300 rounded-lg text-base">
              <p>Khám phá thêm</p>
            </Button>
            <Button
              onClick={handleOnConfirm}
              variant="primary"
              className="flex-1 active:scale-[98%] duration-300 text-white text-base font-semibold w-full py-2.5 rounded-lg">
              {loading ? <LoadingIcon /> : <p>Đăng ký ngay</p>}
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default BasicInfoCard;
