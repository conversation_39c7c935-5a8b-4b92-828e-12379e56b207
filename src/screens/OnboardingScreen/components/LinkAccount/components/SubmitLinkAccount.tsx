import { Button } from "@/components/ui/Button";
import { images } from "@/res";

export const SubmitLinkAccount = ({ onSubmit }: { onSubmit: () => void }) => {
  return (
    <>
      <div className="grid gap-1.5 p-4 sm:text-left pb-6 pt-16">
        <div className="w-full mx-auto mb-6">
          <img loading="eager" src={images.IconAccountVerificationWatingApproval} className="w-full min-h-[7.5rem] image-fade-in" />
        </div>
        <h2 className="pb-2 font-bold text-lead">Bạn đã có tài khoản tại CIMB</h2>
        <p className="text-base font-normal">
          Liên kết tài khoản tại ngân hàng CIMB của bạn với Zalopay để tiếp tục đăng ký trả góp nhé!
        </p>
      </div>
      <div className="flex flex-col items-center gap-2 p-4 mt-auto">
        <Button onClick={onSubmit} variant="primary" className="w-full py-3 text-base font-bold">
          Liên kết ngay
        </Button>
      </div>
    </>
  );
};
