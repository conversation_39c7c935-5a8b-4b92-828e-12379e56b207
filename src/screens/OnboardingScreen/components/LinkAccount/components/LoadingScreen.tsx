import AnimatedGradientText from "@/components/ui/AnimatedGradientText";
import { images } from "@/res";

export const LoadingScreen = () => {
  return (
    <>
      <div className="grid gap-1.5 p-4 sm:text-left pb-6 pt-16">
        <div className="w-full mx-auto mb-6">
          <img
            loading="eager"
            src={images.IconAccountVerificationSend}
            className="w-full min-h-[7.5rem] image-fade-in"
          />
        </div>
        <h2 className="font-bold text-lead">
          <AnimatedGradientText text="Bạn chờ tý nhé..." />
        </h2>
        <p className="text-base font-normal"><PERSON>ệ thống đang liên kết tài khoản tại ngân hàng CIMB của bạn với Zalopay</p>
      </div>
    </>
  );
};
