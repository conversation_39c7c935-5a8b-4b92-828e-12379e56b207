import isNumber from "@/utils/isNumber";
import { useState, useRef, useEffect } from "react";
import { toast } from "sonner";
import { useDebouncedCallback } from "use-debounce";
import { OTPInfo as IOTPInfo, OTPInfo } from "@/types";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { cn } from "@/lib/utils";
import AnimatedGradientText from "@/components/ui/AnimatedGradientText";

export const OTPInput = ({
  onRequestOTP,
  onSubmitOTP,
}: {
  onRequestOTP: () => Promise<OTPInfo>;
  onSubmitOTP: (otpValue: string) => void;
}) => {
  const [OTPInfo, setOTPInfo] = useState<IOTPInfo>();
  const otpInputRef = useRef<HTMLInputElement>(null);
  const [otpValue, setOTP] = useState<string>();
  const isValidOTP = otpValue?.length === 6 && isNumber(otpValue);
  const clearOTPValue = () => {
    if (otpInputRef && otpInputRef.current) {
      otpInputRef.current.value = "";
    }
    setOTP("");
  };

  const setOTPValue = useDebouncedCallback((value: string) => {
    setOTP(value);
  }, 500);

  const handleSubmitOTP = () => {
    if (!isValidOTP) {
      toast.error("Mã OTP không hợp lệ");
      return;
    }
    onSubmitOTP(otpValue);
  };

  const handleRequestOTP = async () => {
    try {
      const result = await onRequestOTP();
      if (result) {
        setOTPInfo(result);
      }
    } catch {}
  };

  useEffect(() => {
    (async () => await handleRequestOTP())();
  }, []);

  return (
    <>
      <form onSubmit={handleSubmitOTP}>
        <div className="relative flex flex-col p-4 overflow-y-auto text-primary-dark min-h-80">
          <h2 className="sticky mt-1 font-bold text-center text-lead">Mã xác thực</h2>
          {OTPInfo ? (
            <>
              <div className="pt-8 text-center">
                <p className="duration-300 fade-in">Mã xác thực (OTP) vừa được gửi đến số điện thoại của bạn</p>
              </div>
              <div className="mt-8">
                <Input
                  className="text-lead h-9"
                  containerClass="py-2"
                  type="text"
                  pattern="[0-9]*"
                  maxLength={6}
                  onChange={e => setOTPValue(e.target.value.trim())}
                  ref={otpInputRef}
                  placeholder="Nhập OTP"
                  reset={clearOTPValue}
                  required
                  inputMode="numeric"
                />
              </div>
              <div className="mt-8">
                <RetrySection onClickRetry={handleRequestOTP} />
              </div>
              <div className="flex flex-col items-center gap-2 mt-6">
                <Button
                  type="submit"
                  disabled={!isValidOTP}
                  variant="primary"
                  className="w-full py-3 text-base font-bold min-h-12 disabled:bg-dark-25 disabled:text-dark-200">
                  Xác nhận
                </Button>
              </div>
            </>
          ) : (
            <div className="flex items-center justify-center flex-1">
              <h2 className="font-bold text-lead ">
                <AnimatedGradientText text="Đang gửi mã xác thực" />
              </h2>
            </div>
          )}
        </div>
      </form>
    </>
  );
};

const RetrySection = ({ onClickRetry }: any) => {
  const [retryEnabled, setRetryEnabled] = useState(false);
  const countdownTime = 60;
  const [countdown, setCountdown] = useState(countdownTime);

  useEffect(() => {
    const interval = setInterval(() => {
      // Decrease countdown by 1 second
      setCountdown(prevCountdown => prevCountdown - 1);
    }, 1000); // 1 second in milliseconds

    // Enable the retry button once countdown reaches 0
    if (countdown === 0) {
      setRetryEnabled(true);
      clearInterval(interval); // Stop the countdown interval
    }

    return () => {
      clearInterval(interval); // Clean up the interval on component unmount
    };
  }, [countdown]);

  const handleRetryClick = () => {
    onClickRetry && onClickRetry();
    setRetryEnabled(false); // Disable button again after user click
    setCountdown(countdownTime); // Reset countdown
  };

  return (
    <div className="flex flex-col items-center justify-center">
      {retryEnabled ? (
        <Button variant="link" onClick={handleRetryClick} className={cn("p-2 h-6")}>
          Gửi lại mã
        </Button>
      ) : (
        <span>
          Thời gian nhập OTP còn{" "}
          <span className="font-mono text-primary">00:{countdown >= 10 ? countdown : `0${countdown}`}s</span>
        </span>
      )}
    </div>
  );
};
