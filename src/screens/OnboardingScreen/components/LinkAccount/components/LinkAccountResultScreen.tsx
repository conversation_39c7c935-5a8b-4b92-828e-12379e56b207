import { WarningCard } from "@/components/WarningCard";
import { useEffect } from "react";
import { LinkAccountResult, configResultContent } from "..";
import { Button } from "@/components/ui/Button";

export const LinkAccountResultScreen = ({
    result,
    onAction,
  }: {
    result: LinkAccountResult | undefined;
    onAction: () => void;
  }) => {
    if (!result) {
      return <WarningCard />;
    }
    const displayContent = configResultContent[result];
  
    useEffect(() => {
      let timer: NodeJS.Timeout;
      if(result === LinkAccountResult.Successed) {
        timer = setTimeout(onAction, 5000);
      }
      return () => clearTimeout(timer);
    }, [])
    return (
      <>
        <div className="grid gap-1.5 p-4 sm:text-left pb-6 pt-16">
          <div className="w-full mx-auto mb-6">
            <img loading="eager" src={displayContent.image} className="w-full min-h-[7.5rem] image-fade-in" />
          </div>
          <h2 className="pb-2 font-bold text-lead">{displayContent.title}</h2>
          <p className="text-base font-normal">{displayContent.description}</p>
        </div>
        <div className="flex flex-col items-center gap-2 p-4 mt-auto">
          <Button onClick={onAction} variant="primary" className="w-full py-3 text-base font-bold">
            {displayContent.cta}
          </Button>
        </div>
      </>
    );
  };