import { useEffect, useMemo, useRef, useState } from "react";
import { AnimatePresence, motion, MotionConfig } from "motion/react";
import useMeasure from "react-use-measure";
import { images } from "@/res";
import { onboardingStore } from "@/store/onboardingStore";
import { OTPType, PartnerCode } from "@/constants";
import { OTPRequestForm, postRequestOTP } from "@/api/postRequestOTP";
import { OTPVerifyForm, postVerifyOTP } from "@/api/postVerifyOTP";
import { WarningCard } from "@/components/WarningCard";
import { postLinkAccount } from "@/api/postLinkAccount";
import { OTPInput } from "./components/OTPInput";
import { SubmitLinkAccount } from "./components/SubmitLinkAccount";
import { LoadingScreen } from "./components/LoadingScreen";
import { LinkAccountResultScreen } from "./components/LinkAccountResultScreen";

export enum LinkAccountViewState {
  Submit,
  OTPInput,
  Loading,
  Result,
}

export enum LinkAccountResult {
  Failed = "Failed",
  Successed = "Successed",
}

export const configResultContent = {
  [LinkAccountResult.Failed]: {
    image: images.IconAccountVerificationRejected,
    title: "Liên kết thất bại",
    description: "Bấm ”Thử lại” để liên kết lại tài khoản tại ngân hàng CIMB của bạn với Zalopay",
    cta: "Thử lại",
  },
  [LinkAccountResult.Successed]: {
    image: images.IconAccountVerificationSuccessful,
    title: "Liên kết thành công",
    description: `Bấm “Tiếp tục" để chuyển đến bước tiếp theo hoặc hệ thống sẽ tự động chuyển tiếp sau: 5s`,
    cta: "Tiếp tục",
  },
};

const transition = {
  type: "spring",
  bounce: 0.2,
  duration: 0.5,
  transitionTimingFunction: "cubic-bezier(0.25, 1, 0.5, 1)",
};

const LinkAccount = ({ onResubmit }: { onResubmit: () => void }) => {
  const [view, setView] = useState<LinkAccountViewState>(LinkAccountViewState.Submit);
  const [elementRef, bounds] = useMeasure();
  const onboardingId = onboardingStore(state => state.onboardingId);
  const [resultLinkAccount, setResultLinkAccount] = useState<LinkAccountResult>();
  const timer = useRef<any>(null);

  const handleSubmitLinkAccount = async () => {
    setView(LinkAccountViewState.Loading);
    try {
      await postLinkAccount({ onboarding_id: onboardingId as string });
      setView(LinkAccountViewState.OTPInput);
    } catch {
      setResultLinkAccount(LinkAccountResult.Failed);
      setView(LinkAccountViewState.Result);
    }
  };

  const handleRequestOTP = async () => {
    try {
      const payload: OTPRequestForm = {
        onboarding_id: onboardingId as string,
        otp_type: OTPType.LINK_TYPE_3,
        partner_code: PartnerCode.CIMB,
      };
      const result = await postRequestOTP(payload);
      return result;
    } catch {
      setResultLinkAccount(LinkAccountResult.Failed);
      setView(LinkAccountViewState.Result);
    }
  };

  const handleSubmitOTP = async (otpCode: string) => {
    setView(LinkAccountViewState.Loading);
    try {
      const payload: OTPVerifyForm = {
        otp_type: OTPType.LINK_TYPE_3,
        onboarding_id: onboardingId as string,
        partner_code: PartnerCode.CIMB,
        otp_code: otpCode,
      };
      const result = await postVerifyOTP(payload);
      if (result?.status) {
        setResultLinkAccount(LinkAccountResult.Successed);
      } else {
        setResultLinkAccount(LinkAccountResult.Failed);
      }
    } catch {
      setResultLinkAccount(LinkAccountResult.Failed);
    }

    timer.current = setTimeout(() => setView(LinkAccountViewState.Result), 1500);
  };

  const handleResultAction = () => {
    if (!resultLinkAccount || resultLinkAccount === LinkAccountResult.Successed) {
      // Resubmit Onboarding Flow
      onResubmit();
    } else {
      // Resubmit Link Account Flow
      setResultLinkAccount(undefined);
      setView(LinkAccountViewState.Loading);
      timer.current = setTimeout(handleSubmitLinkAccount, 1500);
    }
  };

  const content = useMemo(() => {
    switch (view) {
      case LinkAccountViewState.Submit:
        return <SubmitLinkAccount onSubmit={handleSubmitLinkAccount} />;
      case LinkAccountViewState.OTPInput:
        return <OTPInput onRequestOTP={handleRequestOTP} onSubmitOTP={handleSubmitOTP} />;
      case LinkAccountViewState.Loading:
        return <LoadingScreen />;
      case LinkAccountViewState.Result:
        return <LinkAccountResultScreen result={resultLinkAccount} onAction={handleResultAction} />;
      default:
        return <WarningCard />;
    }
  }, [view]);

  useEffect(() => {
    return () => clearTimeout(timer.current);
  }, []);

  return (
    <motion.div
      animate={{
        height: bounds.height,
        transition: {
          duration: 0.27,
          ease: [0.25, 1, 0.5, 1],
        },
      }}>
      <div ref={elementRef}>
        <MotionConfig transition={transition}>
          <AnimatePresence initial={false} mode="wait">
            <motion.div
              initial={{ opacity: 0, scale: 0.96 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.96 }}>
              {content ? content : null}
            </motion.div>
          </AnimatePresence>
        </MotionConfig>
      </div>
    </motion.div>
  );
};

export default LinkAccount;




