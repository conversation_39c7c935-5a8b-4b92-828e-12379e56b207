import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "sonner";

import { ContractResponse, getContract } from "@/api/getContract";
import { FaceChallengeForm } from "@/api/postSubmitFaceChallenge";
import { GlobalDrawer } from "@/components/GlobalDrawer";
import PolicyDrawer from "@/components/PolicyDrawer";
import { LoadingIcon } from "@/components/ui/LoadingIcon";
import { ScreenKey, UMAuthType, UMStatus } from "@/constants";
import { IAuthChallenge, useAuthentication } from "@/hooks/useAuthentication";
import { useDocumentTitle } from "@/hooks/useDocumentTitle";
import { useTracking } from "@/hooks/useTracking";
import { useNavigation } from "@/lib/navigation/buildNavigator";
import { openInternalBrowser } from "@/lib/ZalopaySDK/openInternalBrowser";
import { UMAuthResult } from "@/lib/ZalopaySDK/startAuthChallenge";
import { animations, images } from "@/res";
import { appStore } from "@/store/appStore";
import { onboardingStore } from "@/store/onboardingStore";
import { INTERNAL_ERROR } from "@/types";
import loadable from "@loadable/component";
import { Checkbox } from "@zpi/looknfeel/checkbox";

import { INSTALLMENT_AUTH_CHALLENGE_SOURCE_ID, OnboardingState } from "../constant";
import { OnboardingFaceAuthChallenge, OnboardingOTP, ScreenId } from "../OnboardingTrackingId";
import { useOnboarding } from "../useOnboarding";
import OnboardingStepper from "./OnboardingStepper";

const Rive = loadable(() => import("@rive-app/react-canvas"), {
  resolveComponent: (components) => components.default,
});

export const FaceChallenge = () => {
  useDocumentTitle().setTitle("Xác Thực Khuôn Mặt");
  return <FaceChallengeWrapper />;
};

const enum FaceChallengeScreenState {
  AwaitingSubmit,
  Submitting,
  Retry,
}

export default FaceChallenge;

const FaceChallengeWrapper = () => {
  const navigation = useNavigation();
  const trackEvent = useTracking(ScreenId.OnboardingFaceAuthChallenge).trackEvent;
  const launchAuthChallenge = useAuthentication().launchAuthChallenge;
  const submitFaceChallenge = useOnboarding().submitFaceChallenge;
  const setOnBoardingState = onboardingStore.getState().setOnBoardingState;
  const [screenState, setScreenState] = useState(FaceChallengeScreenState.AwaitingSubmit);
  const onboardingId = onboardingStore((state) => state.onboardingId);
  const [approvedTnCChecked, setApprovedTnCChecked] = useState(true);
  const [approvedSignContractChecked, setApprovedSignContractChecked] = useState(true);
  const contractRef = useRef<ContractResponse>();
  const footerRef = useRef<HTMLDivElement>(null);
  const retryTime = useRef(0);
  const loading = appStore((state) => state.loading);

  const handleSetRetry = () => {
    retryTime.current++;
    setScreenState(FaceChallengeScreenState.Retry);
  };

  const handleSubmit = useCallback(async (faceRequestId: string) => {
    setScreenState(FaceChallengeScreenState.AwaitingSubmit);
    try {
      if (faceRequestId) {
        if (!onboardingId) {
          toast.error("Đã có lỗi khi gửi thông tin xác thực, xin thử lại", { position: "top-center" });
          throw "onboardingId not defined";
        }
        const payload: FaceChallengeForm = {
          onboarding_id: onboardingId,
          face_request_id: faceRequestId,
        };
        const result = await submitFaceChallenge(payload);
        if (result) {
          //trackEvent(OnboardingFaceAuthChallenge.ActionHandleAfterGetSelfieResult, { action_type: "otp_screen" });
          appStore.getState().setOpenApprovedRegisterDialog(true);
          setOnBoardingState(OnboardingState.APPROVAL_WAITING);
          navigation.replace(ScreenKey.HomeScreen);
        } else {
          handleSetRetry();
        }
      } else {
        handleSetRetry();
      }
    } catch {
      handleSetRetry();
    }
  }, []);

  const handleUMAuthResult = (data: UMAuthResult) => {
    trackEvent(OnboardingFaceAuthChallenge.GetCallBackSelfieResult, { selfie_result: data?.status });
    if (data && data.status === UMStatus.Success && data?.result) {
      handleSubmit(data?.result);
    } else if (data && data.status === UMStatus.Cancel) {
      trackEvent(OnboardingFaceAuthChallenge.CancelFaceAuth);
    } else {
      handleSetRetry();
    }
  };

  const handleFaceChallenge = async () => {
    trackEvent(OnboardingFaceAuthChallenge.ClickFaceAuth);

    const payload = {
      authType: UMAuthType.FaceAuth,
      source: INSTALLMENT_AUTH_CHALLENGE_SOURCE_ID,
      skipResult: true,
      numberOfRetries: 3,
    };

    try {
      const result = await launchAuthChallenge(payload as IAuthChallenge);
      handleUMAuthResult(result);
    } catch (error: any) {
      if (error?.errorCode && error.errorCode === INTERNAL_ERROR.CALL_FUNC_OPEN_AUTH_CHALLENGE_FAILED) {
        toast.error("Đã có lỗi trong quá trình xác thực, vui lòng thoát ứng dụng và mở lại.", {
          position: "top-center",
        });
      }
      trackEvent(OnboardingFaceAuthChallenge.GetUMLoadingStatus, { UM_loading_status: false, error });
      handleSetRetry();
    }
  };

  const handleRetry = () => {
    if (retryTime.current > 0) {
      trackEvent(OnboardingFaceAuthChallenge.ActionHandleAfterGetSelfieResult, { action_type: "retry" });
    } else {
      trackEvent(OnboardingFaceAuthChallenge.ActionHandleAfterGetUMStatus, { action_type: "retry" });
    }
    handleFaceChallenge();
  };

  const handleCheckApprovedTnC = () => {
    const checked = !approvedTnCChecked;
    trackEvent(OnboardingOTP.TickTnc, { status: checked ? "check" : "uncheck" });
    setApprovedTnCChecked(checked);
  };

  const handleViewContract = async () => {
    toast.info("Đang tải hợp đồng", { position: "top-center" });
    trackEvent(OnboardingOTP.ClickViewContract);
    if (contractRef.current?.unsigned_contract_url) {
      openInternalBrowser(contractRef.current.unsigned_contract_url);
      return;
    }
    try {
      if (!onboardingId) {
        throw "Empty OnboaringId";
      }
      const contract = await getContract({ onboarding_id: onboardingId });
      if (contract) {
        contractRef.current = contract;
      }
      if (contractRef.current?.unsigned_contract_url) {
        openInternalBrowser(contractRef.current.unsigned_contract_url);
      } else {
        throw "Empty contract url";
      }
    } catch {
      toast.error("Đã có lỗi khi lấy thông tin hợp đồng, vui lòng thử lại sau.");
    }
  };

  const handleCheckApprovedContract = () => {
    const checked = !approvedSignContractChecked;
    trackEvent(OnboardingOTP.TickApprovedContract, { status: checked ? "check" : "uncheck" });
    setApprovedSignContractChecked(checked);
  };

  const handleViewTnC = () => {
    trackEvent(OnboardingOTP.ClickViewTnc);
    GlobalDrawer.open({
      title: "Chính sách và điều khoản",
      children: <PolicyDrawer />,
    });
  };

  useEffect(() => {
    trackEvent(OnboardingFaceAuthChallenge.LoadFaceAuthChallenge);
  }, []);

  const isEnableSubmitButton = () => {
    if (!approvedTnCChecked) {
      return false;
    }
    if (!approvedSignContractChecked) {
      return false;
    }
    return true;
  };

  return (
    <div
      className="overflow-hidden relative h-screen bg-background animate-force-in [--force-in-delay:600ms] [--force-in-tranlateY:10px] flex flex-col justify-between"
      style={{ paddingBottom: footerRef?.current ? `${footerRef.current.clientHeight}px` : "3.5rem" }}>
      <div className="overflow-y-auto">
        <div className="w-full px-4 pt-4 mx-auto">
          <OnboardingStepper activeStep={2} />
        </div>
        <div className="w-full justify-center =">
          {screenState === FaceChallengeScreenState.Retry && (
            <div className="p-4">
              <div className="flex flex-col items-center self-stretch justify-center px-8 pb-4 text-center bg-white rounded-xl animate-force-in">
                <img loading="lazy" className="w-[180px] h-[180px]" src={images.ErrorState} />
                <div className="mt-6 text-base font-bold text-sky-950">Đã có lỗi xảy ra</div>
                <div className="mt-2 text-base leading-5 text-slate-500">
                  Đang có vấn đề trong quá trình gửi hồ sơ của bạn sang ngân hàng. Bạn vui lòng bấm “Xác thực lại” để
                  tiếp tục thực hiện nhé!
                </div>
              </div>
            </div>
          )}
          {screenState === FaceChallengeScreenState.AwaitingSubmit && (
            <div className="p-4">
              <div className="flex flex-col items-center justify-center gap-6 p-6 bg-white rounded-xl">
                <img className="w-[11.25rem]" src={images.ImageFaceChallenge} />
                <div className="flex flex-col items-center justify-center gap-2">
                  <h2 className="font-semibold">Chụp ảnh chân dung của bạn để xác minh</h2>
                </div>
              </div>
            </div>
          )}
          {screenState === FaceChallengeScreenState.Submitting && (
            <div className="p-4">
              <div className="flex flex-col items-center justify-center gap-6 p-6 bg-white rounded-xl">
                <Rive
                  width="100%"
                  height="100%"
                  className="relative w-[11.25rem] h-[11.25rem]"
                  src={animations.WaitingRiv}
                />
                <div className="flex flex-col items-center justify-center gap-2">
                  <h2 className="font-semibold">Bạn chờ tý nhé...</h2>
                  <span className="text-dark-300">Zalopay đang thực hiện xử lý hồ sơ</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      <div className="flex flex-col gap-3 px-4 py-3 bg-white">
        <p className="">Tôi đã đọc hợp đồng và:</p>
        <div className="flex items-center gap-2.5">
          <Checkbox id="approvedTnCCheckbox" onClick={handleCheckApprovedTnC} value="" checked={approvedTnCChecked} />
          <label htmlFor="approvedTnCCheckbox" className="text-base">
            Đồng ý với nội dung hợp đồng và{" "}
            <div onClick={handleViewTnC} className="inline cursor-pointer text-primary">
              chính sách bảo mật, điều khoản và điều kiện của CIMB
            </div>
          </label>
        </div>
        <div className="flex items-center gap-2.5">
          <Checkbox
            id="approvedSignContractCheckbox"
            onClick={handleCheckApprovedContract}
            value=""
            checked={approvedSignContractChecked}
          />
          <label htmlFor="approvedSignContractCheckbox" className="text-base">
            Đồng ý sử dụng ảnh chân dung để ký{" "}
            {onboardingId ? (
              <button onClick={handleViewContract} type="button" className="text-primary">
                hợp đồng
              </button>
            ) : (
              "hợp đồng"
            )}
          </label>
        </div>
      </div>
      <div ref={footerRef} className="fixed bottom-0 left-0 w-full bg-white">
        <div className="w-full px-4 py-3 bg-white">
          <button
            disabled={!isEnableSubmitButton() || loading}
            type="button"
            onClick={screenState === FaceChallengeScreenState.Retry ? handleRetry : handleFaceChallenge}
            className="flex justify-center items-center disabled:bg-dark-25 disabled:text-dark-200 active:scale-[98%] duration-200 bg-primary text-white text-lead font-semibold w-full h-12 py-2.5 rounded-lg">
            {loading ? (
              <LoadingIcon />
            ) : (
              <p>{screenState === FaceChallengeScreenState.Retry ? "Xác thực lại" : "Tiếp tục"}</p>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};
