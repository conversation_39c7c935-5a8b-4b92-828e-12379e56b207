import { GeneralCheckLineIcon } from "@/components/icon/GeneralCheckLineIcon";
import { WarningCard } from "@/components/WarningCard";
import { formatCurrency } from "@/utils/formatCurrency";
import { ReactElement, ReactNode, useEffect, useMemo } from "react";
import { BaseAddInfo, INFO_RESOURCE } from "../../constant";
import { OnboardingAddInformation } from "../../OnboardingTrackingId";
import { Drawer, DrawerClose, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle, DrawerTrigger } from "@/components/ui/Drawer";
import { Button } from "@/components/ui/Button";
import { XIcon } from "@/components/XIcon";

const SelectExtraInfoBottomSheet = ({
    title,
    formKey,
    resource,
    formValue,
    triggerContent,
    onChange,
    trackEvent,
    onClose,
}: {
    title: string,
    formKey: string | null | undefined;
    resource: any;
    formValue: any;
    triggerContent: ReactNode | ReactElement;
    onChange: (result: BaseAddInfo) => void;
    trackEvent?: (eventId: string, metadata?: Record<string, any>) => void;
    onClose?: () => void;
}) => {

    const options = useMemo(
        () => (resource ? resource.find((key: { type: string }) => key.type === formKey)?.data : []),
        [formKey, resource],
    );

    useEffect(() => {
        if (formKey) {
            trackEvent?.(OnboardingAddInformation.LoadInfoBS, { tab: INFO_RESOURCE[formKey].name });
        }

        return () => {
            onClose?.();
            trackEvent?.(OnboardingAddInformation.ClickCloseBS)
        };
    }, []);



    return (
        <Drawer repositionInputs={false} direction="bottom">
            <DrawerTrigger asChild>
                {triggerContent}
            </DrawerTrigger>
            <DrawerContent className="min-h-[12.5rem] bg-white">
                <DrawerHeader className="w-full gap-0 p-0 bg-white border-b border-solid rounded-t-lg border-dark-25">
                    <div className="flex-shrink-0 mx-auto mt-2 h-1.5 w-10 rounded-full bg-dark-50" />
                    <div className="relative flex items-center justify-center w-full h-12 p-4">
                        <DrawerTitle className="font-bold text-lead">{title}</DrawerTitle>
                        <DrawerDescription className="sr-only">{title}</DrawerDescription>
                        <DrawerClose
                            className=
                            "absolute transition-transform duration-300 -translate-y-1/2 top-1/2 right-4 active:scale-95"
                            asChild>
                            <Button variant="ghost" className="p-0 rounded-full h-fit">
                                <XIcon className="w-6 h-6 text-gray-500" strokeWidth={3} />
                            </Button>
                        </DrawerClose>
                    </div>
                </DrawerHeader>
                <div className="flex flex-col w-full mx-auto overflow-hidden bg-white">
                    <div key={formKey} className="px-4 py-3 overflow-y-auto">
                        {formKey && options.length > 0 ? (
                            options.map((option: any, idx: number) => {
                                const baseDelay = 50; //ms
                                const baseTranslateY = 16; //px

                                const style = {
                                    "--blurEnterFrom": 0,
                                    "--force-in-translateY": `${idx > 0 ? baseTranslateY + idx + 2 : baseTranslateY}px`,
                                    "--force-in-delay": `${idx > 0 ? 400 + baseDelay * idx : 400}ms`,
                                } as React.CSSProperties;
                                return (
                                    <DrawerTrigger
                                        onClick={() => {
                                            onChange(option);
                                        }}
                                        className="w-full py-4.5 border-b border-solid border-dark-25 flex flex-row justify-between items-center transform-gpu will-change-transform animate-force-in"
                                        style={style}
                                        key={option.code}
                                        ref={ref => {
                                            if (formValue && formValue.code === option.code) {
                                                ref?.scrollIntoView({ block: "center", behavior: "smooth" });
                                            }
                                        }}
                                    >
                                        <p className="truncate text-lead">
                                            {formKey === "INCOME" ? formatCurrency(Number(option.vietnamese)) : option.vietnamese}
                                        </p>
                                        {formValue && formValue.code === option.code ? (
                                            <GeneralCheckLineIcon />
                                        ) : null}
                                    </DrawerTrigger>
                                );
                            })
                        ) : <WarningCard />}
                    </div>
                </div>
            </DrawerContent>
        </Drawer>
    );
};

export default SelectExtraInfoBottomSheet;