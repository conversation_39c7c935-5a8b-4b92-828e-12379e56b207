import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { toast } from 'sonner';
import { useDebouncedCallback } from 'use-debounce';

import { GeneralCheckLineIcon } from '@/components/icon/GeneralCheckLineIcon';
import { LockupIcon } from '@/components/icon/LockupIcon';
import LocationPicker, {
    CurrentLocation, PermissionStatus
} from '@/components/ModuleFederation/LocationPicker';
import { Button } from '@/components/ui/Button';
import {
    Drawer, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle, DrawerTrigger
} from '@/components/ui/Drawer';
import { Input } from '@/components/ui/Input';
import { WarningCard } from '@/components/WarningCard';
import { XIcon } from '@/components/XIcon';
import { APP_NAME, OS } from '@/constants';
import { colors } from '@/constants/colors';
import { cn } from '@/lib/utils';
import { appStore } from '@/store/appStore';
import { formatCurrency } from '@/utils/formatCurrency';
import { normalizeVietnamese } from '@/utils/normalizeVietnamese';
import { GeneralGpsIc24 } from '@zpi/looknfeel-icons';

import { BaseAddInfo, INFO_RESOURCE } from '../../constant';
import { OnboardingAddInformation, ScreenId } from '../../OnboardingTrackingId';

const enum InfoKey {
  OCCUPATION = "OCCUPATION",
  JOB_TITLE = "JOB_TITLE",
  CITY = "CITY",
  INCOME = "INCOME",
}

const SelectInfoBottomSheet = ({
  infoKey,
  addInfoForm,
  open,
  resource,
  setOpen,
  onChange,
  setCurrentInfoKey,
  trackEvent,
}: {
  infoKey: keyof typeof InfoKey | string | null | undefined;
  addInfoForm: any;
  open: boolean;
  resource: any;
  setOpen: (open: boolean) => void;
  onChange: (result: BaseAddInfo) => void;
  setCurrentInfoKey: (infoKey: string) => void;
  trackEvent: (eventId: string, metadata?: Record<string, any>) => void;
}) => {
  const [keyWord, setKeyWord] = useState("");
  const keywordInputRef = useRef<HTMLInputElement>(null);
  const userId = JSON.parse(window?.__USER_INFO__)?.zalopay_id ?? null;
  
  if (!infoKey) {
    return null;
  }

  const handleChangeInfoKey = useCallback(
    (infoKey: string) => {
      trackEvent(OnboardingAddInformation.ClickInputTabBS, { tab: INFO_RESOURCE[infoKey].name });
      clearKeywordValue();
      setCurrentInfoKey(infoKey);
    },
    [infoKey, INFO_RESOURCE]
  );

  const handleNextStep = useCallback(() => {
    INFO_RESOURCE[infoKey]?.nextInfoKey ? handleChangeInfoKey(INFO_RESOURCE[infoKey]?.nextInfoKey) : setOpen(false);
  }, [infoKey, INFO_RESOURCE]);

  const handleChangeKeyWord = useDebouncedCallback((str) => {
    setKeyWord(str);
  }, 500);

  const handleOnFocusSearchInput = (e: any) => {
    trackEvent(OnboardingAddInformation.ClickSearch, {
      tab: INFO_RESOURCE[infoKey].name,
    });
  };

  const clearKeywordValue = () => {
    if (keywordInputRef && keywordInputRef.current) {
      keywordInputRef.current.value = "";
    }
    setKeyWord("");
  };

  const options = useMemo(
    () => (resource ? resource.find((key: { type: string }) => key.type === infoKey)?.data : []),
    [infoKey, resource]
  );

  const filterOptions = useMemo(
    () =>
      keyWord
        ? options.filter((item: any) => {
            const normalizedInput = normalizeVietnamese(keyWord.trim().toLowerCase());
            const normalizetTarget = normalizeVietnamese(item.vietnamese.toLowerCase());
            return normalizetTarget.indexOf(normalizedInput) >= 0;
          })
        : options,
    [keyWord, options]
  );

  const bottomSheetRef = useRef<HTMLDivElement>(null);
  const HEIGHT_CONTENT_BOTTOM_SHEET = "95%";
  const OFFSET_PERCENTAGE = 0.05; //5%

  useEffect(() => {
    const onResizeIOS = () => {
      try {
        if (window.visualViewport && appStore.getState().appInfo?.os !== OS.ANDROID && bottomSheetRef.current) {
          const visualViewportHeight = window.visualViewport.height;
          const keyboardHeight = window.innerHeight - visualViewportHeight;
          const offset = window.innerHeight * OFFSET_PERCENTAGE;
          const screenHeight = visualViewportHeight - offset;
          // detect keyboard show
          if (keyboardHeight > 0) {
            bottomSheetRef.current.style.height = `${Math.max(screenHeight, 0)}px`;
            bottomSheetRef.current.style.maxHeight = `${Math.max(screenHeight, 0)}px`;
            bottomSheetRef.current.style.bottom = `${Math.max(keyboardHeight, 0)}px`;
          } else {
            bottomSheetRef.current.style.height = HEIGHT_CONTENT_BOTTOM_SHEET;
            bottomSheetRef.current.style.maxHeight = HEIGHT_CONTENT_BOTTOM_SHEET;
            bottomSheetRef.current.style.bottom = "0";
          }
        }
      } catch (error) {
        console.log("Error onVisualViewportChange", error);
      }
    };
    window.visualViewport?.addEventListener("resize", onResizeIOS);
    return () => window.visualViewport?.removeEventListener("resize", onResizeIOS);
  }, []);

  useEffect(() => {
    const onResizeAndroid = () => {
      try {
        if (window.visualViewport && appStore.getState().appInfo?.os === OS.ANDROID && bottomSheetRef.current) {
          const visualViewportHeight = window.visualViewport.height;
          const offset = Math.max(innerHeight * OFFSET_PERCENTAGE, 0);
          const screenHeight = Math.max(visualViewportHeight - offset);

          bottomSheetRef.current.style.height = `${screenHeight}px`;
          bottomSheetRef.current.style.maxHeight = `${screenHeight}px`;
          bottomSheetRef.current.style.bottom = `0px`;
        }
      } catch (error) {
        console.log("Error onResizeAndroid", error);
      }
    };
    window.visualViewport?.addEventListener("resize", onResizeAndroid);
    return () => window.visualViewport?.removeEventListener("resize", onResizeAndroid);
  }, []);

  useEffect(() => {
    if (open) {
      trackEvent(OnboardingAddInformation.LoadInfoBS, { tab: INFO_RESOURCE[infoKey].name });
    }
    return () => clearKeywordValue();
  }, [open]);

  const handleOpenBottomSheet = (isOpen: boolean) => {
    !isOpen && trackEvent(OnboardingAddInformation.ClickCloseBS);
    setOpen(isOpen);
  };

  const handleGetDataFromLocationPicker = (data: CurrentLocation) => {
    const { shouldOpenSetting, location, permissionStatus } = data;
    trackEvent(OnboardingAddInformation.ClickShareLocation, { 
      service_name: APP_NAME,
      user_id: userId,
      location: location,
      touchpoint: 'address',
     });

    if (location?.city_name && options?.length > 0) {
      const matchedOption = options.find((option: { vietnamese: string }) =>
        normalizeVietnamese(option.vietnamese.toLowerCase()).includes(
          normalizeVietnamese(location.city_name.toLowerCase())
        )
      );

      if (matchedOption) {
        onChange(matchedOption);
        toast.success(`Đã tự động chọn nơi sống ${matchedOption.vietnamese} cho bạn.`);
        handleNextStep();
        return;
      } 
    }

    if (!location?.city_name && shouldOpenSetting) {
      //reset focus trap of Drawer
      document.body.style.pointerEvents = "auto";
      toast("Mở cài đặt để bật quyền truy cập vị trí.", {
        action: {
          label: "Mở",
          onClick: () => {
            window?.zlpSdk?.Device?.openSettings();
          },
        },
        duration: 5000,
      });
      return;
    }
    if ([PermissionStatus.PERMISSION_DENIED, PermissionStatus.PERMISSION_NOT_GRANTED].includes(permissionStatus)) {
      toast.error("Vui lòng bật quyền truy cập vị trí để sử dụng tính năng này.");
      return;
    }
  };

  return (
    <Drawer repositionInputs={false} direction="bottom" open={open} onOpenChange={handleOpenBottomSheet}>
      <DrawerContent
        ref={bottomSheetRef}
        className="bg-white bottom-0 left-0 right-0 !pointer-events-auto h-[95%] transition-[height] duration-300 ease-in-out ">
        <div className="flex flex-col w-full mx-auto overflow-hidden">
          <DrawerHeader className="sticky top-0 left-0 z-10 w-full p-0 bg-white rounded-t-extra-lg">
            <div className="w-screen pt-1.5 pb-3 px-4 flex flex-col gap-3 items-center border-b border-solid border-dark-25">
              <div className="w-10 h-1 rounded-full bg-dark-50"></div>
              <div className="w-full flex flex-row justify-between animate-force-in [--force-in-delay:200ms]">
                <div className="invisible">left</div>
                <DrawerTitle>Thông tin thêm</DrawerTitle>
                <DrawerDescription className="sr-only">Thông tin thêm</DrawerDescription>
                <DrawerTrigger>
                  <XIcon />
                </DrawerTrigger>
              </div>
            </div>
            <div className="w-screen">
              <div className="flex overflow-x-auto overflow-y-hidden h-fit flex-nowrap scrollbar-hide">
                {resource
                  ? resource.map((resource: any, idx: number) => {
                      if (!INFO_RESOURCE[resource.type]) {
                        return null;
                      }
                      return (
                        <Button
                          key={`resource_${idx}`}
                          variant="ghost"
                          ref={(ref) => {
                            if (resource.type === infoKey) {
                              ref?.scrollIntoView({ block: "center", behavior: "smooth" });
                            }
                          }}
                          onClick={() => handleChangeInfoKey(resource.type)}
                          className={cn(
                            "px-4 py-3.5 rounded-none border-solid border-b-0.5 border-transparent transform-gpu transition-colors duration-500",
                            resource.type === infoKey ? "border-b-primary text-primary" : ""
                          )}>
                          <span className="font-bold truncate text-responsive-base">
                            {INFO_RESOURCE[resource.type]?.name}
                          </span>
                        </Button>
                      );
                    })
                  : null}
              </div>
              <div className="relative px-4 pt-4">
                <Input
                  className="text-base"
                  maxLength={100}
                  ref={keywordInputRef}
                  onChange={(e) => handleChangeKeyWord(e.target.value)}
                  placeholder={`Tìm kiếm ${INFO_RESOURCE[infoKey].name}`}
                  reset={clearKeywordValue}
                  onFocus={handleOnFocusSearchInput}
                  icon={<LockupIcon />}
                />
                {infoKey === InfoKey.CITY ? (
                  <>
                    <div className="animate-image-show">
                      <LocationPicker
                        className="items-center justify-start w-full h-auto px-0 py-4 gap-3 active:scale-[98%]"
                        variant="ghost"
                        onLocationChange={handleGetDataFromLocationPicker}
                        screenMetadata={{
                          screen_id: ScreenId.OnboardingAddInformation,
                          user_id: userId,
                          service_name: APP_NAME,
                          touchpoint: 'address',
                        }}>
                        <GeneralGpsIc24 color={colors.primary.blue} />
                        <span className="font-normal text-primary text-lead">Sử dụng vị trí hiện tại của tôi</span>
                      </LocationPicker>
                    </div>
                  </>
                ) : null}
              </div>
            </div>
          </DrawerHeader>
          <div key={`${infoKey}_${keyWord}`} className="relative px-4 py-3 overflow-y-auto">
            {filterOptions && filterOptions.length > 0 ? (
              filterOptions.map((option: BaseAddInfo, idx: number) => {
                const baseDelay = 50; //ms
                const baseTranslateY = 16; //px

                const style = {
                  "--blurEnterFrom": 0,
                  "--force-in-translateY": `${idx > 0 ? baseTranslateY + idx + 2 : baseTranslateY}px`,
                  "--force-in-delay": `${idx > 0 ? 400 + baseDelay * idx : 800}ms`,
                } as React.CSSProperties;
                return (
                  <button
                    onClick={() => {
                      onChange(option);
                      handleNextStep();
                    }}
                    className="w-full py-4.5 border-b border-solid border-dark-25 flex flex-row justify-between items-center transform-gpu will-change-transform animate-force-in"
                    style={style}
                    key={`${option.code}_${idx}`}
                    ref={(ref) => {
                      if (addInfoForm[infoKey] && addInfoForm[infoKey].code === option.code) {
                        ref?.scrollIntoView({ block: "center", behavior: "smooth" });
                      }
                    }}>
                    <p className="truncate text-lead">
                      {infoKey === "INCOME" ? formatCurrency(Number(option.vietnamese)) : option.vietnamese}
                    </p>
                    {addInfoForm[infoKey] && addInfoForm[infoKey].code === option.code ? (
                      <GeneralCheckLineIcon />
                    ) : null}
                  </button>
                );
              })
            ) : (
              <WarningCard description={`Chưa tìm thấy ${INFO_RESOURCE[infoKey].name} này, thử tìm lại nhé bạn.`} />
            )}
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default SelectInfoBottomSheet;
