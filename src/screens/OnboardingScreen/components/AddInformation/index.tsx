import { useEffect, useRef, useState } from "react";
import { Controller } from "react-hook-form";

import { FullInfoUser } from "@/components/FullInfoUser";
import { GlobalDrawer } from "@/components/GlobalDrawer";
import { GeneralNextIcon } from "@/components/icon/GeneralNextIcon";
import { Button } from "@/components/ui/Button";
import { DrawerFooter, DrawerTrigger } from "@/components/ui/Drawer";
import { Input } from "@/components/ui/Input";
import { LoadingIcon } from "@/components/ui/LoadingIcon";
import { colors } from "@/constants/colors";
import { useDocumentTitle } from "@/hooks/useDocumentTitle";
import { useTracking } from "@/hooks/useTracking";
import { cn } from "@/lib/utils";
import { onboardingStore } from "@/store/onboardingStore";
import { formatCurrency } from "@/utils/formatCurrency";
import { Checkbox } from "@zpi/looknfeel/checkbox";

import { BaseAddInfo, EXTRA_RESOURCE, INFO_RESOURCE, OnboardingState } from "../../constant";
import { OnboardingAddInformation, ScreenId } from "../../OnboardingTrackingId";
import { useAddInformation } from "../../useAddInformation";
import OnboardingStepper from "../OnboardingStepper";
import FatcaContent from "./FatcaContent";
import SelectAddInfoBottomSheet from "./SelectAddInfoBottomSheet";
import SelectExtraInfoBottomSheet from "./SelectExtraInfoBottomSheet";

const AddInformation = () => {
  useDocumentTitle().setTitle("Bổ sung thông tin");
  const trackEvent = useTracking(ScreenId.OnboardingAddInformation).trackEvent;
  const {
    currentInfoKey,
    control,
    extraControl,
    formValues,
    resource,
    isValid,
    loading,
    updateAddInfo,
    updateExtraInfo,
    setCurrentInfoKey,
    submitApplication,
  } = useAddInformation();

  const [openBottomSheet, setOpenBottomSheet] = useState(false);
  const addressInputRef = useRef<HTMLInputElement>(null);
  const [isChecked, setChecked] = useState(true);
  const profileInfo = onboardingStore(state => state.profileInfo);

  const triggerSelectBottomSheet = (infoKey: string, fieldValue: any) => {
    const metadata = {
      tab: INFO_RESOURCE[infoKey].name,
      filled_status: !!fieldValue ? "filled" : "blank",
    };
    trackEvent(OnboardingAddInformation.ClickUpdateUserInput, metadata);
    setCurrentInfoKey(infoKey);
    setOpenBottomSheet(true);
  };

  const renderFormValue = (value: string | undefined | null) => {
    return value ? value : <span className="text-base text-dark-300">Chọn</span>;
  };



  const handleClickViewUserDetail = () => {
    trackEvent(OnboardingAddInformation.ClickDetailUserInfo);
    GlobalDrawer.open({
      title: "Chi tiết thông tin cá nhân",
      shouldScaleBackground: false,
      children: (
        <>
          <div className="px-4 py-3 flex flex-col gap-4 overflow-y-auto">
            <FullInfoUser profileInfo={profileInfo} />
            <ul className="flex flex-col py-2 bg-white rounded-lg text-lead">
              {Object.values(EXTRA_RESOURCE).map((extraItem: any, index) => (
                <li key={index} className="flex justify-between">
                  <Controller
                    name={extraItem.key}
                    control={extraControl}
                    render={({ field }) => (
                      <SelectExtraInfoBottomSheet
                        title={extraItem.name}
                        formKey={extraItem.key}
                        resource={resource}
                        formValue={field.value}
                        onChange={(result: BaseAddInfo) => {
                          updateExtraInfo(extraItem.key, result);
                        }}
                        triggerContent={<button
                          type="button"
                          className="flex-1 w-full shortScreen:py-2.5 p-4 flex flex-row flex-nowrap justify-between items-center active:scale-[98%] duration-300">
                          <p className="text-dark-300">{extraItem.name}</p>
                          <div className="max-w-[50%] flex flex-row gap-4 justify-end items-center">
                            <p className="truncate text-lead">{renderFormValue(field.value?.vietnamese)}</p>
                            <GeneralNextIcon className="rotate-90 flex-shrink-0" color={colors.blue[500]} width={12} height={12} />
                          </div>
                        </button>} />

                    )}
                  />
                </li>
              ))}
            </ul>
          </div>
          <DrawerFooter className="gap-3">
            <DrawerTrigger className="active:scale-[98%] duration-300 bg-primary text-white text-4 font-semibold w-full py-2.5 rounded-lg">
              Đóng
            </DrawerTrigger>
          </DrawerFooter>
        </>
      ),
    });
  }

  const handleOnFocusAddressInput = (e: any) => {
    trackEvent(OnboardingAddInformation.ClickUpdateAddressInput, {
      filled_status: !!e.target.value ? "filled" : "blank",
    });
  };

  const handleCheckTnC = () => {
    const nextValue = !isChecked;
    trackEvent(OnboardingAddInformation.ClickTnc, { status: !!nextValue ? "checked" : "unchecked" });
    setChecked(nextValue);
  };

  const handelUpdateAddInfo = (result: BaseAddInfo) => {
    trackEvent(OnboardingAddInformation.ClickInputItemBS, { tab: INFO_RESOURCE[currentInfoKey].name, value: result });
    updateAddInfo(currentInfoKey, result);
  };

  useEffect(() => {
    trackEvent(OnboardingAddInformation.LoadAddInformation);
  }, []);


  return (
    <>
      <section
        key={OnboardingState.PERMISSION_WAITING}
        id="AddInformation"
        className="relative h-screen bg-background animate-force-in [--force-in-delay:600ms] [--force-in-tranlateY:10px]">
        <div className="flex flex-col justify-start h-full">
          <div className="pb-[64px]">
            <div className="mx-auto px-4 pt-4">
              <OnboardingStepper activeStep={1} />
            </div>
            <div className="w-full pt-6 px-4">
              <Button
                type="button"
                onClick={handleClickViewUserDetail}
                className="w-full bg-white text-black border border-solid border-gray-100 rounded-lg px-4 py-3 flex flex-row justify-between items-center active:scale-[98%] duration-300">
                <p className="text-base">Thông tin cá nhân</p>
                <div className="max-w-[50%] flex flex-row flex-nowrap gap-4 justify-end items-center">
                  <p className="text-base truncate">{profileInfo?.full_name}</p>
                  <GeneralNextIcon width={12} height={12} className=" flex-shrink-0"/>
                </div>
              </Button>
            </div>
            <div className="mt-5 p-4 bg-background">
              <div className="rounded-lg p-4 bg-white">
                <h2 className="text-4 font-semibold pb-3">Thông tin thêm</h2>
                <ul>
                  {/* AddInformation Fields */}
                  {Object.values(INFO_RESOURCE).map((infoItem: any, index) => (
                    <li key={index}>
                      <Controller
                        name={infoItem.key}
                        control={control}
                        render={({ field }) => (
                          <button
                            type="button"
                            onClick={() => triggerSelectBottomSheet(infoItem.key, field.value)}
                            className="flex-1 w-full py-3 flex flex-row flex-nowrap justify-between items-center active:scale-[98%] duration-300">
                            <p className="text-base text-dark-300">{infoItem.name}</p>
                            <div className="max-w-[50%] flex flex-row gap-4 justify-end items-center">
                              <p className="truncate text-base">{renderFormValue(Number(field.value?.vietnamese) ? formatCurrency(Number(field.value?.vietnamese)) : field.value?.vietnamese)}</p>
                              <GeneralNextIcon className="rotate-90 flex-shrink-0" color={colors.blue[500]} width={12} height={12} />
                            </div>
                          </button>
                        )}
                      />
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            {/* Address Field */}
            <div className="p-4">
              <Controller
                name="ADDRESS.value"
                control={control}
                render={({ field }) => (
                  <>
                    <Input
                      {...field}
                      maxLength={255}
                      required
                      type="text"
                      ref={addressInputRef}
                      label="Địa chỉ liên hệ"
                      placeholder="Địa chỉ liên hệ"
                      aria-errormessage="Vui lòng nhập địa chỉ"
                      reset={() => field.onChange("")}
                      onBlur={() => {
                        field.onBlur();
                      }}
                      onFocus={handleOnFocusAddressInput}
                      onChange={(e) => field.onChange(e.target.value.trimStart())}
                    />
                    {!field.value ? (
                      <p className={cn("text-base text-red-400 my-1 ")}>Vui lòng nhập địa chỉ</p>
                    ) : null}
                  </>
                )}
              />

            </div>

            <div className="px-4 pt-4 flex items-center [&>label>label]:text-lead">
              <Checkbox onClick={handleCheckTnC} value="" checked={isChecked} label="Tôi xác nhận" />
            </div>
            <div className="p-4">
              <FatcaContent />
            </div>
          </div>
        </div>

        <SelectAddInfoBottomSheet
          resource={resource}
          infoKey={currentInfoKey}
          addInfoForm={formValues}
          setOpen={setOpenBottomSheet}
          setCurrentInfoKey={setCurrentInfoKey}
          onChange={(result: BaseAddInfo) => handelUpdateAddInfo(result)}
          open={openBottomSheet}
          trackEvent={(eventId: string, metadata: any) => trackEvent(eventId, metadata)}
        />
      </section>

      {/* Submit Button */}
      <div className="w-full fixed bottom-0 left-0 bg-white">
        <div className="w-full px-4 py-3 bg-white">
          <button
            disabled={!isValid || !isChecked}
            type="button"
            onClick={submitApplication}
            className="flex justify-center items-center disabled:bg-dark-25 disabled:text-dark-200 active:scale-[98%] duration-200 bg-primary text-white text-4 font-semibold w-full h-12 py-2.5 rounded-lg">
            {loading ? <LoadingIcon /> : <p>Tiếp tục</p>}
          </button>
        </div>
      </div>
    </>
  );
};

export default AddInformation;


