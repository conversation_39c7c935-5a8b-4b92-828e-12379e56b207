export const enum ScreenId {
    OnboardingBasicInfo= "6403",
    OnboardingAddInformation="6404",
    OnboardingFaceAuthChallenge="6405",
    OnboardingOTP="6406",
    OnboardingCongratPopup="6407"
}

export const enum OnboardingBasicInfo {
    LoadLandingPage = "000", // kyc_level, permission_status
    ClickViewDetailButton = "001", // kyc_level, permission_status, routing_action
    ClickMainCTA = "002", // kyc_level, permission_status, routing_action
    ClickSubCTA = "003", // kyc_level, permission_status
    SwipeIntro = "004", // kyc_level, permission_status, swipe: count
    ClickZLPTnC= "005", // kyc_level, permission_status
    LoadDetailUserInfo= "080", // kyc_level, permission_status
    ClickUpdateUserInfo = "081", // kyc_level, permission_status
    ClickCTAUserInfo = "082", //kyc_level, permission_status, action_type = Đóng/ Tiếp tục
    <PERSON>egisterFailed = "092", // kyc_level, permission_status, reason_code, detail_code
}

export const enum OnboardingAddInformation {
    LoadAddInformation = "000",
    ClickDetailUserInfo = "001",
    ClickUpdateUserInput = "002", //info_type: tên công việc/ chức danh/ nơi sống/ thu nhập | filled_status: filled/ blank =>  để biết user click vào đó là nhập mới hay sửa lại
    ClickUpdateAddressInput = "003", // filled_status: filled/ blank =>  để biết user click vào đó là nhập mới hay sửa lại
    ClickTnc = "004", // status = checked/ unchecked
    ClickSubmitInfo = "005",
    LoadInfoBS = "080", //tab = Tên công việc/ Chức Danh/ Nơi sống/ Thu nhập
    ClickSearch = "081", //tab = Tên công việc/ Chức Danh/ Nơi sống/ Thu nhập
    ClickInputTabBS = "082", //tab = Tên công việc/ Chức Danh/ Nơi sống/ Thu nhập
    ClickCloseBS = "083",
    ClickInputItemBS = "084", //tab = Tên công việc/ Chức Danh/ Nơi sống/ Thu nhập, value: input data
    CallSubmitInfoFailed = "092", // reason_code, detail_code
    ClickShareLocation = "006" // service_name: installment, user_id, ip, timestamp, location: {lat; long}, accuracy_radius
}

export const enum OnboardingFaceAuthChallenge {
    LoadFaceAuthChallenge = "000",
    GetUMLoadingStatus = "001",
    ActionHandleAfterGetUMStatus = "002", //Action = Thử lại/ Trang chủ/ route to UM to selfie	
    GetCallBackSelfieResult = "003", //selfie_result
    ActionHandleAfterGetSelfieResult = "004", //action_type = Thử lại/ Trang chủ/ route to OTP screen	
    ClickFaceAuth = "005", 
    CancelFaceAuth = "006",
}

export const enum OnboardingOTP {
    LoadOnboardingOTP = "000",
    InputOTP = "001",
    TickTnc = "002", //status = check/unchecked
    ClickViewTnc = "003", 
    TickApprovedContract = "004", //status = check/unchecked	
    ClickViewContract = "005",
    ClickResendOTP = "006",
    ClickSubmitOTP = "007", //otp_result = correct/ incorrect
}

export const enum OnboardingCongratPopup {
    LoadOnboardingCongratPopup = "000",
    ClickMainCTA = "001",
    ClickSubCTA = "002",
    ClickClose = "003",
}

export const enum OnboardingNoticeBS {
    LoadNoticeBS = "090",
    ClickMainCTA = "091",
}