import { colors } from "../../constants/colors";
import { images } from "../../res";

export enum OnboardingState {
  PERMISSION_WAITING = "PERMISSION_WAITING",
  REGISTER_WAITING = "REGISTER_WAITING",
  PERMISSION_REJECTED = "PERMISSION_REJECTED",
  CONTRACT_WAITING = "CONTRACT_WAITING",
  FACE_CHALLENGE_WAITING = "FACE_CHALLENGE_WAITING",
  OTP_WAITING = "OTP_WAITING",
  APPROVAL_WAITING = "APPROVAL_WAITING",
  APPROVAL_SUCCESS = "APPROVAL_SUCCESS",
  APPROVAL_REJECTED = "APPROVAL_REJECTED",
}

export const ONBOARDING_STATE_INFO: any = {
  PERMISSION_WAITING: {
    step: 0,
    card_cta: "<PERSON><PERSON>ng ký",
    card_title: "<PERSON><PERSON><PERSON> ký, nhận ngay ưu đãi",
    card_description: "<PERSON><PERSON><PERSON><PERSON> hồ sơ chỉ trong 5 phút",
  },
  REGISTER_WAITING: {
    step: 1,
    card_cta: "Tiế<PERSON> tục",
    card_title: "Đăng ký, nhận ngay ưu đãi",
    card_description: "Chỉ còn 3 bước nữa",
  },
  PERMISSION_REJECTED: {
    step: 0,
    color_card_cta: colors.red[400],
    card_cta: "Hồ sơ bị từ chối",
    card_title: "Hồ sơ của bạn bị từ chối",
    card_description: "Chỉ còn 3 bước nữa",
    dialog_content: {
      title: "Hồ sơ bị từ chối",
      description: "Hồ sơ của bạn bị từ chối, ngân hàng không phê duyệt hồ sơ của bạn",
      icon: images.ImgNotice,
    },
  },
  FACE_CHALLENGE_WAITING: {
    step: 2,
    card_cta: "Tiếp tục",
    card_title: "Đăng ký, nhận ngay ưu đãi",
    card_description: "Chỉ còn 2 bước nữa",
  },
  APPROVAL_WAITING: {
    step: 3,
    color_card_cta: "#FF8D00",
    card_cta: "Chờ duyệt",
    card_title: "Hồ sơ đang chờ duyệt",
    card_description: "Chỉ chút nữa thôi, bạn chờ tí nhé!",
    dialog_content: {
      title: "Chờ duyệt hồ sơ",
      description: "Hồ sơ của bạn đang được chờ duyệt, kết quả sẽ nhanh chóng được thông báo đến bạn",
      icon: images.ImgTofiConfident,
    },
  },
  APPROVAL_SUCCESS: {
    step: 3,
    card_cta: "Chi tiết",
    color_card_cta: colors.primary.blue,
    card_title: "Hồ sơ được duyệt thành công",
    card_description: "",
    dialog_content: {
      title: "Hồ sơ được duyệt thành công",
      description: "Hồ sơ của bạn được phê duyệt.",
      icon: images.ImgTofiConfident,
    },
  },
  APPROVAL_REJECTED: {
    step: 3,
    color_card_cta: colors.red[400],
    card_cta: "Hồ sơ bị từ chối",
    card_title: "Đăng ký nhận ưu đãi",
    card_description: "Chỉ còn 1 bước nữa",
    dialog_content: {
      title: "Hồ sơ bị từ chối",
      description: "Hồ sơ của bạn bị từ chối, ngân hàng không phê duyệt hồ sơ của bạn",
      icon: images.ImgNotice,
    },
  },
};

export const INFO_RESOURCE: any = {
  OCCUPATION: {
    id: 0,
    key: "OCCUPATION",
    name: "Tên công việc",
    nextInfoKey: "JOB_TITLE",
    prevInfoKey: null,
  },
  JOB_TITLE: {
    id: 1,
    key: "JOB_TITLE",
    name: "Chức danh",
    nextInfoKey: "CITY",
    prevInfoKey: null,
  },
  CITY: {
    id: 2,
    key: "CITY",
    name: "Nơi sống",
    nextInfoKey: "INCOME",
    prevInfoKey: "OCCUPATION",
  },
  INCOME: {
    id: 3,
    key: "INCOME",
    name: "Thu nhập",
    prevInfoKey: "OCCUPATION",
  },
};

export const EXTRA_RESOURCE: any = {
  SOURCE_OF_FUND: {
    key: "SOURCE_OF_FUND",
    name: "Nguồn thu nhập"
  },
  EDUCATION: {
    key: "EDUCATION",
    name: "Trình độ học vấn"
  },
  EMPLOYMENT_STATUS: {
    key: "EMPLOYMENT_STATUS",
    name: "Tình trạng việc làm"
  },
  FUND_PURPOSE: {
    key: "FUND_PURPOSE",
    name: "Mục đích sử dụng vốn"
  },
}

export interface BaseAddInfo {
  code?: number | string | undefined;
  vietnamese?: string | undefined;
  english?: string | undefined;
  value?: string | null | undefined;
}
export interface AddInfoForm {
  JOB_TITLE: BaseAddInfo;
  OCCUPATION: BaseAddInfo;
  CITY: BaseAddInfo;
  INCOME: BaseAddInfo;
  ADDRESS: BaseAddInfo;
}
export const initialAddInfoForm: AddInfoForm = {
  JOB_TITLE: {},
  OCCUPATION: {},
  CITY: {},
  INCOME: {},
  ADDRESS: {
    value: "",
  },
};

export interface OnboardingStatus {
  id: string;
  partner_code: string;
  status: string;
  current_step: string;
  next_step: string;
  created_at: string;
  updated_at: string;
  all_step: string[];
}

export const SIGN_IMAGE_EXPORT_TYPE = "image/jpeg";

export const ID_TYPE = {
  1: "Chứng minh nhân dân",
  2: "Hộ chiếu",
  3: "Căn cước công dân",
  4: "Giấy chứng minh sĩ quan",
  5: "Căn cước công dân gắn chip",
  6: "Căn cước"
};

export const INSTALLMENT_AUTH_CHALLENGE_SOURCE_ID = 3;
export const INSTALLMENT_ONBOARDING_NFC_SOURCE_ID = 7;
export const INSTALLMENT_REWARD_INVENTORY_ID = "Product_Installment_Voucher";
export const INSTALLMENT_PRODUCT_INVENTORY_ID = "Product_Installment_Merchant";