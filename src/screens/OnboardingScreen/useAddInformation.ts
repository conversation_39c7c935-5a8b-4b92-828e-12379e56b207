import { getResource } from "@/api/getResource";
import { postSubmitApplication } from "@/api/postSubmitApplication";
import useLocalStorage from "@/hooks/useLocalStorage";
import { trackEvent } from "@/lib/ZalopaySDK/tracking";
import { appStore } from "@/store/appStore";
import { onboardingStore } from "@/store/onboardingStore";
import { ResourceTypes } from "@/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { OnboardingState } from "./constant";
import { OnboardingAddInformation, ScreenId } from "./OnboardingTrackingId";

const addInforSchema = z.object({
  JOB_TITLE: z.object({
    code: z.string(),
    english: z.string(),
    vietnamese: z.string(),
  }),
  OCCUPATION: z.object({
    code: z.string(),
    english: z.string(),
    vietnamese: z.string(),
  }),
  CITY: z.object({
    code: z.string(),
    english: z.string(),
    vietnamese: z.string(),
  }),
  INCOME: z.object({
    code: z.string(),
    english: z.string(),
    vietnamese: z.string(),
  }),
  ADDRESS: z.object({
    value: z.string().min(1),
  }),
});

const extraInfoSchema = z.object({
  SOURCE_OF_FUND: z.object({
    code: z.string(),
    english: z.string(),
    vietnamese: z.string(),
  }),
  EDUCATION: z.object({
    code: z.string(),
    english: z.string(),
    vietnamese: z.string(),
  }),
  EMPLOYMENT_STATUS: z.object({
    code: z.string(),
    english: z.string(),
    vietnamese: z.string(),
  }),
  FUND_PURPOSE: z.object({
    code: z.string(),
    english: z.string(),
    vietnamese: z.string(),
  }),
});

export type AddInforFormData = z.infer<typeof addInforSchema>;
export type ExtraInfoFormData = z.infer<typeof extraInfoSchema>;

export const useAddInformation = () => {
  const [addInfoSnapshot, setAddInfoSnapshot] = useLocalStorage("add-info");
  const [currentInfoKey, setCurrentInfoKey] = useState<string>("JOB_TITLE");
  const [resource, setResource] = useState<ResourceTypes[]>();
  const setOnBoardingState = onboardingStore.getState().setOnBoardingState;
  const setNotice = onboardingStore.getState().setNotice;
  const onboardingId = onboardingStore.getState().onboardingId;
  const loading = appStore(state => state.loading);
  const setLoading = appStore.getState().setLoading;

  const { control, setValue, watch, formState } = useForm<AddInforFormData>({
    resolver: zodResolver(addInforSchema),
    defaultValues: addInfoSnapshot
      ? JSON.parse(addInfoSnapshot)
      : {
          JOB_TITLE: {},
          OCCUPATION: {},
          CITY: {},
          INCOME: {},
          ADDRESS: { value: "" },
        },
  });

  const {
    control: extraControl,
    setValue: setValueExtraForm,
    watch: watchExtraForm,
  } = useForm<ExtraInfoFormData>({
    resolver: zodResolver(extraInfoSchema),
    defaultValues: {
      SOURCE_OF_FUND: {
        code: "3",
        vietnamese: "Tiền lương",
        english: "Payroll",
      },
      EDUCATION: {
        code: "EDU003",
        vietnamese: "Đại học",
        english: "University",
      },
      EMPLOYMENT_STATUS: {
        code: "EPM001",
        vietnamese: "Toàn thời gian",
        english: "Full time",
      },
      FUND_PURPOSE: {
        code: "01",
        vietnamese: "Tiêu dùng cá nhân, mua sắm hàng hoá dịch vụ",
        english: "Personal finance",
      },
    },
  });

  const formValues = watch();
  const extraformValues = watchExtraForm();

  const updateAddInfo = (type: string, value: object | string | number) => {
    setValue(type as keyof AddInforFormData, value as any, { shouldValidate: true });
  };
  const updateExtraInfo = (type: string, value: object | string | number) => {
    setValueExtraForm(type as keyof ExtraInfoFormData, value as any, { shouldValidate: true });
  };

  const submitApplication = async () => {
    if (loading) return;
    setLoading(true);

    try {
      const payload = {
        temp_residence_address: formValues.ADDRESS.value,
        occupation: formValues.OCCUPATION.code,
        job_title: formValues.JOB_TITLE.code,
        monthly_income: formValues.INCOME.code,
        living_city: formValues.CITY.code,
        source_of_fund: extraformValues.SOURCE_OF_FUND.code,
        education: extraformValues.EDUCATION.code,
        employment_status: extraformValues.EMPLOYMENT_STATUS.code,
        fund_purpose: extraformValues.FUND_PURPOSE.code,
        onboarding_id: onboardingId as string,
      };

      const response = await postSubmitApplication(payload);
      if (response) {
        trackEvent(`${ScreenId.OnboardingAddInformation}.${OnboardingAddInformation.ClickSubmitInfo}`, {
          submit_profile_status: "success",
        });
        setAddInfoSnapshot("");
        setOnBoardingState(OnboardingState.FACE_CHALLENGE_WAITING);
      } else {
        trackEvent(`${ScreenId.OnboardingAddInformation}.${OnboardingAddInformation.ClickSubmitInfo}`, {
          submit_profile_status: "failed",
        });
        return false;
      }
    } catch (err: any) {
      trackEvent(`${ScreenId.OnboardingAddInformation}.${OnboardingAddInformation.ClickSubmitInfo}`, {
        submit_profile_status: "failed",
      });
      trackEvent(`${ScreenId.OnboardingAddInformation}.${OnboardingAddInformation.CallSubmitInfoFailed}`, {
        reason_code: err?.reason,
        detail_code: err?.metadata?.detail_code,
      });
      if (err?.metadata?.notice) {
        setNotice({
          content: err.metadata.notice,
          callback: submitApplication as any,
          metadata: { screenId: ScreenId.OnboardingAddInformation },
        });
        return false;
      }
      toast.error("Đã có lỗi hệ thống, vui lòng thử lại sau", { position: "top-center" });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setAddInfoSnapshot(JSON.stringify(formValues));
  }, [formValues]);

  useEffect(() => {
    if (
      (!addInfoSnapshot || JSON.parse(addInfoSnapshot)?.ADDRESS?.value === "") &&
      onboardingStore.getState().profileInfo?.permanent_address
    ) {
      updateAddInfo("ADDRESS", { value: onboardingStore.getState().profileInfo?.permanent_address as string });
    }

    (async () => {
      const result = await getResource();
      if (result?.resources) {
        setResource(result?.resources);
      }
    })();
  }, []);

  return {
    currentInfoKey,
    control,
    formValues,
    resource,
    isValid: Boolean(formState.isValid),
    loading,
    extraControl,
    updateAddInfo,
    updateExtraInfo,
    setCurrentInfoKey,
    submitApplication: submitApplication,
  };
};
