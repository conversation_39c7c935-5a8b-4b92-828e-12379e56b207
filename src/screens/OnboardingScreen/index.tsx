import { useCallback, useEffect } from "react";

import { onboardingStore } from "@/store/onboardingStore";
import loadable from "@loadable/component";

import { ONBOARDING_STATE_INFO, OnboardingState } from "./constant";
import R18AuthenticationContainer from "@/components/ModuleFederation/R18AuthenticationContainer";

const AddInformation = loadable(() => import("./components/AddInformation"));
const BasicInfo = loadable(() => import("./components/BasicInfo"));
const NoticeBottomSheet = loadable(() => import("./components/NoticeBottomSheet"));
const FaceChallenge = loadable(() => import("./components/FaceChallenge"));
const OnboardingDevUI = loadable(() => import("./components/OnboardingDevUI"));
const EmptyOnboarding = loadable(() => import("./components/EmptyOnboarding"));

const OnboardingScreen = () => {
  const getProfile = onboardingStore.getState().getProfile;
  const onBoardingState = onboardingStore(state => state.onBoardingState);
  const currentStepInfo = onBoardingState ? ONBOARDING_STATE_INFO[OnboardingState[onBoardingState]] : {};

  const renderScreen = useCallback(
    (currentStepInfo: any | undefined) => {
      const currentStep = currentStepInfo?.step || 0;
      switch (currentStep) {
        case 0:
          return <BasicInfo />;
        case 1:
          return <AddInformation />;
        case 2:
          return <FaceChallenge />;
        default:
          return <EmptyOnboarding />;
      }
    },
    [],
  );

  useEffect(() => {
    getProfile();
  }, []);

  return (
    <>
       <R18AuthenticationContainer/>
        {renderScreen(currentStepInfo)}
        <NoticeBottomSheet />
        <OnboardingDevUI />
    </>
  );
};

export default OnboardingScreen;
