import { INSTALLMENT_AUTH_CHALLENGE_SOURCE_ID, OnboardingState } from "./constant";
import { appStore } from "@/store/appStore";
import { onboardingStore } from "@/store/onboardingStore";
import { OnboardingActionCode, INTERNAL_ERROR } from "@/types";
import { useNavigation } from "@/lib/navigation/buildNavigator";
import {
  CIMB_TEL,
  OTPType,
  PartnerCode,
  PLATFORM,
  ScreenKey,
  UMAuthType,
  UMStatus,
  ZLP_CS_DEEPLINK,
  ZLP_CS_URL,
} from "@/constants";
import { launchDeeplink } from "@/lib/ZalopaySDK/launchDeeplink";
import { postRegister } from "@/api/postRegister";
import { toast } from "sonner";
import { OTPRequestForm, postRequestOTP } from "@/api/postRequestOTP";
import { OTPVerifyForm, postVerifyOTP } from "@/api/postVerifyOTP";
import { FaceChallengeForm, postSubmitFaceChallenge } from "@/api/postSubmitFaceChallenge";
import { IAuthChallenge, useAuthentication } from "@/hooks/useAuthentication";
import { launchChangePhone } from "@/helpers/launchChangePhone";
import { postResetNFC } from "@/api/postResetNFC";
import { OnboardingBasicInfo, ScreenId } from "./OnboardingTrackingId";
import { accountStore } from "@/store/accountStore";
import { BindStatus } from "@/types/bindStatus";
import { trackEvent } from "@/lib/ZalopaySDK/tracking";
import { isAppVersionGreaterThanOrEqual } from "@/utils/appVersion";

export const useOnboarding = () => {
  const loading = appStore((state) => state.loading);
  const setLoading = appStore.getState().setLoading;
  const launchAuthChallenge = useAuthentication().launchAuthChallenge;
  const { setOnBoardingState, getProfile, setOnboardingId, setOTPInfo, setNotice } = onboardingStore.getState();
  const setBindStatus = accountStore.getState().setBindStatus;
  const navigation = useNavigation();

  const onBasicInfoConfirm = async () => {
    try {
      if (loading) {
        return;
      }
      setLoading(true);
      const partnerCode = onboardingStore.getState().onboardingStatus?.onboardings[0]?.partner_code || "CIMB";
      const result = await postRegister({ partner_code: partnerCode as string });
      if (result.onboarding_id) {
        setOnboardingId(result.onboarding_id);
        setBindStatus(BindStatus.BIND_STATUS_ONBOARDING);
        setOnBoardingState(OnboardingState.REGISTER_WAITING);
      }
    } catch (e: any) {
      trackEvent(`${ScreenId.OnboardingBasicInfo}.${OnboardingBasicInfo.CallRegisterFailed}`, {
        kyc_level: onboardingStore.getState()?.profileInfo?.kyc_level,
        permission_status: accountStore.getState()?.permissionInfo?.bind_status,
        reason_code: e?.reason,
        detail_code: e?.metadata?.detail_code,
      });

      if (!e?.metadata?.notice) {
        toast.error("Đã có lỗi hệ thống, vui lòng thử lại sau", { position: "top-center" });
        return;
      }

      setNotice({
        content: e.metadata.notice,
        callback: onBasicInfoConfirm,
        metadata: { screenId: ScreenId.OnboardingBasicInfo },
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAuthChallenge = async (authType: UMAuthType, numberOfRetries?: number) => {
    const payload = {
      authType,
      source: INSTALLMENT_AUTH_CHALLENGE_SOURCE_ID,
      skipResult: false,
      numberOfRetries: numberOfRetries ? numberOfRetries : 3,
    };

    try {
      const result = await launchAuthChallenge(payload as IAuthChallenge);
      if (result) {
        if (result.status === UMStatus.Cancel) {
          return;
        }
        navigation.replace(ScreenKey.SplashScreen);
      }
      //WIP: handle failed case
    } catch (error: any) {
      if (error?.errorCode && error.errorCode === INTERNAL_ERROR.CALL_FUNC_OPEN_AUTH_CHALLENGE_FAILED) {
        toast.error("Đã có lỗi trong quá trình xác thực, vui lòng thoát ứng dụng và mở lại.", {
          position: "top-center",
        });
      }
      toast.error("Quá trình xác thực bị gián đoạn, vui lòng thử lại");
    }
  };

  const handleResetNFC = async () => {
    try {
      const platform = appStore.getState().appInfo?.platform;
      // TODO: remove this after release 10.12.0 all users
      if (platform === PLATFORM.ZPA && !isAppVersionGreaterThanOrEqual("10.12.0")) {
        await postResetNFC();
        await handleAuthChallenge(UMAuthType.NFC);
        return;
      }
      await handleAuthChallenge(UMAuthType.Reset_NFC);
    } catch {
      toast.error("Có lỗi khi thực hiện yêu cầu, vui lòng thử lại.");
    }
  };

  const navigateHandler = (code: OnboardingActionCode) => {
    switch (code) {
      case OnboardingActionCode.CONTACT_CS:
        return launchDeeplink({ zpi: ZLP_CS_URL, zpa: ZLP_CS_DEEPLINK });
      case OnboardingActionCode.CONTACT_PARTNER:
        return window.open(`tel:${CIMB_TEL}`);
      case OnboardingActionCode.DO_KYC:
        return handleAuthChallenge(UMAuthType.KYC_NFC);
      case OnboardingActionCode.UPDATE_KYC:
        return handleAuthChallenge(UMAuthType.Adjust);
      case OnboardingActionCode.UPDATE_KYC_NFC:
        return handleAuthChallenge(UMAuthType.Adjust_NFC);
      case OnboardingActionCode.UPDATE_NFC:
        return handleAuthChallenge(UMAuthType.NFC);
      case OnboardingActionCode.NAVIGATE_TO_HOME:
        return navigation.replace(ScreenKey.HomeScreen);
      case OnboardingActionCode.REGISTER_ONBOARDING:
        return onBasicInfoConfirm();
      case OnboardingActionCode.UPDATE_PHONE:
        return launchChangePhone();
      case OnboardingActionCode.RE_REGISTER_ONBOARDING:
        return navigation.replace(ScreenKey.HomeScreen);
      case OnboardingActionCode.RESET_NFC:
        return handleResetNFC();
      default:
        break;
    }
  };

  const submitFaceChallenge = async (payload: FaceChallengeForm) => {
    try {
      setLoading(true);
      const result = await postSubmitFaceChallenge({ ...payload });
      if (result?.onboarding_id) {
        return true;
      }
      return false;
    } catch (e: any) {
      if (e?.metadata?.notice) {
        setNotice({
          content: e.metadata.notice,
          callback: () => {
            submitFaceChallenge(payload);
          },
        });
      } else {
        toast.error("Đã có lỗi hệ thống, vui lòng thử lại sau", { position: "top-center" });
      }
      return false;
    } finally {
      setLoading(false);
    }
  };

  const requestOTP = async (onboarding_id: string, otp_type: string) => {
    try {
      setLoading(true);
      const payload: OTPRequestForm = {
        otp_type,
        onboarding_id,
        partner_code: "CIMB",
      };
      const result = await postRequestOTP(payload);
      setOTPInfo(result);
      return result;
    } catch (err: any) {
      if (err?.metadata?.notice) {
        setNotice({
          content: err.metadata.notice,
          callback: () => {
            requestOTP(onboarding_id, otp_type);
          },
        });
      } else {
        toast.error("Đã có lỗi hệ thống khi yêu cầu OTP, vui lòng thử lại sau", { position: "top-center" });
      }
      return false;
    } finally {
      setLoading(false);
    }
  };

  const verifyOTP = async (onboarding_id: string, otp_code: string) => {
    try {
      setLoading(true);
      const payload: OTPVerifyForm = {
        otp_type: OTPType.CONTRACT_SIGNING,
        onboarding_id,
        partner_code: PartnerCode.CIMB,
        otp_code: otp_code,
      };

      const result = await postVerifyOTP(payload);
      if (result?.status === true) {
        setOnBoardingState(OnboardingState.APPROVAL_WAITING);
        appStore.getState().setOpenApprovedRegisterDialog(true);
        navigation.replace(ScreenKey.HomeScreen);
      }
      return result;
    } catch (err: any) {
      if (err?.metadata?.notice) {
        setNotice({
          content: err.metadata.notice,
          callback: () => {
            verifyOTP(onboarding_id, otp_code);
          },
        });
      } else {
        toast.error("Đã có lỗi hệ thống, vui lòng thử lại sau", { position: "top-center" });
      }
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    navigateHandler,
    handleAuthChallenge,
    getProfile,
    setOnBoardingState,
    onBasicInfoConfirm,
    requestOTP,
    verifyOTP,
    submitFaceChallenge,
  };
};
