export const EmptyStatementIcon = (props: any) => {
  return (
    <svg width="121" height="120" viewBox="0 0 121 120" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g clipPath="url(#clip0_7193_52340)">
        <path
          d="M69.0211 20.2619C45.773 7.84805 26.8365 16.0344 17.4627 26.2964C-2.22336 47.8481 10.9004 93.5379 53.0855 94.4H79.3324C84.0195 94.4 102.768 94.4 109.33 81.4689C117.027 66.3009 109.33 54.7447 100.894 51.2964C92.4567 47.8481 87.7696 40.9515 84.9573 36.6412C82.1451 32.3308 76.5201 24.2661 69.0211 20.2619Z"
          fill="url(#paint0_radial_7193_52340)"
        />
        <ellipse cx="57.0996" cy="105.8" rx="23" ry="1.8" fill="url(#paint1_linear_7193_52340)" />
        <ellipse cx="57.0996" cy="105.8" rx="23" ry="1.8" fill="url(#paint2_linear_7193_52340)" />
        <circle
          cx="54.0166"
          cy="75.1208"
          r="11.5288"
          transform="rotate(25.1871 54.0166 75.1208)"
          fill="url(#paint3_linear_7193_52340)"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M77.26 25.0057C79.1103 25.8759 79.4898 27.4823 78.0518 29.7808C77.4939 30.6727 77.4223 31.258 77.9581 32.0052C83.2994 35.5543 89.3275 42.8681 84.4384 55.6093C83.9225 56.9538 83.4272 58.2346 82.9554 59.4546C78.2272 71.6811 75.8548 77.8155 78.6905 80.9885C81.6507 84.3007 80.8336 85.515 80.181 86.4849C80.146 86.5368 80.1116 86.588 80.0782 86.6388C79.2418 87.9114 77.7582 88.2845 75.3221 87.1389L53.4187 76.838L53.4104 76.8558L31.3885 66.4992C28.9524 65.3535 28.2937 63.9728 28.7404 62.517C28.7472 62.4947 28.754 62.4723 28.7608 62.4497C28.7716 62.4134 28.7825 62.3767 28.7934 62.3397C29.1242 61.2184 29.5383 59.8146 33.9775 59.9822C38.2299 60.1427 41.4418 54.403 47.8434 42.9635L47.8443 42.9617C48.4829 41.8208 49.1531 40.6231 49.8593 39.3689C56.1543 28.1884 64.9089 27.5007 71.0366 28.9964C71.9872 28.9711 72.6947 28.546 72.939 27.4953C73.4051 25.4916 74.7929 23.8454 77.26 25.0057Z"
          fill="url(#paint4_linear_7193_52340)"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M35.7523 39.1254C37.4733 36.9457 38.5004 34.1928 38.5004 31.2C38.5004 24.1307 32.7696 18.4 25.7004 18.4C18.6311 18.4 12.9004 24.1307 12.9004 31.2C12.9004 38.2692 18.6311 44 25.7004 44C28.1266 44 30.3951 43.325 32.3284 42.1525C33.9078 43.0691 36.314 42.9696 37.0133 42.9177C37.0656 42.9138 37.1187 42.9159 37.1717 42.918C37.2507 42.9212 37.3296 42.9243 37.4059 42.9076C37.5072 42.8856 37.595 42.8242 37.6521 42.7407C37.7787 42.5554 37.6075 42.3378 37.4411 42.1871C36.9471 41.7399 35.9207 40.6431 35.7523 39.1254Z"
          fill="white"
        />
        <path
          d="M96.7946 49.1159C99.6433 42.5711 97.9441 35.027 92.7119 30.2717C91.592 29.2538 89.8598 29.3244 88.8429 30.4294C87.8261 31.5344 87.9097 33.2553 89.0297 34.2732C92.5403 37.4638 93.68 42.5245 91.7639 46.9268C91.1652 48.3022 91.8061 49.9072 93.1953 50.5117C94.5845 51.1162 96.1959 50.4913 96.7946 49.1159Z"
          fill="#FFBE04"
        />
        <path
          d="M107.541 53.2787C112.38 42.1624 109.021 29.3108 99.5742 21.8539C98.3859 20.9159 96.6638 21.1068 95.7278 22.2803C94.7919 23.4539 94.9966 25.1656 96.185 26.1036C103.692 32.0293 106.361 42.2427 102.51 51.0894C101.911 52.4648 102.552 54.0698 103.941 54.6744C105.331 55.2789 106.942 54.654 107.541 53.2787Z"
          fill="#FCCD46"
        />
        <g clipPath="url(#clip1_7193_52340)">
          <path
            d="M40.0998 31.2C40.0998 39.1529 33.6527 45.6 25.6998 45.6C17.7469 45.6 11.2998 39.1529 11.2998 31.2C11.2998 23.2471 17.7469 16.8 25.6998 16.8C33.6527 16.8 40.0998 23.2471 40.0998 31.2Z"
            fill="white"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M25.6998 44.88C33.2551 44.88 39.3798 38.7553 39.3798 31.2C39.3798 23.6448 33.2551 17.52 25.6998 17.52C18.1445 17.52 12.0198 23.6448 12.0198 31.2C12.0198 38.7553 18.1445 44.88 25.6998 44.88ZM25.6998 45.6C33.6527 45.6 40.0998 39.1529 40.0998 31.2C40.0998 23.2471 33.6527 16.8 25.6998 16.8C17.7469 16.8 11.2998 23.2471 11.2998 31.2C11.2998 39.1529 17.7469 45.6 25.6998 45.6Z"
            fill="#F0F5FE"
          />
          <path
            d="M21.0862 24.5531C21.2373 24.3523 21.2611 24.0868 21.1488 23.8623C21.0365 23.6378 20.8099 23.4975 20.5584 23.4975H16.1016V24.783H19.3063L16.2484 28.8599C16.0972 29.0607 16.0735 29.3262 16.1858 29.5507C16.298 29.7752 16.5247 29.9155 16.7762 29.9155H21.2341V28.63H18.0294L21.0873 24.5531H21.0862Z"
            fill="#0033C9"
          />
          <path
            d="M32.7319 24.7801C31.3158 24.7801 30.1641 25.9318 30.1641 27.348C30.1641 28.7641 31.3158 29.9158 32.7319 29.9158C34.1481 29.9158 35.2998 28.7641 35.2998 27.348C35.2998 25.9318 34.1481 24.7801 32.7319 24.7801ZM32.7319 28.6292C32.0249 28.6292 31.4507 28.0539 31.4507 27.348C31.4507 26.642 32.026 26.0667 32.7319 26.0667C33.4379 26.0667 34.0132 26.642 34.0132 27.348C34.0132 28.0539 33.4379 28.6292 32.7319 28.6292Z"
            fill="#0033C9"
          />
          <path
            d="M18.6675 31.8442C17.2513 31.8442 16.0996 32.9959 16.0996 34.4121V38.9023H17.3852V36.6334C17.7629 36.8526 18.2001 36.9799 18.6664 36.9799C20.0826 36.9799 21.2343 35.8282 21.2343 34.4121C21.2343 32.9959 20.0826 31.8442 18.6664 31.8442H18.6675ZM18.6675 35.6933C17.9605 35.6933 17.3862 35.118 17.3862 34.4121C17.3862 33.7061 17.9616 33.1308 18.6675 33.1308C19.3734 33.1308 19.9487 33.7061 19.9487 34.4121C19.9487 35.118 19.3734 35.6933 18.6675 35.6933Z"
            fill="#00CF6A"
          />
          <path
            d="M31.3872 31.8453L30.0207 35.036L28.7211 31.8453H27.333L29.3083 36.6983L28.3638 38.9024H29.7627L32.7872 31.8453H31.3872Z"
            fill="#00CF6A"
          />
          <path
            d="M26.8433 28.6292V24.7801H25.5556V25.2496C25.1595 24.9593 24.6781 24.7866 24.1567 24.7866C22.8064 24.7866 21.7119 25.935 21.7119 27.3512C21.7119 28.7674 22.8064 29.9148 24.1567 29.9148C24.7882 29.9148 25.3624 29.6611 25.7963 29.2488C26.0597 29.6503 26.512 29.9158 27.0279 29.9158H27.4834V28.6292H26.8433ZM24.2765 28.6292C23.5706 28.6292 22.9975 28.0571 22.9975 27.3501C22.9975 26.6431 23.5695 26.071 24.2765 26.071C24.9835 26.071 25.5556 26.6431 25.5556 27.3501C25.5556 28.0571 24.9825 28.6292 24.2765 28.6292Z"
            fill="#0033C9"
          />
          <path
            d="M26.8433 35.6933V31.8442H25.5556V32.3137C25.1595 32.0234 24.6781 31.8507 24.1567 31.8507C22.8064 31.8507 21.7119 32.9991 21.7119 34.4153C21.7119 35.8315 22.8064 36.9788 24.1567 36.9788C24.7882 36.9788 25.3624 36.7252 25.7963 36.3129C26.0597 36.7144 26.512 36.9799 27.0279 36.9799H27.4834V35.6933H26.8433ZM24.2765 35.6933C23.5706 35.6933 22.9975 35.1212 22.9975 34.4142C22.9975 33.7072 23.5695 33.1351 24.2765 33.1351C24.9835 33.1351 25.5556 33.7072 25.5556 34.4142C25.5556 35.1212 24.9825 35.6933 24.2765 35.6933Z"
            fill="#00CF6A"
          />
          <path
            d="M29.2595 28.6289V23.4975H27.9707V28.4411C27.9707 29.255 28.6302 29.9156 29.4441 29.9156H29.9039V28.6289H29.2595Z"
            fill="#0033C9"
          />
        </g>
        <ellipse cx="46.7004" cy="28.6" rx="1.8" ry="1.8" fill="#3A8AFF" />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M94.2368 64.0771C93.8607 63.86 93.3798 63.9888 93.1627 64.3649L92.6909 65.1821L91.8736 64.7103C91.4975 64.4931 91.0166 64.622 90.7995 64.9981C90.5824 65.3742 90.7112 65.8551 91.0873 66.0722L91.9046 66.544L91.4328 67.3611C91.2157 67.7372 91.3446 68.2181 91.7206 68.4352C92.0967 68.6524 92.5776 68.5235 92.7947 68.1474L93.2665 67.3303L94.0835 67.8021C94.4596 68.0192 94.9405 67.8903 95.1576 67.5143C95.3747 67.1382 95.2459 66.6573 94.8698 66.4401L94.0528 65.9684L94.5246 65.1512C94.7417 64.7751 94.6129 64.2942 94.2368 64.0771Z"
          fill="#3A8AFF"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M23.3491 55.0744C23.0428 55.0744 22.7945 55.3227 22.7945 55.629V56.2946H22.1288C21.8225 56.2946 21.5742 56.5429 21.5742 56.8492C21.5742 57.1555 21.8225 57.4038 22.1288 57.4038H22.7945V58.0692C22.7945 58.3755 23.0428 58.6238 23.3491 58.6238C23.6554 58.6238 23.9037 58.3755 23.9037 58.0692V57.4038H24.5691C24.8754 57.4038 25.1237 57.1555 25.1237 56.8492C25.1237 56.5429 24.8754 56.2946 24.5691 56.2946H23.9037V55.629C23.9037 55.3227 23.6554 55.0744 23.3491 55.0744Z"
          fill="#97C2FF"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M53.7 26C55.0255 26 56.1 24.9255 56.1 23.6C56.1 22.2745 55.0255 21.2 53.7 21.2C52.3745 21.2 51.3 22.2745 51.3 23.6C51.3 24.9255 52.3745 26 53.7 26ZM53.7 26.8C55.4673 26.8 56.9 25.3673 56.9 23.6C56.9 21.8327 55.4673 20.4 53.7 20.4C51.9327 20.4 50.5 21.8327 50.5 23.6C50.5 25.3673 51.9327 26.8 53.7 26.8Z"
          fill="#97C2FF"
        />
      </g>
      <defs>
        <radialGradient
          id="paint0_radial_7193_52340"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(17.1 27) rotate(42.4432) scale(104.615 99.0602)">
          <stop stopColor="#DAE8FD" />
          <stop offset="0.386483" stopColor="#EFF6FF" />
          <stop offset="1" stopColor="#F7FAFF" />
        </radialGradient>
        <linearGradient
          id="paint1_linear_7193_52340"
          x1="67.9171"
          y1="108.256"
          x2="67.4923"
          y2="101.697"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#EEF5FF" />
          <stop offset="1" stopColor="#F7FAFF" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_7193_52340"
          x1="67.9171"
          y1="108.256"
          x2="67.4923"
          y2="101.697"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#EEF5FF" />
          <stop offset="1" stopColor="#F7FAFF" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_7193_52340"
          x1="54.2345"
          y1="80.7722"
          x2="54.0165"
          y2="86.6496"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#FFAB00" />
          <stop offset="1" stopColor="#FFBF3C" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_7193_52340"
          x1="78.2831"
          y1="82.6217"
          x2="56.7726"
          y2="69.395"
          gradientUnits="userSpaceOnUse">
          <stop stopColor="#FFAB00" />
          <stop offset="1" stopColor="#FFC83C" />
        </linearGradient>
        <clipPath id="clip0_7193_52340">
          <rect width="120" height="120" fill="white" transform="translate(0.5)" />
        </clipPath>
        <clipPath id="clip1_7193_52340">
          <rect width="28.8" height="28.8" fill="white" transform="translate(11.2998 16.8)" />
        </clipPath>
      </defs>
    </svg>
  );
};
