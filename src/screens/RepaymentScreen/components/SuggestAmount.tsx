import { useState, useEffect, FC } from "react";

interface SuggestAmountProps {
  currentAmount: number;
  maxAmount: number;
  onAmountChange: (amount: number) => void;
}

export const SuggestAmount: FC<SuggestAmountProps> = ({ currentAmount, maxAmount, onAmountChange }) => {
  const [suggestedAmounts, setSuggestedAmounts] = useState<number[]>([1000, 10000, 100000]);

  useEffect(() => {
    let newSuggestedAmounts;

    if (currentAmount === 0) {
      newSuggestedAmounts = [50000, 100000, maxAmount];
    } else {
      newSuggestedAmounts = [currentAmount * 1000, currentAmount * 10000, currentAmount * 100000];
    }

    newSuggestedAmounts = newSuggestedAmounts.filter(amount => amount <= maxAmount);
    if(newSuggestedAmounts.length <= 2) {
      newSuggestedAmounts.push(maxAmount)
    };
    setSuggestedAmounts(newSuggestedAmounts);
  }, [currentAmount, maxAmount]);

  return (
    <div className="flex space-x-2">
      {suggestedAmounts.map(amount => (
        <button
          key={amount}
          onClick={() => onAmountChange(amount)}
          className={"px-4 py-2 rounded-full bg-blue-25 text-black"}>
          {amount.toLocaleString("vi-VN")}đ
        </button>
      ))}
    </div>
  );
};
