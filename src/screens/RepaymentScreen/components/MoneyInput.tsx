import { useEffect } from "react";
import { useIMask } from "react-imask";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { SuggestAmount } from "./SuggestAmount";

const inputOptions = {
  mask: [
    {
      mask: "", // To hide đ if field is empty
    },
    {
      mask: "numđ",
      lazy: false,
      blocks: {
        num: {
          mask: Number,
          thousandsSeparator: ".",
          normalizeZeros: true,
          min: 0,
        },
      },
    },
  ],
};

export const MoneyInput = ({
  maxAmount,
  onAmountChange,
  onSetMaxAmount,
  onSetAmountBySuggest,
  onResetAmount,
}: {
  maxAmount: number;
  onAmountChange?: (amount: number) => void;
  onSetMaxAmount?: () => void;
  onSetAmountBySuggest?: (amount: number) => void;
  onResetAmount?: () => void;
}) => {
  const { ref, value, setValue, unmaskedValue, setUnmaskedValue } = useIMask(inputOptions);

  const setMaxAmount = () => {
    onSetMaxAmount?.();
    setValue(maxAmount.toString());
  };

  const setAmountBySuggest = (amount: number) => {
    onSetAmountBySuggest?.(amount);
    setUnmaskedValue(amount.toString());
  };

  const handleResetAmount = () => {
    onResetAmount?.();
    setUnmaskedValue("");
  };

  const handleOnChange = () => {
    if (parseInt(unmaskedValue) < 1) {
      setValue("0");
    } else if (value[0] === "0") {
      setValue(unmaskedValue);
    }
  };

  useEffect(() => {
    onAmountChange?.(parseInt(unmaskedValue));
  }, [unmaskedValue]);

  return (
    <div className="space-y-3">
      <div className="relative">
        <Input
          // @ts-ignore
          ref={ref}
          className="text-2xl leading-9 h-9"
          containerClass="px-4 py-3"
          type="tel"
          pattern="[0-9]"
          placeholder="Nhập số tiền"
          required
          inputMode="numeric"
          defaultValue={maxAmount ? maxAmount.toString() : "0"}
          onChange={handleOnChange}
          reset={handleResetAmount}
        />
        {!value && maxAmount && maxAmount > 0 ? (
          <div className="absolute top-2.5 right-0">
            <Button variant="link" className="text-base font-normal" onClick={setMaxAmount}>
              Thanh toán đủ
            </Button>
          </div>
        ) : null}
      </div>

      {maxAmount && maxAmount > 0 ? (
        <div className="min-h-10">
          <SuggestAmount
            currentAmount={parseInt(unmaskedValue) || (0 as number)}
            maxAmount={maxAmount}
            onAmountChange={setAmountBySuggest}
          />
        </div>
      ) : null}
    </div>
  );
};
