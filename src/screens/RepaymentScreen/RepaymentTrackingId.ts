export const enum ScreenId {
    RepaymentScreenId= "6411",
}

export const enum RepaymentScreenId {
    LoadRepaymentScreen = "001", // status = no_outstanding; before_due; due; over_due | outstanding = <outstanding left (number)> | due_date = <due date (format: DDMMYYYY>
    ClickDetailCTA = "002", // status = no_outstanding; before_due; due; over_due | outstanding = <outstanding left (number)> | due_date = <due date (format: DDMMYYYY>
    InputAmount = "003", // amount = <amount users input after out-focusing>
    ClickClearBtn = "004",
    ClickPayAll= "005", // amount = <amount users input after out-focusing>
    ClickSuggestAmountItem= "006", //amount = <amount users choose> | type = outstanding, default
    ClickPayBtn = "007"
}

