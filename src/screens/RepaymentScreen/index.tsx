import { formatDate } from "date-fns";
import debounce from "lodash.debounce";
import isNumber from "lodash.isnumber";
import { useEffect, useState } from "react";
import { Button as ReactAria<PERSON><PERSON><PERSON>, <PERSON><PERSON>, DialogTrigger, OverlayArrow, Popover } from "react-aria-components";
import { toast } from "sonner";

import { OrderRequestForm, postCreateOrder } from "@/api/postCreateOrder";
import { Button } from "@/components/ui/Button";
import { PartnerCode, ScreenKey } from "@/constants";
import { colors } from "@/constants/colors";
import { useDocumentTitle } from "@/hooks/useDocumentTitle";
import { useTracking } from "@/hooks/useTracking";
import { useNavigation } from "@/lib/navigation/buildNavigator";
import { images } from "@/res";
import { accountStore } from "@/store/accountStore";
import { statementStore } from "@/store/statementStore";
import { formatCurrency } from "@/utils/formatCurrency";
import { GeneralNoticeIc16 } from "@zpi/looknfeel-icons";

import { EmptyStatementIcon } from "./components/EmptyStatementIcon";
import { FlashPowerIcon } from "./components/FlashPowerIcon";
import { MoneyInput } from "./components/MoneyInput";
import { RepaymentScreenId, ScreenId } from "./RepaymentTrackingId";

const RepaymentScreen = () => {
  const { setTitle } = useDocumentTitle();
  setTitle("Thanh toán dư nợ");
  const trackEvent = useTracking(ScreenId.RepaymentScreenId).trackEvent;
  const navigation = useNavigation();
  const statement = statementStore((state) => state.lastestStatement);
  const totalRemainAmount = statement?.total_due_amount ? Number(statement.total_due_remaining) : 0;
  const dueDate = statement?.due_date ? statement?.due_date : "";
  const [repayAmount, setRepayAmount] = useState(totalRemainAmount);

  const handleViewDetailStatement = () => {
    trackEvent(RepaymentScreenId.ClickDetailCTA, {
      status: statement?.paid_status,
      outstanding: statement?.total_due_remaining,
      due_date: statement?.due_date,
    });
    navigation.navigate(ScreenKey.StatementScreen, { statementId: statement?.id });
  };

  const handleSetRepayAmount = debounce((amount: number) => {
    trackEvent(RepaymentScreenId.InputAmount, {
      amount: amount ? amount : 0,
    });
    setRepayAmount(amount);
  }, 400);

  const handleSetMaxAmount = () => {
    trackEvent(RepaymentScreenId.ClickPayAll, { amount: totalRemainAmount });
  };

  const handleSetAmountBySuggest = (amount: number) => {
    trackEvent(RepaymentScreenId.ClickSuggestAmountItem, { amount });
  };

  const handleResetAmount = () => {
    trackEvent(RepaymentScreenId.ClickClearBtn);
  };

  const handleCreateOrder = async (statementId: string, statementDate: string, amount: number) => {
    trackEvent(RepaymentScreenId.ClickPayBtn);

    if (amount <= 0) {
      toast.error("Số tiền phải lớn hơn 0");
      return;
    }

    try {
      const payload: OrderRequestForm = {
        amount: String(amount),
        partner_code: PartnerCode.CIMB,
        statement_id: statementId,
        statement_date: statementDate,
      };

      // const options = {
      //   "rp.redirect_data.type": "2",
      //   "rp.redirect_data.url":
      //     appStore.getState().appInfo?.platform === PLATFORM.ZPA
      //       ? buildDeeplink(APP_ID, { redirect: true, "redirect-path": rootBase })
      //       : appBase,
      // };
      const order = await postCreateOrder(payload);
      if (order && order.zp_trans_token && order.app_id) {
        await window.zlpSdk?.Payment.startCashier({
          orders: [
            {
              order_type: 1,
              order: {
                app_id: order.app_id,
                zp_trans_token: order.zp_trans_token,
              },
            },
          ],
          // options,
        });
      }
    } catch {
      toast.error("Đã có lỗi khi thực hiện yêu cầu");
    }
  };

  useEffect(() => {
    trackEvent(RepaymentScreenId.LoadRepaymentScreen, {
      status: statement?.paid_status,
      outstanding: statement?.total_due_remaining,
      due_date: statement?.due_date,
    });
  }, []);

  if (!statement) {
    return (
      <div className="flex flex-col items-center justify-center gap-6 px-8 pt-16">
        <img src={images.ImgEmptyStatement} width={180} height={180} />
        <div className="space-y-1 text-center">
          <h2 className="font-bold text-lead">Bạn chưa có chi tiêu trả góp</h2>
          <p className="text-base text-dark-300">
            Khám phá ưu đãi đặc quyền chỉ dành cho trả góp qua Zalopay và mua sắm thoả thích nhé
          </p>
        </div>
        <Button className="w-full" variant="outlined" onClick={() => navigation.replace(ScreenKey.HomeScreen)}>
          Trang chủ
        </Button>
      </div>
    );
  }

  const hasRemainAmount = totalRemainAmount > 0;
  const repaymentBalance = Number(accountStore.getState().accountInfo?.repayment_balance || 0);
  return (
    <>
      <div className="flex flex-col justify-between h-auto">
        <div className="pb-24">
          <div className="relative bg-primary">
            <img
              loading="lazy"
              src={images.BGRepayment}
              className="absolute inset-0 top-0 left-0 object-cover w-full h-full"
            />
            <div className="relative z-10 flex justify-center w-full p-4 pt-5">
              <div className="flex flex-row items-start justify-between w-full gap-2 p-4 text-base bg-white rounded-lg">
                <img src={images.ImgLoan} width={60} height={60} />
                <div className="flex-1 space-y-3">
                  <div className="flex justify-between">
                    <div>
                      <p>
                        Dư nợ: <strong>{formatCurrency(totalRemainAmount)}</strong>
                      </p>
                    </div>
                    <Button
                      onClick={handleViewDetailStatement}
                      variant="outlined"
                      className="h-auto px-2 py-1 text-xs rounded-full">
                      Chi tiết
                    </Button>
                  </div>
                  {dueDate ? (
                    <div className="flex justify-between">
                      <p>Hạn thanh toán:</p>
                      <p>
                        <strong>{formatDate(dueDate, "dd/MM/yyyy")}</strong>
                      </p>
                    </div>
                  ) : null}
                  {repaymentBalance && isNumber(repaymentBalance) ? (
                    <div className="flex justify-between">
                      <p>Số dư cấn trừ nợ:</p>
                      <p className="flex items-center justify-start gap-2">
                        <strong>{formatCurrency(repaymentBalance)}</strong>
                        <DialogTrigger>
                          <ReactAriaButton>
                            <GeneralNoticeIc16 color={colors.secondary.orange} />
                          </ReactAriaButton>
                          <Popover>
                            <OverlayArrow>
                              <svg
                                width="12"
                                height="8"
                                viewBox="0 0 12 8"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M6 0L0 8H12L6 0Z" fill="#FF8D00" />
                              </svg>
                            </OverlayArrow>
                            <Dialog className="px-3 py-2 text-base text-white rounded-lg outline-none max-w-52 bg-secondary-orange">
                              <div>
                                <span>
                                  Số tiền dư do thanh toán dư nợ thừa hoặc hoàn huỷ, sẽ được cấn trừ vào dư nợ vào ngày
                                  10 hằng tháng.
                                </span>
                              </div>
                            </Dialog>
                          </Popover>
                        </DialogTrigger>
                      </p>
                    </div>
                  ) : null}
                </div>
              </div>
            </div>
          </div>
          {hasRemainAmount ? (
            <div className="px-4 pt-5 bg-white">
              <div className="mb-4">
                <h2 className="font-bold">Số tiền thanh toán</h2>
              </div>
              <MoneyInput
                maxAmount={totalRemainAmount}
                onAmountChange={handleSetRepayAmount}
                onSetMaxAmount={handleSetMaxAmount}
                onSetAmountBySuggest={handleSetAmountBySuggest}
                onResetAmount={handleResetAmount}
              />
            </div>
          ) : (
            <>
              <style>{`
              body::before {
                background: ${colors.background}!important;
              }
            `}</style>
              <div className="px-4 pt-5">
                <div className="flex items-center justify-start mb-4 space-x-2">
                  <FlashPowerIcon />
                  <h2 className="font-bold">Tổng nợ kỳ này</h2>
                </div>
                <div className="flex flex-col items-center justify-between w-full bg-white rounded-lg shadow-sm ">
                  <EmptyStatementIcon />
                  <div className="px-8 py-6 space-y-2 text-center">
                    <h2 className="font-bold">Bạn đã thanh toán hết dư nợ kỳ này</h2>
                    <p className="text-dark-300">Sử dụng tài khoản trả góp để tận hưởng ưu đãi lớn nhé!</p>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
        {hasRemainAmount ? (
          <div className="fixed bottom-0 left-0 w-full p-4 bg-white">
            <Button
              disabled={!repayAmount || repayAmount <= 0}
              onClick={() => handleCreateOrder(statement.id, statement.incurred_date, repayAmount)}
              variant="primary"
              size="lg"
              className="w-full font-bold text-lead py-3.5 disabled:bg-dark-25 disabled:text-dark-200">
              Thanh toán {repayAmount ? `${repayAmount.toLocaleString("vi-VN")}đ` : ""}
            </Button>
          </div>
        ) : null}
      </div>
    </>
  );
};

export default RepaymentScreen;
