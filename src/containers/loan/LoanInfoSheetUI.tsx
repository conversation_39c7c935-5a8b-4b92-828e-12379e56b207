import { memo } from 'react';

import { LoanDetail } from '@/api/getLoanDetail';
import { Button } from '@/components/ui/Button';
import { Divider } from '@/components/ui/Divider';
import { formatCurrency } from '@/utils/formatCurrency';

export const LoanInfoSheetUI = ({ loanDetail, onClose }: { loanDetail: LoanDetail; onClose: () => void }) => {
  return (
    <>
      <div className="relative pb-20 overflow-y-auto bg-background">
        <div className="flex flex-col items-center w-full gap-3 p-4">
          <div className="w-full py-2 bg-white border border-solid rounded-lg border-blue-50">
            <ul className="text-base font-normal">
              <li className="px-4 py-4.5 flex justify-between items-center">
                <span className="text-dark-500">Tổng phải trả</span>
                <span className="font-bold">
                  {loanDetail?.installment?.total_amount_due
                    ? formatCurrency(Number(loanDetail?.installment?.total_amount_due))
                    : ""}
                </span>
              </li>
              <li className="px-4">
                <Divider />
              </li>
              <li className="px-4 py-4.5 flex justify-between items-center">
                <span className="text-dark-300">1. Số tiền vay</span>
                <span className="text-dark-500 text-end">
                  {loanDetail?.installment?.principal_amount
                    ? formatCurrency(Number(loanDetail?.installment?.principal_amount))
                    : ""}
                </span>
              </li>
              <li className="px-4 py-4.5 flex justify-between items-center">
                <span className="text-dark-300">
                  2. Phí trả góp {loanDetail?.installment?.tenure ? `(${loanDetail?.installment?.tenure} kỳ)` : ""}
                </span>
                <span className="text-dark-500 text-end">
                  {loanDetail?.installment?.interest_amount
                    ? formatCurrency(Number(loanDetail?.installment?.interest_amount))
                    : "0đ"}
                </span>
              </li>
              <li className="px-4 py-4.5 flex justify-between items-center">
                <span className="text-dark-300">3. Lãi phạt quá hạn</span>
                <span className="text-dark-500 text-end">
                  {loanDetail?.installment?.penalty_amount
                    ? Number(loanDetail?.installment?.penalty_amount) > 0
                      ? formatCurrency(Number(loanDetail?.installment?.penalty_amount))
                      : "Chưa phát sinh"
                    : "0đ"}
                </span>
              </li>
            </ul>
          </div>
          <div className="w-full bg-white border border-solid rounded-lg border-blue-50">
            <div className="text-base px-4 py-4.5 flex justify-between items-center">
              <span>Đã thanh toán</span>
              <span className="font-bold">
                {loanDetail?.installment?.total_paid_amount
                  ? formatCurrency(Number(loanDetail?.installment?.total_paid_amount))
                  : "0đ"}
              </span>
            </div>
          </div>
          <div className="w-full bg-white border border-solid rounded-lg border-blue-50">
            <div className="text-base px-4 py-4.5 flex justify-between items-center">
              <span>Còn phải trả</span>
              <span className="font-bold text-primary">
                {loanDetail?.installment?.total_remaining_amount
                  ? formatCurrency(Number(loanDetail?.installment?.total_remaining_amount))
                  : "0đ"}
              </span>
            </div>
          </div>
        </div>
        <div className="fixed bottom-0 left-0 w-full p-4 bg-white">
          <Button size="lg" className="w-full font-bold text-lead" onClick={onClose}>
            Đóng
          </Button>
        </div>
      </div>
    </>
  );
};

export default memo(LoanInfoSheetUI);