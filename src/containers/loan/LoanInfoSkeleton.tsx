import { Divider } from "@/components/ui/Divider";
import { colors } from "@/constants/colors";
import { Skeleton } from "@/components/ui/Skeleton";
import { memo } from "react";

export const LoanInfoSkeleton = () => {
  return (
    <>
      <div className="flex flex-col gap-2 mx-4 overflow-hidden bg-white border border-solid rounded-lg border-strokeV2">
        <div className="px-4 pt-4">
          <div className="flex items-center justify-between">
            <h2 className="text-base">
              <Skeleton className="h-4 w-[120px]" />
            </h2>
            <Skeleton className="w-20 h-4" />
          </div>
          <Divider color={colors.dark[100]} />
        </div>

        <ul className="flex flex-col w-full gap-1 text-base">
          <li>
            <div className="flex justify-between gap-2 p-4">
              <Skeleton className="h-[40px] w-[40px] rounded-xl" />
              <div className="flex flex-col items-start flex-1 gap-3">
                <h3>
                  <Skeleton className="w-32 h-4" />
                </h3>
                <Skeleton className="relative w-full h-1.5 bg-blue-50 rounded-full" />

                <div className="flex justify-start w-full gap-11">
                  <div className="flex justify-between">
                    <Skeleton className="w-2 h-2 m-2 rounded-full bg-primary" />
                    <div className="flex flex-col items-start gap-2">
                      <p>
                        <Skeleton className="h-4 w-7" />
                      </p>
                      <p>
                        <Skeleton className="h-3 w-14" />
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <Skeleton className="w-2 h-2 m-2 rounded-full bg-blue-50" />
                    <div className="flex flex-col items-start gap-2">
                      <p>
                        <Skeleton className="h-4 w-7" />
                      </p>
                      <p>
                        <Skeleton className="h-3 w-14" />
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div className="flex flex-col gap-2 mx-4 overflow-hidden bg-white border border-solid rounded-lg border-strokeV2">
        <div className="px-4 pt-4">
          <div className="flex items-center justify-between">
            <h2 className="text-base">
              <Skeleton className="h-[18] w-16" />
            </h2>
            <div className="flex">
              <p className="inline text-base">
                <Skeleton className="h-[18] w-10" />
              </p>
            </div>
          </div>
          <Divider color={colors.dark[100]} />
        </div>

        <ul className="flex flex-col w-full gap-1 px-4 text-base">
          <li>
            <div className="flex items-center justify-between gap-2 py-4">
              <Skeleton className="rounded-full w-9 h-9" />
              <div className="flex-1">
                <p className="pb-1 text-base truncate max-w-40">
                  <Skeleton className="h-5 w-9" />
                </p>
                <span className="text-tiny text-dark-300">
                  <Skeleton className="w-14 h-" />
                </span>
              </div>
              <div className="flex flex-col items-end justify-center gap-1">
                <p className="font-bold text-black">
                  <Skeleton className="h-5 w-9" />
                </p>
                <Skeleton className="h-5 w-9" />
              </div>
            </div>
          </li>
          <li>
            <div className="flex items-center justify-between gap-2 py-4">
              <Skeleton className="rounded-full w-9 h-9" />
              <div className="flex-1">
                <p className="pb-1 text-base truncate max-w-40">
                  <Skeleton className="h-5 w-9" />
                </p>
                <span className="text-tiny text-dark-300">
                  <Skeleton className="h-5 w-14" />
                </span>
              </div>
              <div className="flex flex-col items-end justify-center gap-1">
                <p className="font-bold text-black">
                  <Skeleton className="h-5 w-9" />
                </p>
                <Skeleton className="h-5 w-9" />
              </div>
            </div>
          </li>
          <li>
            <div className="flex items-center justify-between gap-2 py-4">
              <Skeleton className="rounded-full w-9 h-9" />
              <div className="flex-1">
                <p className="pb-1 text-base truncate max-w-40">
                  <Skeleton className="h-5 w-9" />
                </p>
                <span className="text-tiny text-dark-300">
                  <Skeleton className="h-5 w-14" />
                </span>
              </div>
              <div className="flex flex-col items-end justify-center gap-1">
                <p className="font-bold text-black">
                  <Skeleton className="h-5 w-9" />
                </p>
                <Skeleton className="h-5 w-9" />
              </div>
            </div>
          </li>
        </ul>
      </div>
    </>
  );
};

export default memo(LoanInfoSkeleton)