import { memo, useCallback, useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';

import {
    getLoanDetail, LoanDetail, RepaymentSchedule as IRepaymentSchedule
} from '@/api/getLoanDetail';
import BottomSheet from '@/components/BottomSheet';
import { GlobalDrawer } from '@/components/GlobalDrawer';
import { ScreenId, TransactionTrackingId } from '@/constants/tracking/TransactionTrackingId';
import EarlyDischargeSheet from '@/containers/early-discharge-sheet/index';
import RepaymentSchedule from '@/containers/repayment-schedule';
import RepaymentScheduleDetailSheet from '@/containers/repayment-schedule/RepaymentScheduleDetailSheet';
import { useTracking } from '@/hooks/useTracking';
import { AppAction } from '@/types';
import { getParameterByName } from '@/utils/getParameterByName';

import LoanInfoSheetUI from './LoanInfoSheetUI';
import LoanInfoSkeleton from './LoanInfoSkeleton';
import LoanSummary from './LoanSummary';

export const Loan = ({ zpTransId, transTitle }: { zpTransId: string; transTitle: string }) => {
  const trackEvent = useTracking(ScreenId.TransactionTrackingId).trackEvent;

  const [loanDetail, setLoanDetail] = useState<LoanDetail | undefined | null>(undefined);
  const [error, setError] = useState<Boolean>();
  const [openLoanInfoSheet, setOpenLoanInfoSheet] = useState(false);
  const [openEarlyDischargeSheet, setOpenEarlyDischargeSheet] = useState(false);

  const [openRepaymentScheduleDetailSheet, setOpenRepaymentScheduleDetailSheet] = useState(false);
  const repaymentScheduleDetailTarget = useRef<IRepaymentSchedule | undefined>();

  const handleGetLoanDetail = async () => {
    try {
      const loanDetailResult = await getLoanDetail(zpTransId);
      if (loanDetailResult) {
        setLoanDetail(loanDetailResult);
      } else {
        setLoanDetail(null);
        throw "empty loan detail";
      }
    } catch (e) {
      setError(true);
    }
  };

  const handleClickLoadLoanDetail = () => {
    trackEvent(TransactionTrackingId.ClickLoanDetail);
    if (!loanDetail) {
      toast.error("Không thể xem chi tiết giao dịch");
      return;
    }
    setOpenLoanInfoSheet(true);
  };

  const handleClickLoadTenorDetail = (repaymentSchedule: IRepaymentSchedule) => {
    trackEvent(TransactionTrackingId.ClickTenorDetail);
    if (!repaymentSchedule || !loanDetail?.installment.tenure) {
      toast.error("Đã có lỗi gì đó");
    }
    try {
      repaymentScheduleDetailTarget.current = repaymentSchedule;
      setOpenRepaymentScheduleDetailSheet(true);
    } catch {
      toast.error("Không thể xem chi tiết giao dịch");
      return;
    }
  };

  const handleOpenEarlyDischargeSheet = useCallback(() => {
    setOpenEarlyDischargeSheet(true);
  }, []);

  useEffect(() => {
    const action = getParameterByName("action");
    if (action === AppAction.OPEN_EARLY_DISCHARGE_BOTTOM_SHEET) {
      handleOpenEarlyDischargeSheet();
    }
    handleGetLoanDetail();
  }, []);

  if (error || loanDetail === null) {
    return (
      <div className="flex flex-col gap-2 mx-4 overflow-hidden bg-white border border-solid rounded-lg border-strokeV2">
        <h2 className="p-4">Không có thông tin khoản vay</h2>
      </div>
    );
  }

  if (loanDetail === undefined) {
    return <LoanInfoSkeleton />;
  }

  return (
    <>
      <LoanSummary
        loanDetail={loanDetail}
        onLoadLoanDetail={handleClickLoadLoanDetail}
        onLoadEarlyDischargeInfo={handleOpenEarlyDischargeSheet}
      />
      <RepaymentSchedule
        repaymentSchedules={loanDetail?.repayment_schedules}
        onClickLoadTenorDetail={handleClickLoadTenorDetail}
      />
      <BottomSheet title="Thông tin khoản vay" open={openLoanInfoSheet} onOpenChange={setOpenLoanInfoSheet}>
        <LoanInfoSheetUI loanDetail={loanDetail} onClose={() => setOpenLoanInfoSheet(false)} />
      </BottomSheet>
      <BottomSheet
        title="Lịch thanh toán"
        open={openRepaymentScheduleDetailSheet}
        onOpenChange={setOpenRepaymentScheduleDetailSheet}>
        {repaymentScheduleDetailTarget.current ? (
          <RepaymentScheduleDetailSheet
            onClose={() => GlobalDrawer.close()}
            repaymentSchedule={repaymentScheduleDetailTarget.current}
            tenure={loanDetail?.installment?.tenure as number}
          />
        ) : null}
      </BottomSheet>
      <EarlyDischargeSheet
        open={openEarlyDischargeSheet}
        onOpenChange={setOpenEarlyDischargeSheet}
        transTitle={transTitle}
        zpTransId={zpTransId}
      />
    </>
  );
};

export default memo(Loan);
