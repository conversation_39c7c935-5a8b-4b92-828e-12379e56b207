
import { LoanDetail } from "@/api/getLoanDetail";
import { Button } from "@/components/ui/Button";
import { Divider } from "@/components/ui/Divider";
import { colors } from "@/constants/colors";
import { images } from "@/res";
import { formatCurrency } from "@/utils/formatCurrency";

import { LoanInfoSkeleton } from "./LoanInfoSkeleton";
import { memo } from "react";

export const LoanSummary = ({
  loanDetail,
  
  onLoadLoanDetail,
  onLoadEarlyDischargeInfo,
}: {
  loanDetail?: LoanDetail;
  onLoadLoanDetail?: () => void;
  onLoadEarlyDischargeInfo?: () => void;
}) => {
    
  if (loanDetail === null) {
    return (
      <div className="flex flex-col gap-2 mx-4 overflow-hidden bg-white border border-solid rounded-lg border-strokeV2">
        <h2 className="p-4"><PERSON>hông có thông tin khoản vay</h2>
      </div>
    );
  }

  if (loanDetail === undefined) {
    return <LoanInfoSkeleton />;
  }

  return (
    <>
      <div className="flex flex-col gap-2 mx-4 overflow-hidden bg-white border border-solid rounded-lg border-strokeV2">
        <div className="px-4 pt-4">
          <div className="flex items-center justify-between">
            <h2 className="text-base">Thông tin khoản vay</h2>
            {onLoadLoanDetail ? (
              <Button
                onClick={onLoadLoanDetail}
                variant="ghost"
                className="!p-0 h-auto text-primary font-normal">
                Chi tiết
              </Button>
            ) : null}
          </div>
          <Divider color={colors.dark[100]} />
        </div>

        <ul className="flex flex-col w-full gap-1 text-base">
          <li>
            <div className="flex justify-between gap-2 p-4">
              <img src={images.ImgLoan} className="w-10 h-8" />
              <div className="flex flex-col items-start flex-1 gap-3">
                <h3 className="text-base font-bold">
                  Tổng phải trả:{" "}
                  {loanDetail?.installment?.total_amount_due
                    ? formatCurrency(Number(loanDetail?.installment?.total_amount_due))
                    : ""}
                  {loanDetail?.installment?.tenure ? ` - ${loanDetail?.installment?.tenure} kỳ` : ""}
                </h3>
                <div className="relative w-full h-1.5 bg-blue-50 rounded-full">
                  {/* progress bar */}
                  <div
                    className="absolute top-0 left-0 h-1.5 bg-primary rounded-full"
                    style={{
                      width: `${
                        ((Number(loanDetail?.installment?.total_paid_amount) || 0) /
                          (Number(loanDetail?.installment?.total_amount_due) || 0)) *
                        100
                      }%`,
                    }}></div>
                </div>
                <div className="flex justify-start w-full gap-11">
                  <div className="flex justify-between">
                    <div className="w-2 h-2 m-2 rounded-full bg-primary" />
                    <div className="flex flex-col items-start gap-2">
                      <p>Đã trả</p>
                      <p>
                        {loanDetail?.installment?.total_paid_amount
                          ? formatCurrency(Number(loanDetail?.installment?.total_paid_amount))
                          : ""}
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <div className="w-2 h-2 m-2 rounded-full bg-blue-50" />
                    <div className="flex flex-col items-start gap-2">
                      <p>Còn lại</p>
                      <p>
                        {loanDetail?.installment?.total_remaining_amount
                          ? formatCurrency(Number(loanDetail?.installment?.total_remaining_amount))
                          : ""}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {loanDetail?.early_discharge?.info?.allowed && (
              <div className="px-4 pb-4">
                <Button
                  variant="outlined"
                  onClick={onLoadEarlyDischargeInfo}
                  className="w-full h-12 px-5 py-4 tracking-wide text-center text-lead"
                  animation="scale">
                  Tất toán khoản vay
                </Button>
              </div>
            )}
          </li>
        </ul>
      </div>
    </>
  );
};

export default memo(LoanSummary);
