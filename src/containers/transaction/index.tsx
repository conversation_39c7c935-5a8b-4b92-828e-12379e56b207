import { useEffect, useState } from "react";

import { getTransaction, TransactionItem, TransactionType } from "@/api/getTransaction";
import { Button } from "@/components/ui/Button";
import withErrorBoundary from "@/hocs/withErrorBoundary";
import { useTracking } from "@/hooks/useTracking";
import { TransactionTrackingId, ScreenId } from "@/constants/tracking/TransactionTrackingId";
import Loan from "../loan";
import TransactionUI from "./TransactionUI";

const Transaction = ({ zpTransID }: { zpTransID: string }) => {
  const id = zpTransID;
  const trackEvent = useTracking(ScreenId.TransactionTrackingId).trackEvent;
  const [transaction, setTransaction] = useState<TransactionItem>();
  
  const [error, setError] = useState<Boolean>();

  const handleGetTransactionDetail = async () => {
    try {
      const result = await getTransaction(id);
      if (result?.transaction) {
        return result.transaction;
      } else {
        throw "empty transaction detail";
      }
    } catch (e) {
      throw "get transaction detail failed";
    }
  };

  const handleClickDetailOrder = () => {
    trackEvent(TransactionTrackingId.ClickOrderDetail);
  };

  useEffect(() => {
    (async () => {
      try {
        const transResult = await handleGetTransactionDetail();
        if (transResult) {
          setTransaction(transResult);
        }
      } catch (e) {
        setError(true);
      }
    })();
    trackEvent(TransactionTrackingId.LoadLoanDetailScreen);
  }, []);

  if (error) {
    return (
      <section className="flex flex-col items-center justify-center w-full gap-3 h-dvh">
        <p className="text-lg font-semibold">Có lỗi khi lấy thông tin giao dịch</p>
        <Button variant="outlined">Thử lại</Button>
      </section>
    );
  }
  return (
    <>
      <div className="relative w-full h-screen mx-auto overflow-hidden select-none bg-background">
        <div className="absolute z-0 h-1/3 w-full bg-gradient-to-b from-[#0033C9] via-[#003CBE] via-30% to-transparent"></div>
        <div className="absolute z-10 w-full h-full overflow-hidden">
          <div className="w-full h-full overflow-auto">
            <div className="flex flex-col w-full max-w-2xl gap-2 pt-4 pb-20 mx-auto">
              {transaction ? (
                <TransactionUI transaction={transaction} onClickDetailOrder={handleClickDetailOrder} />
              ) : null}
              {id && transaction?.type === TransactionType.TRANS_TYPE_PAYMENT ? (
                <Loan zpTransId={id} transTitle={transaction.remark} />
              ) : (
                ""
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
export default withErrorBoundary(Transaction);
