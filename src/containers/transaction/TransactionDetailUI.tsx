import { TransactionItem, TransactionStatus, TransactionType } from "@/api/getTransaction";
import { Button } from "@/components/ui/Button";
import { Divider } from "@/components/ui/Divider";
import { formatCurrency } from "@/utils/formatCurrency";
import { format } from "date-fns";
import { memo, useCallback } from "react";

export const TransactionDetailUI = ({ transaction, onClose }: { transaction: TransactionItem; onClose: () => void }) => {
  const transformTransactionStatus = useCallback((status: TransactionStatus) => {
    const StatusArr: { [key in TransactionStatus]: JSX.Element } = {
      [TransactionStatus.TRANS_STATUS_SUCCESS]: <span className="text-green-500 text-end">Thành công</span>,
      [TransactionStatus.TRANS_STATUS_FAILED]: <span className="text-red-500 text-end"><PERSON>h<PERSON><PERSON> b<PERSON><PERSON></span>,

      [TransactionStatus.TRANS_STATUS_PROCESSING]: <span className="text-orange-500 text-end">Ch<PERSON> xử lý</span>,
      [TransactionStatus.TRANS_STATUS_PENDING]: <span className="text-orange-500 text-end">Chờ xử lý</span>,
      [TransactionStatus.TRANS_STATUS_UNSPECIFIED]: <span className="text-orange-500 text-end">Chờ xử lý</span>,
    };
    const transformStatus = StatusArr[status] || <span className="text-orange-500 text-end">Chờ xử lý</span>;

    return transformStatus;
  }, []);

  const transformTransactionType = useCallback((transactionType: TransactionType) => {
    const TypeArr: { [key in TransactionType]: string } = {
      [TransactionType.TRANS_TYPE_PAYMENT]: "Mua hàng",
      [TransactionType.TRANS_TYPE_REPAYMENT]: "Trả nợ",
      [TransactionType.TRANS_TYPE_UNSPECIFIED]: "Không xác định",
    };
    const transformStatus = TypeArr[transactionType] || "Không xác định";

    return transformStatus;
  }, []);

  return (
    <>
      <div className="relative pb-20 overflow-y-auto bg-background">
        <div className="flex flex-col items-center w-full p-4">
          <div className="w-full py-2 bg-white border border-solid rounded-lg border-blue-50">
            <ul className="text-base font-normal">
              <li className="px-4 py-4.5 flex justify-between items-center">
                <span className="w-1/2 text-dark-500">Giá trị đơn hàng</span>
                <span className="flex-1 font-bold text-primary text-end">
                  {formatCurrency(Number(transaction.amount))}
                </span>
              </li>
              <li className="px-4">
                <Divider />
              </li>
              <li className="px-4 py-4.5 flex justify-between items-center">
                <span className="w-1/2 text-dark-300">Nội dung</span>
                <span className="flex-1 text-dark-500 text-end">{transaction.remark}</span>
              </li>
              <li className="px-4 py-4.5 flex justify-between items-center">
                <span className="w-1/2 text-dark-300">Loại giao dịch</span>
                <span className="flex-1 text-dark-500 text-end">{transformTransactionType(transaction.type)}</span>
              </li>
              <li className="px-4 py-4.5 flex justify-between items-center">
                <span className="w-1/2 text-dark-300">Thời gian</span>
                <span className="flex-1 text-dark-500 text-end">
                  {format(new Date(transaction.createdAt), "HH:mm - dd/MM/yyyy")}
                </span>
              </li>
              <li className="px-4 py-4.5 flex justify-between items-center">
                <span className="w-1/2 text-dark-300">Mã giao dịch Zalopay</span>
                <span className="flex-1 text-dark-500 text-end">{transaction.zpTransId}</span>
              </li>
              <li className="px-4 py-4.5 flex justify-between items-center">
                <span className="w-1/2 text-dark-300">Mã giao dịch ngân hàng</span>
                <span className="flex-1 text-dark-500 text-end">{transaction.partnerTransId}</span>
              </li>
              <li className="px-4 py-4.5 flex justify-between items-center">
                <span className="w-1/2 text-dark-300">Trạng thái</span>
                {transformTransactionStatus(transaction.status)}
              </li>
            </ul>
          </div>
        </div>
        <div className="fixed bottom-0 left-0 w-full p-4 bg-white">
          <Button size="lg" className="w-full font-bold text-lead" onClick={onClose}>
            Đóng
          </Button>
        </div>
      </div>
    </>
  );
};

export default memo(TransactionDetailUI);