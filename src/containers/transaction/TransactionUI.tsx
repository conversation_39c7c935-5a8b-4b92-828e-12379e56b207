import { format } from 'date-fns';
import { memo, useCallback, useState } from 'react';
import { toast } from 'sonner';

import { TransactionItem, TransactionStatus, TransactionType } from '@/api/getTransaction';
import { BottomSheet } from '@/components/BottomSheet';
import { Button } from '@/components/ui/Button';
import { Skeleton } from '@/components/ui/Skeleton';
import { colors } from '@/constants/colors';
import withErrorBoundary from '@/hocs/withErrorBoundary';
import { images } from '@/res';
import { formatCurrency } from '@/utils/formatCurrency';
import {
    GeneralCheckIc24, GeneralClosecircleIc24, GeneralListSecondary, GeneralLoadingSecondary,
    GeneralNextIc16, GeneralNoticeLineSecondary, GeneralTimeSecondary
} from '@zpi/looknfeel-icons';

import TransactionDetailUI from './TransactionDetailUI';

const TransactionUI = ({
  transaction,
  onClickDetailOrder,
}: {
  transaction: TransactionItem;
  onClickDetailOrder: () => void;
}) => {
  const [openDetailOrderDrawer, setOpenDetailOrderDrawer] = useState(false);

  const transformTransactionStatus = useCallback((status: TransactionStatus, type: TransactionType) => {
    const typeDesc =
      type === TransactionType.TRANS_TYPE_REPAYMENT
        ? "Giao dịch thanh toán dư nợ trả góp"
        : "Giao dịch thanh toán trả góp";

    const StatusArr: { [key in TransactionStatus]: JSX.Element } = {
      [TransactionStatus.TRANS_STATUS_SUCCESS]: (
        <div className="flex items-center gap-2 p-2 text-base text-green-200 rounded-lg bg-green-50">
          <GeneralCheckIc24 viewBox="0 0 24 24" className="w-6 h-6" color={colors.primary.green} />{" "}
          <span className="text-green-500">{typeDesc} thành công</span>
        </div>
      ),
      [TransactionStatus.TRANS_STATUS_FAILED]: (
        <div className="flex items-center gap-2 p-2 text-base text-red-200 rounded-lg bg-red-50">
          <GeneralClosecircleIc24 viewBox="0 0 24 24" className="w-6 h-6" color={colors.red[500]} />{" "}
          <span className="text-red-500">{typeDesc} thất bại</span>
        </div>
      ),

      [TransactionStatus.TRANS_STATUS_PROCESSING]: (
        <div className="flex items-center gap-2 p-2 text-base text-orange-200 rounded-lg bg-orange-50">
          <GeneralLoadingSecondary className="w-6 h-6" color={colors.orange[500]} />
          <span className="text-orange-500">{typeDesc} đang chờ xử lý</span>
        </div>
      ),
      [TransactionStatus.TRANS_STATUS_PENDING]: (
        <div className="flex items-center gap-2 p-2 text-base text-orange-200 rounded-lg bg-orange-50">
          <GeneralLoadingSecondary className="w-6 h-6" color={colors.orange[500]} />
          <span className="text-orange-500">{typeDesc} đang chờ xử lý</span>
        </div>
      ),
      [TransactionStatus.TRANS_STATUS_UNSPECIFIED]: (
        <div className="flex items-center gap-2 p-2 text-base rounded-lg text-dark-200 bg-dark-50">
          <GeneralNoticeLineSecondary className="w-6 h-6" color={colors.dark[500]} />
          <span className="text-dark-500">{typeDesc} đang chờ xử lý</span>
        </div>
      ),
    };
    const transformStatus = StatusArr[status] || (
      <div className="flex items-center gap-2 p-2 text-base rounded-lg text-dark-200 bg-dark-50">
        <GeneralNoticeLineSecondary className="w-6 h-6" color={colors.dark[500]} />
        <span className="text-dark-500">{typeDesc} đang chờ xử lý</span>
      </div>
    );

    return transformStatus;
  }, []);

  const handleClickDetailOrder = () => {
    onClickDetailOrder?.();

    if (!transaction) {
      toast.error("Không thể xem chi tiết giao dịch");
      return;
    }

    setOpenDetailOrderDrawer(true);
  };

  const defaultIcon =
    transaction?.type === TransactionType.TRANS_TYPE_REPAYMENT
      ? images.IconTransactionRePay
      : images.IconTransactionPay;
  return (
    <>
      <div className="flex flex-col w-full pt-5">
        <div className="flex flex-col gap-2 px-4 mx-4 text-base bg-white border border-solid rounded-lg border-strokeV2">
          <div className="relative">
            <div className="flex items-center justify-center w-10 h-10 mx-auto -mt-5 border-strokeV2">
              {transaction ? (
                <img
                  className="object-contain w-10 h-10 rounded-lg"
                  src={transaction.productIcon || defaultIcon}
                  alt="icon"
                />
              ) : (
                <Skeleton className="w-10 h-10 shadow-md" />
              )}
            </div>
          </div>
          <div className="flex flex-col items-center w-full gap-2">
            <p className="font-bold text-center text-lead">
              {transaction?.remark ? transaction?.remark : <Skeleton className="w-24 h-5 rounded-2xl" />}
            </p>
          </div>
          {transaction?.status ? (
            <div className="text-base">{transformTransactionStatus(transaction?.status, transaction.type)}</div>
          ) : (
            <Skeleton className="w-full h-10 bg-green-50" />
          )}
          <ul className="flex flex-col w-full text-base">
            <li className="flex justify-between py-2">
              {transaction?.amount ? (
                <Button
                  variant="ghost"
                  onClick={handleClickDetailOrder}
                  className="w-full h-auto !p-0 justify-between items-center font-normal">
                  <div className="flex items-center justify-start gap-3">
                    <GeneralListSecondary className="w-6 h-6" color={colors.primary.blue} />
                    <span>Giá trị đơn hàng</span>
                  </div>
                  <div className="flex items-center justify-end gap-1">
                    <span className="text-base text-primary">{formatCurrency(Number(transaction.amount))}</span>
                    <GeneralNextIc16 className="w-4 h-4" color={colors.primary.blue} />
                  </div>
                </Button>
              ) : (
                <Skeleton className="w-full h-6" />
              )}
            </li>
            <li className="flex justify-between py-2">
              {transaction?.amount ? (
                <>
                  <div className="flex items-center justify-start gap-3">
                    <GeneralTimeSecondary className="w-6 h-6" color={colors.primary.blue} />
                    <span>Thời gian</span>
                  </div>
                  <span>{format(new Date(transaction.createdAt), "HH:mm - dd/MM/yyyy")}</span>
                </>
              ) : (
                <Skeleton className="w-full h-6" />
              )}
            </li>
          </ul>
        </div>
      </div>
      <BottomSheet title="Thông tin đơn hàng" open={openDetailOrderDrawer} onOpenChange={setOpenDetailOrderDrawer}>
        {transaction ? (
          <TransactionDetailUI onClose={() => setOpenDetailOrderDrawer(false)} transaction={transaction} />
        ) : null}
      </BottomSheet>
    </>
  );
};
export default withErrorBoundary(memo(TransactionUI));
