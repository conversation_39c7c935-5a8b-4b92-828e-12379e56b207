import { LoanDetail, RepaymentSchedule, RepaymentScheduleStatus } from "@/api/getLoanDetail";
import { Button } from "@/components/ui/Button";
import { Divider } from "@/components/ui/Divider";
import { formatCurrency } from "@/utils/formatCurrency";
import { format } from "date-fns";
import { memo, useCallback } from "react";

export const RepaymentScheduleDetailSheet = ({
  repaymentSchedule,
  tenure,
  onClose,
}: {
  repaymentSchedule: RepaymentSchedule;
  tenure: number;
  onClose: () => void;
}) => {
  const transformRepaymentScheduleStatus = useCallback((status?: RepaymentScheduleStatus) => {
    const StatusArr: { [key in RepaymentScheduleStatus]: JSX.Element } = {
      [RepaymentScheduleStatus.REPAY_STATUS_DUE]: <span className="flex-1 text-orange-500 text-end">Đ<PERSON> đến hạn</span>,
      [RepaymentScheduleStatus.REPAY_STATUS_PAID]: <span className="flex-1 text-green-500 text-end"><PERSON><PERSON> thanh toán</span>,
      [RepaymentScheduleStatus.REPAY_STATUS_PENDING]: <span className="flex-1 text-dark-200 text-end">Chưa đến hạn</span>,
      [RepaymentScheduleStatus.REPAY_STATUS_UNSPECIFIED]: (
        <span className="flex-1 text-gray-500 text-end">Không xác định</span>
      ),
      [RepaymentScheduleStatus.REPAY_STATUS_OVERDUE]: <span className="flex-1 text-red-500 text-end">Đã quá hạn </span>,
    };
    const transformStatus = status ? (
      StatusArr[status] || <span className="flex-1 text-gray-500 text-end">Không xác định</span>
    ) : (
      <span className="flex-1 text-gray-500 text-end">Không xác định</span>
    );

    return transformStatus;
  }, []);

  return (
    <>
      <div className="relative pb-20 overflow-y-auto bg-background">
        <div className="flex flex-col items-center w-full gap-3 p-4">
          <div className="w-full py-2 bg-white border border-solid rounded-lg border-blue-50">
            <ul className="text-base font-normal">
              <li className="px-4 py-4.5 flex justify-between items-center">
                <span className="w-1/2 text-dark-500">Kỳ thanh toán</span>
                <span className="flex-1 font-bold text-end">{`Kỳ ${repaymentSchedule.seq_no}/${tenure}`}</span>
              </li>
              <li className="px-4">
                <Divider />
              </li>
              <li className="px-4 py-4.5 flex justify-between items-center">
                <span className="w-1/2 text-dark-300">1. Ngày đến hạn</span>
                <span className="flex-1 text-dark-500 text-end">
                  {repaymentSchedule?.due_date ? format(repaymentSchedule?.due_date, "dd/MM/yyyy") : ""}
                </span>
              </li>
              <li className="px-4 py-4.5 flex justify-between items-center">
                <span className="w-1/2 text-dark-300">2. Tình trạng</span>

                {repaymentSchedule.status ? transformRepaymentScheduleStatus(repaymentSchedule.status) : ""}
              </li>
            </ul>
          </div>
          <div className="w-full py-2 bg-white border border-solid rounded-lg border-blue-50">
            <ul className="text-base font-normal">
              <li className="px-4 py-4.5 flex justify-between items-center">
                <span className="w-1/2 text-dark-500">Còn phải trả</span>
                <span className="flex-1 font-bold text-end">
                  {repaymentSchedule?.total_remaining_amount
                    ? formatCurrency(Number(repaymentSchedule?.total_remaining_amount))
                    : ""}
                </span>
              </li>
              <li className="px-4">
                <Divider />
              </li>
              <li className="px-4 py-4.5 flex justify-between items-center">
                <span className="w-1/2 text-dark-300">1. Số tiền đến hạn</span>
                <span className="flex-1 text-dark-500 text-end">
                  {repaymentSchedule?.due_amount
                    ? formatCurrency(Number(repaymentSchedule?.due_amount))
                    : "0đ"}
                </span>
              </li>
              <li className="px-4 py-4.5 flex justify-between items-center">
                <span className="w-1/2 text-dark-300">2. Lãi phạt quá hạn</span>

                {repaymentSchedule?.penalty_amount ? formatCurrency(Number(repaymentSchedule?.penalty_amount)) : "0đ"}
              </li>
              <li className="px-4 py-4.5 flex justify-between items-center">
                <span className="w-1/2 text-dark-300">3. Đã thanh toán</span>

                {repaymentSchedule?.total_paid_amount
                  ? formatCurrency(Number(repaymentSchedule?.total_paid_amount))
                  : "0đ"}
              </li>
            </ul>
          </div>
        </div>
        <div className="fixed bottom-0 left-0 w-full p-4 bg-white">
          <Button size="lg" className="w-full font-bold text-lead" onClick={onClose}>
            Đóng
          </Button>
        </div>
      </div>
    </>
  );
};

export default memo(RepaymentScheduleDetailSheet);