import { RepaymentSchedule as IRepaymentSchedule, RepaymentScheduleStatus } from "@/api/getLoanDetail";
import { Button } from "@/components/ui/Button";
import { Divider } from "@/components/ui/Divider";
import { colors } from "@/constants/colors";
import { images } from "@/res";
import { formatCurrency } from "@/utils/formatCurrency";
import { format } from "date-fns";
import { memo, useCallback } from "react";

export const RepaymentSchedule = ({
  repaymentSchedules,
  onClickLoadTenorDetail,
}: {
  repaymentSchedules?: IRepaymentSchedule[];
  onClickLoadTenorDetail: (item: IRepaymentSchedule) => void;
}) => {
    
  const transformRepaymentScheduleStatus = useCallback((status?: RepaymentScheduleStatus) => {
    const StatusArr: { [key in RepaymentScheduleStatus]: JSX.Element } = {
      [RepaymentScheduleStatus.REPAY_STATUS_DUE]: <span className="text-orange-500"><PERSON><PERSON> đến hạn</span>,
      [RepaymentScheduleStatus.REPAY_STATUS_PAID]: <span className="text-green-500">Đã thanh toán</span>,
      [RepaymentScheduleStatus.REPAY_STATUS_PENDING]: <span className="text-dark-200">Chưa đến hạn</span>,
      [RepaymentScheduleStatus.REPAY_STATUS_UNSPECIFIED]: <span className="text-gray-500">Không xác định</span>,
      [RepaymentScheduleStatus.REPAY_STATUS_OVERDUE]: <span className="text-red-500">Đã quá hạn </span>,
    };
    const transformStatus = status ? (
      StatusArr[status] || <span className="text-gray-500">Không xác định</span>
    ) : (
      <span className="text-gray-500">Không xác định</span>
    );

    return transformStatus;
  }, []);
  return (
    <div className="flex flex-col gap-2 mx-4 overflow-hidden bg-white border border-solid rounded-lg border-strokeV2">
      <div className="px-4 pt-4">
        <div className="flex items-center justify-between">
          <h2 className="text-base">Lịch thanh toán</h2>
          <div className="flex">
            {/* <p className="inline text-base">
                Hiện cả{" "}
                <span className="text-primary">
                  {loanDetail?.installment?.tenure ? loanDetail?.installment?.tenure : ""}
                </span>{" "}
                kỳ
              </p> */}
          </div>
        </div>
        <Divider color={colors.dark[100]} />
      </div>

      <ul className="flex flex-col w-full gap-1 px-4 text-base">
        {repaymentSchedules && repaymentSchedules.length > 0
          ? repaymentSchedules.map((item: IRepaymentSchedule, idx: number) => (
              <li key={idx}>
                <Button
                  onClick={() => onClickLoadTenorDetail(item)}
                  variant="ghost"
                  className="flex items-center justify-between w-full h-auto gap-2 px-0 py-4 font-normal">
                  <img src={images.IconTransactionRePay} className="w-9 h-9" />
                  <div className="flex-1 text-left">
                    <p className="pb-1 text-base truncate max-w-40">Kỳ {item?.seq_no}</p>
                    <span className="text-tiny text-dark-300">
                      Đến hạn: {item?.due_date ? format(new Date(item?.due_date), "dd/MM/yyyy") : ""}
                    </span>
                  </div>
                  <div className="flex flex-col items-end justify-center gap-1">
                    <p className="text-base font-bold text-black">
                      {item?.total_due_amount ? formatCurrency(Number(item?.total_due_amount)) : ""}
                    </p>
                    <div className="text-tiny">{transformRepaymentScheduleStatus(item.status)}</div>
                  </div>
                </Button>
              </li>
            ))
          : null}
      </ul>
    </div>
  );
};

export default memo(RepaymentSchedule);