import { memo, useEffect, useState } from "react";
import { Button } from "@/components/ui/Button";
import { Divider } from "@/components/ui/Divider";
import { images } from "@/res";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/Drawer";
import { formatCurrency } from "@/utils/formatCurrency";
import { getEarlyDischarge, EarlyDischargeInfo } from "@/api/getEarlyDischarge";
import { cn } from "@/lib/utils";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { XIcon } from "@/components/XIcon";
import { Skeleton } from "@/components/ui/Skeleton";
import { toast } from "sonner";
import { postCreateTopupOrder, TopupOrderRequestForm } from "@/api/postCreateTopupOrder";

type EarlyDischargeSheetProps = {
  transTitle: string;
  zpTransId: string;
};

const EarlyDischargeSheet = ({
  transTitle,
  zpTransId,
  ...props
}: React.ComponentProps<typeof Drawer> & EarlyDischargeSheetProps) => {
  const [earlyDischargeInfo, setEarlyDischargeInfo] = useState<EarlyDischargeInfo | undefined | null>();
  const [loading, setLoading] = useState(false);

  const handleFetchEarlyDischagreInfo = async () => {
    setLoading(true);
    try {
      const response = await getEarlyDischarge({ zpTransId });
      setEarlyDischargeInfo(response.early_discharge);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateEarlyDischargeOrder = async () => {
    if (
      !earlyDischargeInfo?.detail?.early_discharge_amount ||
      Number(earlyDischargeInfo.detail.early_discharge_amount) <= 0
    ) {
      toast.error("Số tiền phải lớn hơn 0");
      return;
    }

    try {
      const payload: TopupOrderRequestForm = {
        amount: earlyDischargeInfo?.detail?.early_discharge_amount,
        zp_trans_id: zpTransId,
      };

      //disable focus trap of Drawer when open Cashier subview
      document.body.style.pointerEvents = "";

      const order = await postCreateTopupOrder(payload);
      if (order && order.zp_trans_token && order.app_id) {
        await window.zlpSdk?.Payment.startCashier({
          orders: [
            {
              order_type: 1,
              order: {
                app_id: order.app_id,
                zp_trans_token: order.zp_trans_token,
              },
            },
          ],
        });
      }
    } catch {
      toast.error("Đã có lỗi khi thực hiện yêu cầu");
    }
  };

  useEffect(() => {
    handleFetchEarlyDischagreInfo();
  }, []);

  const isError = !earlyDischargeInfo?.detail || !earlyDischargeInfo?.info?.allowed;
  const bottomSheetTitle = isError ? "Đã có lỗi xảy ra" : "Tất toán dư nợ Trả góp";

  return (
    <Drawer {...props}>
      <DrawerContent>
        <DrawerHeader className={"w-full p-0 gap-0 rounded-t-lg bg-white"}>
          <div className="relative flex items-center justify-center w-full h-12 p-4">
            <DrawerTitle className="font-bold text-lead">{bottomSheetTitle}</DrawerTitle>
            <VisuallyHidden>
              <DrawerDescription></DrawerDescription>
            </VisuallyHidden>
            <DrawerClose
              className={cn(
                "absolute top-1/2 -translate-y-1/2 right-4 active:scale-95 transition-transform duration-300"
              )}
              asChild>
              <Button variant="ghost" className="p-0 rounded-full h-fit">
                <XIcon className="w-6 h-6 text-gray-500" strokeWidth={3} />
              </Button>
            </DrawerClose>
          </div>
        </DrawerHeader>
        <article className="w-full">
          {loading ? (
            <div className="py-2 bg-white rounded-lg">
              <div className="flex gap-3 px-4 py-3">
                <div className="flex items-center justify-center rounded-sm w-9 h-9 aspect-square bg-blue-50">
                  <Skeleton className="w-3 h-3 aspect-square" />
                </div>
                <div className="flex flex-col gap-1">
                  <Skeleton className="w-8 h-2" />
                  <Skeleton className="w-12 h-2" />
                </div>
              </div>
              <Divider orientation="horizontal" type="dot" shape="circle" className="px-4" />
              <ul className="w-full">
                <li className="flex items-center justify-between w-full p-4">
                  <Skeleton className="w-8 h-2" />
                  <Skeleton className="w-10 h-2" />
                </li>
                <li className="flex items-center justify-between w-full p-4">
                  <Skeleton className="w-10 h-2" />
                  <Skeleton className="w-10 h-2" />
                </li>
                <li className="flex items-center justify-between w-full p-4">
                  <Skeleton className="w-10 h-2" />
                  <Skeleton className="w-10 h-2" />
                </li>
              </ul>
            </div>
          ) : (
            <div className="w-full p-4">
              {isError ? (
                <div className="flex flex-col items-center self-stretch justify-center gap-4 text-center animate-force-in">
                  <img className="object-cover w-28 h-28" src={images.ErrorState} alt="Error image" />
                  <h3 className="font-bold text-lead text-dark-500">{'Vui lòng bấm "Thử lại"'}</h3>
                </div>
              ) : (
                <div className="py-2 bg-white rounded-lg">
                  <div className="flex gap-3 px-4 py-3">
                    <div className="flex items-center justify-center rounded-sm w-9 h-9 aspect-square bg-blue-50">
                      <img
                        className="w-3 h-3 aspect-square"
                        alt="Early discharge icon"
                        src={images.IconEarlyDischarge}
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <h3 className="text-base text-dark-500">Khoản trả góp</h3>
                      <span className="text-base text-dark-300">{transTitle}</span>
                    </div>
                  </div>
                  <Divider orientation="horizontal" type="dot" shape="circle" className="px-4" />
                  <ul className="w-full">
                    <li className="flex items-center justify-between w-full p-4">
                      <span className="text-base text-dark-500">Tổng thanh toán</span>
                      <span className="text-base font-bold text-right text-blue-500">
                        {formatCurrency(Number(earlyDischargeInfo?.detail?.early_discharge_amount))}
                      </span>
                    </li>
                    <li className="flex items-center justify-between w-full p-4">
                      <span className="text-base text-dark-300">Dư nợ còn lại</span>
                      <span className="text-base text-right text-dark-500">
                        {formatCurrency(Number(earlyDischargeInfo?.detail?.total_outstanding_amount))}
                      </span>
                    </li>
                    <li className="flex items-center justify-between w-full p-4">
                      <span className="text-base text-dark-300">Phí tất toán</span>
                      <span className="text-base text-right text-dark-500">
                        {Number(earlyDischargeInfo?.detail?.early_discharge_fee) === 0
                          ? "Miễn phí"
                          : formatCurrency(Number(earlyDischargeInfo?.detail?.early_discharge_fee))}
                      </span>
                    </li>
                  </ul>
                </div>
              )}
            </div>
          )}
        </article>
        <div className="flex items-center justify-center w-full gap-3 p-4 bg-white">
          <DrawerTrigger asChild>
            <Button variant="outlined" className="flex-1 p-5 text-lead">
              Quay lại
            </Button>
          </DrawerTrigger>
          <Button
            variant="primary"
            className="flex-1 p-5 font-bold text-lead"
            onClick={isError ? () => handleFetchEarlyDischagreInfo() : () => handleCreateEarlyDischargeOrder()}>
            {isError ? "Thử lại" : "Tất toán"}
          </Button>
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default memo(EarlyDischargeSheet);
