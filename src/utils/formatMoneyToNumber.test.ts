import { formatMoneyToNumber } from './formatMoneyToNumber';

describe(formatMoneyToNumber.name, () => {
  it('return 0 when given 0đ or 0', () => {
    expect(formatMoneyToNumber('0đ')).toEqual(0);
    expect(formatMoneyToNumber('0')).toEqual(0);
  });
  it('return 1000 when given 1.000đ', () => {
    expect(formatMoneyToNumber('1.000đ')).toEqual(1000);
  });
  it('return 1 when given string 1a', () => {
    expect(formatMoneyToNumber('1a')).toEqual(1);
  });
  it('return 1000 when given string 1.000a', () => {
    expect(formatMoneyToNumber('1.000a')).toEqual(1000);
  });
  it('return empty string when given string a', () => {
    expect(formatMoneyToNumber('a')).toEqual('');
  });
  it('return 1 when given string 01', () => {
    expect(formatMoneyToNumber('01')).toEqual(1);
  });
  it('return empty when given string empty', () => {
    expect(formatMoneyToNumber('')).toEqual('');
  });
});
