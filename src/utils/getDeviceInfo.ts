
const getZpiOS = () => {
  const MobileDetect = require('mobile-detect');
  const deviceInfo = new MobileDetect(navigator.userAgent);
  return deviceInfo.os() === 'AndroidOS' ? 'android' : 'ios';
};

const getDeviceInfo = () => {
  /*
    getModel on ios not working smooth cause lag when using, suggest from document to use getDeviceId instead.
  */
  return {
    deviceOS: getZpiOS(),
  };
};

export default getDeviceInfo;
