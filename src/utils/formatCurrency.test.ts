import { formatCurrency, VI_CURRENCY } from './formatCurrency';

describe(formatCurrency.name, () => {
  it('returns empty string if value is undefined', () => {
    expect(formatCurrency(undefined)).toEqual('');
  });

  it('returns "123.456.789đ" if value is 123456789', () => {
    expect(formatCurrency(123456789)).toEqual('123.456.789đ');
  });

  it('returns "123.456.789đ" if value is 123456789 and currency is "đ"', () => {
    expect(formatCurrency(123456789, VI_CURRENCY)).toEqual('123.456.789đ');
  });

});
