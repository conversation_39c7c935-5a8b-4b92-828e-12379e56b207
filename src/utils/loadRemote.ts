type Container = {
  get(module: string): () => Promise<any>;
  init(shareScope: any): void;
};

declare global {
  interface Window {
    [key: string]: Container | any;
  }
}
declare const __webpack_share_scopes__: any;

/**
 * Dynamically loads a remote module using Module Federation
 *
 * @param remoteUrl - The URL of the remote container (where the remoteEntry.js is hosted)
 * @param remoteName - The name of the remote container (as defined in the Webpack config)
 * @param exposedModule - The exposed module path (e.g., './ModuleName')
 * @returns A promise that resolves with the module factory
 */

export const loadRemote = async (remoteUrl: string, remoteName: string, moduleName: string) => {
  // Dynamically load the remote entry script if it's not already loaded
  if (!window[remoteName]) {
    await new Promise<void>((resolve, reject) => {
      const script = document.createElement('script');
      script.src = remoteUrl;
      script.onload = () => {
        console.log(`Remote ${remoteName} script loaded`);
        resolve();
      };
      script.onerror = () => {
        console.error(`Failed to load remote script: ${remoteUrl}`);
        reject(new Error(`Failed to load remote script: ${remoteUrl}`));
      };
      document.head.appendChild(script);
    });
  }

  // Check if the container is initialized before initializing it
  const container = window[remoteName] as any;
  if (!container) {
    throw new Error(`Remote container ${remoteName} is not available on the window object`);
  }

  if (!container.__initialized) {
    try {
      // Initialize the container with the shared scope if it hasn't been initialized yet
      const sharedScope = __webpack_share_scopes__.default;
      await container.init(sharedScope);
      container.__initialized = true; // Mark the container as initialized
      console.log(`${remoteName} initialized with shared scope.`);
    } catch (error) {
      console.error(`Error initializing remote container ${remoteName}:`, error);
      throw new Error(`Failed to initialize remote container ${remoteName}`);
    }
  } else {
    console.warn(`${remoteName} is already initialized. Skipping reinitialization.`);
  }

  // Load the module from the remote container
  try {
    const factory = await container.get(moduleName);
    const Module = factory();
    return Module;
  } catch (error) {
    console.error(`Failed to load module ${moduleName} from ${remoteName}:`, error);
    throw new Error(`Failed to load module ${moduleName} from remote ${remoteName}`);
  }
};
