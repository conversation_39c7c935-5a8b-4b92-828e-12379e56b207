export const buildDeeplink = (appID: string, params?: Record<string, string | number | boolean>) => {
  const queryString = params
    ? Object.keys(params)
        .map(key => {
          const value = params[key];
          // Convert boolean to string if applicable
          const stringValue = typeof value === 'boolean' ? String(value) : value;
          // Encode the key and value, but don't encode slashes in the value
          return `${encodeURIComponent(key)}=${encodeURIComponent(stringValue).replace(/%2F/g, '/')}`;
        })
        .join('&')
    : '';

  return `zalopay://launch/app/${appID}${queryString ? `?${queryString}` : ''}`;
};
