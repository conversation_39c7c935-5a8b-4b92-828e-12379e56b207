import { normalizeVietnamese } from './normalizeVietnamese';

describe('normalizeVietnamese', () => {
  it('should return an empty string when input is empty', () => {
    expect(normalizeVietnamese('')).toBe('');
  });

  it('should remove diacritics and replace Vietnamese characters correctly', () => {
    expect(normalizeVietnamese('Thủ Đô <PERSON> Nộ<PERSON>')).toEqual('Thu Do Ha Noi');
    expect(normalizeVietnamese('Sài Gòn')).toEqual('Sai Gon');
    expect(normalizeVietnamese('Thà rằng như thế - Ưng Hoàng Phú<PERSON>')).toEqual('Tha rang nhu the - Ung <PERSON>');
  });

  it('should normalize string with mixed Vietnamese and non-Vietnamese characters', () => {
    expect(normalizeVietnamese('Cà phê phố <PERSON>')).toEqual('Ca phe pho <PERSON>');
    expect(normalizeVietnamese('Vũ Điệu <PERSON>')).toEqual('<PERSON>u <PERSON>');
    expect(normalizeVietnamese('Cầu <PERSON>àng <PERSON> Nẵng')).toEqual('Cau <PERSON>g <PERSON>');
  });
  
  it('should handle special characters and numbers', () => {
    expect(normalizeVietnamese('!@# %^123')).toEqual('!@# %^123');
    expect(normalizeVietnamese('Tiếng Việt 123')).toEqual('Tieng Viet 123');
  });
  
});
