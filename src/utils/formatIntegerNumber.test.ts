import { formatIntegerNumber } from './formatIntegerNumber';

describe(formatIntegerNumber.name, () => {
  it('returns empty string if value is undefined', () => {
    expect(formatIntegerNumber(undefined)).toEqual('');
  });

  it('returns "123.456.789" if value is 123456789', () => {
    expect(formatIntegerNumber(123456789)).toEqual('123.456.789');
  });

  it('formats number with fractionDigits', () => {
    expect(formatIntegerNumber(123.4567)).toEqual('123');
  });
});
