import { useCallback } from "react";
import { trackEvent as ZLPTrackEvent } from "../lib/ZalopaySDK/tracking";
import { appStore } from "@/store/appStore";
import { PartnerCode, PLATFORM } from "@/constants";
import { utmStore } from "@/store/utmStore";

export const useTracking = (screenId: string) => {
  const utmSource = utmStore(state => state.utmSource);
  const platform = appStore(state => state.appInfo?.platform);

  const platformTrackingId = platform === PLATFORM.ZPA ? "01" : "02";
  const partner = PartnerCode.CIMB;
  
  const trackEvent = useCallback(
    (eventId: string, metadata: Record<string, any> = {}) => {
      ZLPTrackEvent(`${platformTrackingId}.${screenId}.${eventId}`, { utm_source: utmSource, partner, ...metadata, });
    },
    [screenId, utmSource, partner, platformTrackingId],
  );
  return { trackEvent };
};
