import { useEffect } from 'react';

/**
 * hook to lazy load core apps in ZMP/ZPI (pin, bio, cashier ...)
 * more detail: https://confluence.zalopay.vn/x/9J-2Cg
 */
export const useMountCoreApps = () => {
  useEffect(() => {
    const handleMount = () => {
      const isZPA = navigator.userAgent.includes('ZaloPayClient') || navigator.userAgent.includes('ZaloPayWebClient');
      if (!isZPA && (window as any)?.__mountCoreApps__) {
        (window as any)
          ?.__mountCoreApps__()
          ?.then((modules: any) => {
            console.log('Success to mount core apps', modules);
          })
          .catch((error: any) => {
            console.log('Failed to mount core apps: ', error);
          });
      }
    };
    handleMount();
  }, []);
};
