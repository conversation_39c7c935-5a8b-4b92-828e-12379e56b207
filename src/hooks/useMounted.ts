import { useState, useEffect, useRef, useLayoutEffect } from 'react';

export const useMounted = (delay?: number) => {
  const [mounted, setMounted] = useState(false);
  const timer = useRef<any>(null);
  useLayoutEffect(() => {
    if (delay) {
      timer.current = setTimeout(() => setMounted(true), delay);
    } else {
      setMounted(true);
    }
    return () => timer.current && clearTimeout(timer.current);
  }, []);

  return mounted;
};
