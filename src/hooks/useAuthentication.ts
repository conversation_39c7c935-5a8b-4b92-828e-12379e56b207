// import { useAuthentication as _useAuthentication } from "auth_challenge/AuthenticationContext";
import { UMAuthResult, startAuthChallenge } from "../lib/ZalopaySDK/startAuthChallenge";
import { PLATFORM, UMAuthType, UMStatus } from "../constants";
import { appStore } from "../store/appStore";
import { Logger } from "../lib/logger";
export interface IAuthChallenge {
  source: number;
  authType: UMAuthType;
  params?: Object;
  options?: Object;
  skipResult?: boolean;
  numberOfRetries?: number;
}

const authenticationLogger = new Logger("Authentication");

export const useAuthentication = () => {
  const launchAuthChallenge = async (payload: IAuthChallenge): Promise<UMAuthResult> => {
    const platform = appStore.getState().appInfo?.platform?.toUpperCase();
    authenticationLogger.info(`launchAuthChallenge`, { payload });
    if (platform === PLATFORM.ZPA) {
      const result = await startAuthChallenge({ payload });
      authenticationLogger.info(`ZPA Result`, { payload, result });

      return result?.data
        ? result.data
        : {
            status: UMStatus.Failure,
            result: "",
          };
    } else {
      return new Promise((resolve, _reject) => {
        if (!window.UM_AUTH_CHALLENGE?.openAuthChallenge) {
          const error = {
            errorCode: "CALL_FUNC_OPEN_AUTH_CHALLENGE_FAILED",
            errorMessage: "openAuthChallenge not ready'",
          };
          authenticationLogger.info("ZPI Result", { payload, error });
          throw error;
        }

        window.UM_AUTH_CHALLENGE?.openAuthChallenge({
          ...payload,
          onCompleted: (result: any) => {
            authenticationLogger.info("ZPI Result", { payload, result });
            resolve({
              status: result.status,
              result: result.resultData,
            } as UMAuthResult);
          },
        });
      });
    }
  };
  return { launchAuthChallenge };
};
