import { useState } from 'react';

type StorageValue = any;

const useLocalStorage = (key: string): [StorageValue, (value: StorageValue) => void, () => void] => {
  const getStorageValue = (): StorageValue => {
    const storedValue = localStorage.getItem(key);
    return storedValue;
  };

  const [value, setValue] = useState<StorageValue>(getStorageValue());

  const setStorageValue = (newValue: StorageValue): void => {
    setValue(newValue);
    if (newValue === null) {
      localStorage.removeItem(key);
    } else {
      localStorage.setItem(key, newValue);
    }
  };

  const resetStorageValue = (): void => {
    setValue(null);
    localStorage.removeItem(key);
  };

  return [value, setStorageValue, resetStorageValue];
};

export default useLocalStorage;
