import { act, renderHook } from "@testing-library/react-hooks";
import { useDocumentTitle } from "./useDocumentTitle";

describe("useDocumentTitle", () => {
  it("should set the document title to the initial title", () => {
    const { result } = renderHook(() => useDocumentTitle("My Title"));
    expect(document.title).toBe("My Title");
  });

  it("should set the document title to the new title when calling setTitle", () => {
    const { result } = renderHook(() => useDocumentTitle());
    act(() => {
      result.current.setTitle("New Title");
    });
    expect(document.title).toBe("New Title");
  });
});
