import { renderHook, act } from '@testing-library/react-hooks';
import useLocalStorage from './useLocalStorage';

describe('useLocalStorage', () => {
  it('should return the stored value', () => {
    const key = 'test-key';
    localStorage.setItem(key, 'test-value');

    const { result } = renderHook(() => useLocalStorage(key));

    expect(result.current[0]).toBe('test-value');
  });

  it('should return null if the stored value does not exist', () => {
    const key = 'test-key';
    localStorage.removeItem(key);

    const { result } = renderHook(() => useLocalStorage(key));

    expect(result.current[0]).toBeNull();
  });

  it('should set the stored value', () => {
    const key = 'test-key';
    const value = 'test-value';

    const { result } = renderHook(() => useLocalStorage(key));

    act(() => {
      result.current[1](value);
    });

    expect(localStorage.getItem(key)).toBe(value);
  });

  it('should remove the stored value if the new value is null', () => {
    const key = 'test-key';
    const value = 'test-value';

    const { result } = renderHook(() => useLocalStorage(key));

    act(() => {
      result.current[1](value);
    });

    expect(localStorage.getItem(key)).toBe(value);

    act(() => {
      result.current[1](null);
    });

    expect(localStorage.getItem(key)).toBeNull();
  });

  it('should reset the stored value', () => {
    const key = 'test-key';
    const value = 'test-value';

    const { result } = renderHook(() => useLocalStorage(key));

    act(() => {
      result.current[1](value);
    });

    expect(localStorage.getItem(key)).toBe(value);

    act(() => {
      result.current[2]();
    });

    expect(localStorage.getItem(key)).toBeNull();
  });
});
