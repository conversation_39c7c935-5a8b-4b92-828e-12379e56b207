{"compilerOptions": {"target": "es2018", "module": "es2015", "lib": ["dom", "dom.iterable", "es2018"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "paths": {"react": ["./node_modules/@types/react"], "@/*": ["./src/*"]}}, "include": ["src"]}