#!/usr/bin/env bash

HOME=$(pwd)

# Load env vars
. "${HOME}/scripts/load-env-with-arg.sh" $1

echo "${HOME},${PUBLIC_URL}"
# Run build resource
yarn run build
if [ $? -eq 0 ]; then
  echo "SUCCESSFUL: Build resource success"
else
  echo "ERROR: Build resource error"
  exit 1
fi

# Check exist and install rsync
if ! [ -x "$(command -v rsync)" ]; then
  apk add rsync
fi

# Check exist and install curl
if ! [ -x "$(command -v curl)" ]; then
  apk add curl
fi

TAG="${npm_package_version}"

# Variables
CDN_SRC="${HOME}/${BUILD_DIR}-cdn"
CDN_DESC="${CDN_USERNAME}@${CDN_SERVER_IP}:${CDN_SERVER_DIR}"

ENABLE_RELEASE=1

if [ $1 == 'prod' ]; then
  ENABLE_RELEASE=0
fi

if [ ! -z $CI_PIPELINE_SOURCE ]; then
  CDN_DESC="${CDN_USERNAME}@${CDN_SERVER_IP}::${CDN_SERVER_DIR}"
fi

# Check existing of build dir
if [ ! -d $BUILD_DIR ]; then
  echo "ERROR: not found build directory ${BUILD_DIR}"
  exit 1
fi

mkdir -pv "${CDN_SRC}/${APP_NAME}/${TAG}"
cp -a "${HOME}/${BUILD_DIR}/." "${CDN_SRC}/${APP_NAME}/${TAG}"
if [ $? -eq 0 ]; then
  echo "SUCCESSFUL: Made the CDN temp dir success"
else
  echo "ERROR: Make the CDN temp dir error"
  exit 1
fi

# Sync resource to CDN server
echo "INFO: Syncing resource from ${CDN_SRC}/ to ${CDN_DESC} ..."
rsync -avrzc "${CDN_SRC}/" "${CDN_DESC}"
if [ $? -eq 0 ]; then
  echo "SUCCESSFUL: Sync resource done"
else
  echo "ERROR: Sync resource error"
  exit 1
fi

# Compose release data
REALESE_DATA="{
  \"micro_app_id\": \"${APP_ID}\",
  \"version_name\": \"${TAG}\",
  \"description\": \"Release version ${TAG}\",
  \"url\": \"${PUBLIC_URL}\",
  \"status\": ${ENABLE_RELEASE},
  \"release_by\": \"${CI_COMMIT_AUTHOR}\"
}"
echo "INFO: REALESE_DATA: ${REALESE_DATA}"
echo "INFO: Creating new release..."

# Request API create new release
RELEASE_STATUS=$(
  curl --write-out %{http_code} --silent --output /dev/null --location --request POST "${CONFIG_TOOL_API_URL}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json" \
    --data "${REALESE_DATA}"
)
if [ $RELEASE_STATUS -eq 200 ]; then
  echo "SUCCESSFUL: Created new release"
else
  echo "ERROR: Create new release error"
  exit 1
fi

