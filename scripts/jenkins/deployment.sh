#!/usr/bin/env bash
 
# Enable "exit on error" mode
set -e
 
HOME=$(pwd)
IMAGE_TAG=$1
 
echo "Start Load env vars ============================="
echo "Loading variables ..."
echo "${HOME}/scripts/jenkins/load-env-vars-with-arg.sh" pro "${IMAGE_TAG}"
. "${HOME}/scripts/jenkins/load-env-vars-with-arg.sh" pro "${IMAGE_TAG}"
echo "End Load env vars ==============================="
 
echo "Start check version exist ======================="
sh "${HOME}/scripts/jenkins/check-version-exist.sh"
echo "End check version exist ========================="
 
echo "Start build resource ============================"
sh "${HOME}/scripts/jenkins/build-resource.sh"
echo "End build resource =============================="
 
echo "Start sync resource to CDN ======================"
sh "${HOME}/scripts/jenkins/sync-resource.sh"
 
# Expose CDN url
echo "CDN_URL:${PUBLIC_URL}/remoteEntry.js"
echo "End sync resource to CDN ========================="