/* istanbul ignore file */
import { cdn_images as currentImages } from '../src/res/images/cdn';
require('dotenv').config();
const fs = require('fs');
const { join } = require('path');
const ftp = require('basic-ftp');

const BASE_URL_IMAGE_CDN = `https://simg.zalopay.com.vn/fs/${process.env.FTP_FOLDER}/images`;
const BASE_URL_ANIMATION_CDN = `https://simg.zalopay.com.vn/fs/${process.env.FTP_FOLDER}/animations`;
const CACHE_KEY = new Date().valueOf(); //Unix Timestamp 
/**
 * param: modeGenerate = all is default will generate image both local and cdn folder
 * param: modeGenerate = local only generate image from local folder
 * param: modeGenerate = cdn only generate image from cdn folder
 */
const modeGenerate = process.argv[2] || 'all';
if (['all', 'local', 'cdn'].includes(modeGenerate) === false) {
  console.log('Param modeGenerate must be value is all/local/cdn, default value is all');
  process.exit();
}

/**
 * param: modeUpload = create is default will upload new image to CDN
 * param: modeUpload = update will upload all of image to CDN include image changed name
 */
const modeUpload = process.argv[3] || 'create';
if (['create', 'update'].includes(modeUpload) === false) {
  console.log('Param modeUpload must be value is create/update, default value is create');
  process.exit();
}

const traverseDir = (dir: string) => {
  return fs.readdirSync(dir).map((file: string) => {
    let fullPath = join(dir, file);
    if (fs.lstatSync(fullPath).isDirectory()) {
      return traverseDir(fullPath);
    } else {
      return fullPath;
    }
  });
};

const getFileName = (file: string) => {
  return file
    .split('/')
    .filter(item => {
      return item.endsWith('.png') || item.endsWith('.lottie') || item.endsWith('.riv') || item.endsWith('.json') || item.endsWith('.svg')
    })?.[0]
    ?.replace('.png', '')
    ?.replace('.lottie', '')
    ?.replace('.riv', '')
    ?.replace('.json', '')
    ?.replace('.svg', '');
};

const generateCDNImages = async () => {
  console.log('Generating cdn images...');
  const imagesLocalPath = 'src/res/images/cdn';
  const imagesCDNPath = `fs/${process.env.FTP_FOLDER}/images`;
  const newImages: { [key: string]: string } = {};
  const properties = traverseDir(imagesLocalPath)
    .flat(Infinity)
    .filter((item: string) => item.endsWith('.png') || item.endsWith('.svg'))
    .map((file: string) => file.replace(`${imagesLocalPath}/`, ''))
    .map((file: string) => {
      const fileName = getFileName(file);
      console.log("fileName", fileName)
      if(fileName === undefined || fileName === "undefined") {
        return;
      }
      newImages[fileName] = file;
      return `${fileName}: '${BASE_URL_IMAGE_CDN}/${file}?cacheKey=${CACHE_KEY}',`;
    })
    .join('\n  ');

  const string = `//DO NOT EDIT
//this file should not be edited manually, instead run script sync_image_res to update new image in res/images folder
export const cdn_images = {
  ${properties.trim()}
};
`;

  fs.writeFileSync(`${imagesLocalPath}/index.ts`, string, 'utf8');

  const client = new ftp.Client();
  client.ftp.verbose = true;
  try {
    await client.access({
      host: process.env.FTP_HOST,
      user: process.env.FTP_USER,
      password: process.env.FTP_PASS,
    });
    console.log('Uploading to FTP server...');
    if (modeUpload === 'update') {
      console.log('Running mode update...');
      await client.uploadFromDir(imagesLocalPath, imagesCDNPath);
      await client.remove(`${imagesCDNPath}/index.ts`);
    }
    if (modeUpload === 'create') {
      console.log('Running mode create...');
      const newKeys = Object.keys(newImages).filter(item => !Object.keys(currentImages).includes(item));
      console.log('Number of changed file is ' + newKeys.length);
      for (const key of newKeys) {
        await client.uploadFrom(`${imagesLocalPath}/${newImages[key]}`, `${imagesCDNPath}/${newImages[key]}`);
      }
    }
    console.log('Upload to FTP server success');
  } catch (err) {
    console.log('Upload to FTP server error:', err);
  }
  client.close();
};

const generateCDNAnimations = async () => {
  console.log('Generating CDN animations...');
  const animationsLocalPath = 'src/res/animations/cdn';
  const animationsCDNPath = `fs/${process.env.FTP_FOLDER}/animations`;
  const newImages: { [key: string]: string } = {};
  const properties = traverseDir(animationsLocalPath)
    .flat(Infinity)
    .map((file: string) => file.replace(`${animationsLocalPath}/`, ''))
    .map((file: string) => {
      const fileName = getFileName(file);
      if(fileName === undefined || fileName === "undefined") {
        return;
      }
      newImages[fileName] = file;
      return `${fileName}: '${BASE_URL_ANIMATION_CDN}/${file}?cacheKey=${CACHE_KEY}',`;
    })
    .join('\n  ');

  const string = `//DO NOT EDIT
//this file should not be edited manually, instead run script sync_animation_res to update new image in res/animations folder
export const animations = {
  ${properties.trim()}
};
`;

  fs.writeFileSync('src/res/animations/index.ts', string, 'utf8');

  const client = new ftp.Client();
  client.ftp.verbose = true;
  try {
    await client.access({
      host: process.env.FTP_HOST,
      user: process.env.FTP_USER,
      password: process.env.FTP_PASS,
    });
    console.log('Uploading to FTP server...');
    if (modeUpload === 'update') {
      console.log('Running mode update...');
      await client.uploadFromDir(animationsLocalPath, animationsCDNPath);
      //await client.remove(`${animationsCDNPath}/index.ts`);
    }
    if (modeUpload === 'create') {
      console.log('Running mode create...');
      const newKeys = Object.keys(newImages).filter(item => !Object.keys(currentImages).includes(item));
      console.log('Number of changed file is ' + newKeys.length);
      for (const key of newKeys) {
        await client.uploadFrom(`${animationsLocalPath}}/${newImages[key]}`, `${animationsCDNPath}}/${newImages[key]}`);
      }
    }
    console.log('Upload to FTP server success');
  } catch (err) {
    console.log('Upload to FTP server error:', err);
  }
  client.close();
};

const generateLocalImages = () => {
  console.log('Generating local images...');
  const properties = traverseDir('src/res/images/local')
    .flat(Infinity)
    .filter((item: string) => item.endsWith('.png') || item.endsWith('.svg'))
    .map((file: string) => file.replace('src/res/images/local/', ''))
    .map((file: string) => {
      const fileName = getFileName(file);
      console.log("fileName", fileName)
      if(fileName === undefined || fileName === "undefined") {
        return;
      }
      return `${fileName}: require('./${file}'),`;
    })
    .join('\n  ');

  const string = `//DO NOT EDIT
//this file should not be edited manually, instead run script sync_image_res to update new image in res/images folder
export const local_images = {
  ${properties.trim()}
};
`;

  fs.writeFileSync('src/res/images/local/index.ts', string, 'utf8');
};

if (modeGenerate === 'all' || modeGenerate === 'cdn') {
  generateCDNImages();
  generateCDNAnimations();
}

if (modeGenerate === 'all' || modeGenerate === 'local') {
  generateLocalImages();
}
