import sharp from 'sharp';
import path from 'path';
import fs from 'fs';

const sourceFolder = 'src/res/images';
let totalSavedMemory = 0;

// Function to recursively compress images in the given folder
function compressImages(folder: string) {
  const files = fs.readdirSync(folder);

  files.forEach(file => {
    const filePath = path.join(folder, file);

    if (fs.statSync(filePath).isDirectory()) {
      // Recursively compress images in subfolders
      compressImages(filePath);
    } else if (file.toLowerCase().match(/\.(jpg|jpeg|png)$/)) {
      // Compress the image using Sharp and overwrite it
      const originalSize = fs.statSync(filePath).size;
      sharp(filePath)
        .png({ quality: 80, progressive: true })
        .toBuffer()
        .then(data => {
          fs.writeFileSync(filePath, data);
          const compressedSize = data.length;
          totalSavedMemory += originalSize - compressedSize;
          console.log(`Image ${filePath} compressed. Memory saved: ${originalSize - compressedSize} bytes`);
        })
        .catch(err => {
          console.error(`Error compressing image ${filePath}: ${err}`);
        });
    }
  });
}

// Iterate over each image file in the source folder and its subfolders
compressImages(sourceFolder);

console.log(`Total memory saved: ${Math.abs(totalSavedMemory)} bytes`);
