#!/usr/bin/env bash

HOME=$(pwd)

# Load env vars
. "${HOME}/scripts/load-env-vars.sh"

# Check existing of version of this release
echo "INFO: Checking existing of version..."
EXISTING_STATUS=$(curl --write-out %{http_code} --output /dev/null --location --request GET "$PUBLIC_URL/remoteEntry.js")
if [ $EXISTING_STATUS -eq 200 ]; then
  echo "ERROR: version ${npm_package_version} already exist"
  exit 1
else
  echo "SUCCESSFUL: version is valid"
fi
