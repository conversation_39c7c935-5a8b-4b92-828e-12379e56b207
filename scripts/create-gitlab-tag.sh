#!/usr/bin/env bash

HOST="https://gitlab.zalopay.vn/api/v4/projects/"

HOME=$(pwd)

PRIVATE_TOKEN=$1
tag=$2

# Check exist and install curl
if ! [ -x "$(command -v curl)" ]; then
  apk add curl
fi

echo "INFO: Creating tag ${tag}..."
RESPONSE=$(
  curl -sw "%{http_code}" --request POST  \
  "${HOST}${CI_PROJECT_ID}/repository/tags?tag_name=${tag}&ref=${CI_COMMIT_BRANCH}" \
   --header "PRIVATE-TOKEN:${PRIVATE_TOKEN}"
)

HTTP_CODE="${RESPONSE:${#RESPONSE}-3}"
BODY="${RESPONSE:0:${#RESPONSE}-3}"
if [ $HTTP_CODE -eq 201 ]; then
  echo "SUCCESSFUL: tag is created successfully"
else
  echo "ERROR: failed when creating tag (code: ${HTTP_CODE}, body: ${BODY})"
  exit 1
fi
