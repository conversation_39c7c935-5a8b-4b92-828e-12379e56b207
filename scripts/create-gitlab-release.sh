#!/usr/bin/env bash

HOST="https://gitlab.zalopay.vn/api/v4/projects/"

HOME=$(pwd)
IFS=$'\n' # replace bash default boundary detector
PRIVATE_TOKEN=$1
RELEASE_TAG=$2
RELEASE_NOTE=""

# Check exist and install curl
if ! [ -x "$(command -v curl)" ]; then
  apk add curl
fi

# STEP 1: get all commit from default branch to release tag.
# use tformat to replace git log default separator (\n) with terminator which cause STEP 2 failed
GIT_LOGS=$(git log --pretty="tformat:- %h %s")
to_replace='"'
replacement='\"'
for commit in $GIT_LOGS
do
    RELEASE_NOTE+="${commit//$to_replace/$replacement}"
done


# STEP 2: build POST curl request to create Gitlab release with built release note
REQUEST_BODY="{ \"name\": \"Release ${RELEASE_TAG}\", \"tag_name\": \"${RELEASE_TAG}\", \"description\": \"${RELEASE_NOTE}\"}"
echo "INFO: Creating release ${RELEASE_TAG} with data: \n ${REQUEST_BODY}"
RESPONSE=$(
  curl -sw "%{http_code}" --request POST "${HOST}${CI_PROJECT_ID}/releases" \
       --header 'Content-Type: application/json' --header "PRIVATE-TOKEN:${PRIVATE_TOKEN}" \
       --data "$REQUEST_BODY" \
)

# STEP 3: check response code to verify release is created successfully
HTTP_CODE="${RESPONSE:${#RESPONSE}-3}"
BODY="${RESPONSE:0:${#RESPONSE}-3}"
if [ $HTTP_CODE -eq 201  ]; then
   echo "SUCCESSFUL: release is created successfully"
else
  echo "ERROR: failed when creating release (code: ${HTTP_CODE}, body: ${BODY})"
  exit 1
fi
