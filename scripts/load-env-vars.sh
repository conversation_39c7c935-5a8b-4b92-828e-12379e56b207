#!/usr/bin/env bash

HOME=$(pwd)

# Extract version value from package.json
npm_package_version=$(node -p "require('./package.json').version")

# Load .env file
if [ -e "${HOME}/.env" ]; then
  . "${HOME}/.env"
fi

# Load .env.local file
if [ -e "${HOME}/.env.local" ]; then
  . "${HOME}/.env.local"
fi

if [ ! -z $CI_COMMIT_BRANCH ]; then
  if [ $CI_COMMIT_BRANCH == $DEVELOP_BRANCH ] || [ $CI_COMMIT_BRANCH == "develop" ]; then
    # Load .env.develop file
    . "${HOME}/.env.develop"
  elif [ $CI_COMMIT_BRANCH == $STAGING_BRANCH ] || [ $CI_COMMIT_BRANCH == "staging" ]; then
    # Load .env.staging file
    . "${HOME}/.env.staging"
  elif [ $CI_COMMIT_BRANCH == $PRODUCTION_BRANCH ] || [ $CI_COMMIT_BRANCH == "master" ]; then
    # Load .env.product file
    . "${HOME}/.env.product"
  fi
fi

export npm_package_version=$npm_package_version
export PUBLIC_URL=$PUBLIC_URL
export REACT_APP_ENV=$REACT_APP_ENV
export REACT_APP_MMF_BASE_URL=$REACT_APP_MMF_BASE_URL
export REACT_APP_COMMON_LOG_URL=$REACT_APP_COMMON_LOG_URL
export REACT_APP_COMMON_BASE_URL=$REACT_APP_COMMON_BASE_URL
export GENERATE_SOURCEMAP=$GENERATE_SOURCEMAP
export APP_NAME=$APP_NAME
export APP_PATH=$APP_PATH
export ADVERTISING_GATEWAY_APP_URL=$ADVERTISING_GATEWAY_APP_URL
