#!/usr/bin/env bash

HOME=$(pwd)

# Load env vars
. "${HOME}/scripts/load-env-with-arg.sh" $1


PRIVATE_TOKEN=$2
date=$(date '+%d%m%Y.%H%M')
TAG="${npm_package_version}"

echo $TAG

# create tag
. "${HOME}/scripts/create-gitlab-tag.sh" $PRIVATE_TOKEN $TAG
CREATE_TAG_STATUS=$?

if [ $CREATE_TAG_STATUS -eq 0 ]; then
  # create release
  . "${HOME}/scripts/create-gitlab-release.sh" $PRIVATE_TOKEN $TAG
fi

