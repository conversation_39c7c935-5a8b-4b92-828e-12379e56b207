#!/usr/bin/env bash

HOME=$(pwd)

# Load env vars
. "${HOME}/scripts/jenkins/load-env-vars-with-arg.sh" $1


PRIVATE_TOKEN=$2

# MINIAPP_TAG="${npm_package_version}_miniapp"
# echo "INFO: Creating miniapp tag ${MINIAPP_TAG}..."
# # create miniapp tag
# . "${HOME}/scripts/create-gitlab-tag.sh" $PRIVATE_TOKEN $MINIAPP_TAG
# CREATE_MINIAPP_TAG_STATUS=$?
# if [ $CREATE_MINIAPP_TAG_STATUS -eq 0 ]; then
#   # create release
#   . "${HOME}/scripts/create-gitlab-release.sh" $PRIVATE_TOKEN $MINIAPP_TAG
# fi

#work arround to wait for gitlab to create tag and trigger jenkins build
sleep 10

MICROAPP_TAG="${npm_package_version}_microapp"
echo "INFO: Creating microapp tag ${MICROAPP_TAG}..."
# create microapp tag
. "${HOME}/scripts/create-gitlab-tag.sh" $PRIVATE_TOKEN $MICROAPP_TAG
CREATE_MICROAPP_TAG_STATUS=$?
if [ $CREATE_MICROAPP_TAG_STATUS -eq 0 ]; then
  # create release
  . "${HOME}/scripts/create-gitlab-release.sh" $PRIVATE_TOKEN $MICROAPP_TAG
fi

