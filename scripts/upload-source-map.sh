#!/usr/bin/env bash

HOME=$(pwd)

# Check exist and install sentry-cli
if ! [ -x "$(command -v sentry-cli)" ]; then
  curl -sL https://sentry.io/get-cli/ | bash
fi

# Load env vars
. "${HOME}/scripts/load-env-vars.sh"

sentry-cli --version

echo "Creating new source-map release ..."
sentry-cli releases new $npm_package_version
if [ $? -eq 0 ]; then
  echo "SUCCESSFUL: Created source-map release"
else
  echo "ERROR: Create source-map release error"
  exit 1
fi

echo "Uploading source-map files ..."
sentry-cli releases files $npm_package_version upload-sourcemaps $BUILD_DIR
if [ $? -eq 0 ]; then
  echo "SUCCESSFUL: Uploaded source-map files"
else
  echo "ERROR: Upload source-map files error"
  exit 1
fi
