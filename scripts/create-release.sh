#!/usr/bin/env bash

HOME=$(pwd)

# Check exist and install curl
if ! [ -x "$(command -v curl)" ]; then
  apk add curl
fi

# Load env vars
. "${HOME}/scripts/load-env-vars.sh"

# Compose release data
REALESE_DATA="{
  \"micro_app_id\": \"${APP_ID}\",
  \"version_name\": \"${npm_package_version}\",
  \"description\": \"Release version ${npm_package_version}\",
  \"url\": \"${PUBLIC_URL}\",
  \"status\": 0,
  \"release_by\": \"${CI_COMMIT_AUTHOR}\"
}"
echo "INFO: REALESE_DATA: ${REALESE_DATA}"
echo "INFO: Creating new release..."

# Request API create new release
RELEASE_STATUS=$(
  curl --write-out %{http_code} --silent --output /dev/null --location --request POST "${CONFIG_TOOL_API_URL}" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json" \
    --data "${REALESE_DATA}"
)
if [ $RELEASE_STATUS -eq 200 ]; then
  echo "SUCCESSFUL: Created new release"
else
  echo "ERROR: Create new release error"
  exit 1
fi
