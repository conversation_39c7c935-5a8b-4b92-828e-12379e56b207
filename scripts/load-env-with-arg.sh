#!/usr/bin/env bash

HOME=$(pwd)

# Extract version value from package.json
date=$(date '+%d%m%Y.%H%M')
npm_package_version="$(node -p "require('./package.json').version")-${date}"

if [ ! -z $2 ]; then
  npm_package_version=$2
fi

# Load .env file
if [ -e "${HOME}/.env" ]; then
  . "${HOME}/.env"
fi

# Load .env.local file
if [ -e "${HOME}/.env.local" ]; then
  . "${HOME}/.env.local"
fi

if [ ! -z $1 ]; then
  if [ $1 == "qc" ]; then
    # Load .env.develop file
    . "${HOME}/.env.develop"
  elif [ $1 == "stg" ]; then
    # Load .env.staging file
    . "${HOME}/.env.staging"
  elif [ $1 == "cdn" ]; then
      # Load .env.staging file
      . "${HOME}/.env.cdn"
  elif [  $1 == "prod" ]; then
    # Load .env.product file
    . "${HOME}/.env.product"
  fi
fi

export npm_package_version=$npm_package_version
export PUBLIC_URL=$PUBLIC_URL
export REACT_APP_ENV=$REACT_APP_ENV
export REACT_APP_MMF_BASE_URL=$REACT_APP_MMF_BASE_URL
export REACT_APP_COMMON_LOG_URL=$REACT_APP_COMMON_LOG_URL
export REACT_APP_COMMON_BASE_URL=$REACT_APP_COMMON_BASE_URL
export GENERATE_SOURCEMAP=$GENERATE_SOURCEMAP
export APP_NAME=$APP_NAME
export APP_PATH=$APP_PATH
export ADVERTISING_GATEWAY_APP_URL=$ADVERTISING_GATEWAY_APP_URL
export AUTHENTICATION_CHALLENGE_APP_URL=$AUTHENTICATION_CHALLENGE_APP_URL