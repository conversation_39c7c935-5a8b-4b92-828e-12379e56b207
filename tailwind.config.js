const defaultTheme = require("tailwindcss/defaultTheme");
import { colors } from "./src/constants/colors";
import { extendTheme } from "./src/constants/extendTheme";

import { APP_ELEMENT_ID } from "./src/constants/index";

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{html,js,jsx,tsx}"],
  darkMode: "class",
  important: `#${APP_ELEMENT_ID}`,
  theme: {
    extend: {
      ...defaultTheme,
      colors,
      ...extendTheme,
    },
  },
  plugins: [require("tailwindcss-animate"), require("tailwind-scrollbar-hide"), require('@tailwindcss/line-clamp')],
};
