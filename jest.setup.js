global.fetch = jest.fn();

global.console = {
  ...console,
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

global.__DEV__ = true;

global.window = global;

global.flushPromises = () => new Promise(setImmediate);

window.addEventListener = () => {};

jest.mock('./src/utils/getCurrentDate');

jest.mock('./src/lib/ZalopaySDK');

const FAKE_NAVIGATION = {
  navigate: jest.fn(),
  state: {},
  dispatch: jest.fn(),
  goBack: jest.fn(),
  dismiss: jest.fn(),
  openDrawer: jest.fn(),
  closeDrawer: jest.fn(),
  toggleDrawer: jest.fn(),
  getParam: jest.fn().mockReturnValue(true),
  setParams: jest.fn(),
  addListener: jest.fn(),
  push: jest.fn(),
  replace: jest.fn(),
  pop: jest.fn(),
  popToTop: jest.fn(),
  isFocused: jest.fn(),
  dangerouslyGetParent: jest.fn(),
  setTitle: jest.fn(),
};

jest.mock('@/lib/navigation/buildNavigator', () => ({
  useNavigation: () => FAKE_NAVIGATION,
}));

// jest.mock('./src/components/ui/popup/Loading', () => ({
//   LoadingService: {
//     show: jest.fn(),
//     hide: jest.fn(),
//   },
// }));

// jest.mock('./src/utils/launchFAQDeepLinkForTag', () => ({
//   buildFaqUrl: jest.fn(),
//   launchFAQDeepLinkForTag: jest.fn(),
// }));

// jest.mock('./src/api', () => ({
//   getValidBankInfo: jest.fn(),
//   getListBank: jest.fn(),
//   getOnboardingPermission: jest.fn(),
// }));

module.exports = {
  FAKE_NAVIGATION
};
