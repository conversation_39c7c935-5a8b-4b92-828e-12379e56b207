<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="39" failures="0" errors="0" time="3.11">
  <testsuite name="LineDot" errors="0" failures="0" skipped="0" timestamp="2025-03-18T08:05:14" time="0.973" tests="3">
    <testcase classname="LineDot renders correctly with default props" name="LineDot renders correctly with default props" time="0.017">
    </testcase>
    <testcase classname="LineDot renders with specified thick" name="LineDot renders with specified thick" time="0.002">
    </testcase>
    <testcase classname="LineDot renders with specified orientation" name="LineDot renders with specified orientation" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="PermissionProvider" errors="0" failures="0" skipped="0" timestamp="2025-03-18T08:05:15" time="0.612" tests="5">
    <testcase classname="PermissionProvider should render children when ready" name="PermissionProvider should render children when ready" time="0.008">
    </testcase>
    <testcase classname="PermissionProvider should handle null permission info" name="PermissionProvider should handle null permission info" time="0.003">
    </testcase>
    <testcase classname="PermissionProvider should handle bound user with whitelist" name="PermissionProvider should handle bound user with whitelist" time="0.003">
    </testcase>
    <testcase classname="PermissionProvider should handle non-whitelisted user" name="PermissionProvider should handle non-whitelisted user" time="0.003">
    </testcase>
    <testcase classname="PermissionProvider should handle error case" name="PermissionProvider should handle error case" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="createIDBStorage" errors="0" failures="0" skipped="0" timestamp="2025-03-18T08:05:16" time="0.107" tests="7">
    <testcase classname="createIDBStorage should return stringified data when getData succeeds" name="createIDBStorage should return stringified data when getData succeeds" time="0.001">
    </testcase>
    <testcase classname="createIDBStorage should return null and log error when getData fails" name="createIDBStorage should return null and log error when getData fails" time="0">
    </testcase>
    <testcase classname="setItem should save parsed data successfully" name="setItem should save parsed data successfully" time="0">
    </testcase>
    <testcase classname="setItem should log error when saveData fails" name="setItem should log error when saveData fails" time="0.001">
    </testcase>
    <testcase classname="removeItem should delete data successfully" name="removeItem should delete data successfully" time="0">
    </testcase>
    <testcase classname="removeItem should log error when deleteData fails" name="removeItem should log error when deleteData fails" time="0">
    </testcase>
    <testcase classname="createPersistStore create store" name="createPersistStore create store" time="0">
    </testcase>
  </testsuite>
  <testsuite name="useLocalStorage" errors="0" failures="0" skipped="0" timestamp="2025-03-18T08:05:16" time="0.117" tests="5">
    <testcase classname="useLocalStorage should return the stored value" name="useLocalStorage should return the stored value" time="0.002">
    </testcase>
    <testcase classname="useLocalStorage should return null if the stored value does not exist" name="useLocalStorage should return null if the stored value does not exist" time="0.001">
    </testcase>
    <testcase classname="useLocalStorage should set the stored value" name="useLocalStorage should set the stored value" time="0.001">
    </testcase>
    <testcase classname="useLocalStorage should remove the stored value if the new value is null" name="useLocalStorage should remove the stored value if the new value is null" time="0">
    </testcase>
    <testcase classname="useLocalStorage should reset the stored value" name="useLocalStorage should reset the stored value" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="formatCurrency" errors="0" failures="0" skipped="0" timestamp="2025-03-18T08:05:16" time="0.106" tests="3">
    <testcase classname="formatCurrency returns empty string if value is undefined" name="formatCurrency returns empty string if value is undefined" time="0.001">
    </testcase>
    <testcase classname="formatCurrency returns &quot;123.456.789đ&quot; if value is 123456789" name="formatCurrency returns &quot;123.456.789đ&quot; if value is 123456789" time="0">
    </testcase>
    <testcase classname="formatCurrency returns &quot;123.456.789đ&quot; if value is 123456789 and currency is &quot;đ&quot;" name="formatCurrency returns &quot;123.456.789đ&quot; if value is 123456789 and currency is &quot;đ&quot;" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="useDocumentTitle" errors="0" failures="0" skipped="0" timestamp="2025-03-18T08:05:16" time="0.106" tests="2">
    <testcase classname="useDocumentTitle should set the document title to the initial title" name="useDocumentTitle should set the document title to the initial title" time="0.002">
    </testcase>
    <testcase classname="useDocumentTitle should set the document title to the new title when calling setTitle" name="useDocumentTitle should set the document title to the new title when calling setTitle" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="formatMoneyToNumber" errors="0" failures="0" skipped="0" timestamp="2025-03-18T08:05:17" time="0.096" tests="7">
    <testcase classname="formatMoneyToNumber return 0 when given 0đ or 0" name="formatMoneyToNumber return 0 when given 0đ or 0" time="0">
    </testcase>
    <testcase classname="formatMoneyToNumber return 1000 when given 1.000đ" name="formatMoneyToNumber return 1000 when given 1.000đ" time="0">
    </testcase>
    <testcase classname="formatMoneyToNumber return 1 when given string 1a" name="formatMoneyToNumber return 1 when given string 1a" time="0">
    </testcase>
    <testcase classname="formatMoneyToNumber return 1000 when given string 1.000a" name="formatMoneyToNumber return 1000 when given string 1.000a" time="0">
    </testcase>
    <testcase classname="formatMoneyToNumber return empty string when given string a" name="formatMoneyToNumber return empty string when given string a" time="0">
    </testcase>
    <testcase classname="formatMoneyToNumber return 1 when given string 01" name="formatMoneyToNumber return 1 when given string 01" time="0">
    </testcase>
    <testcase classname="formatMoneyToNumber return empty when given string empty" name="formatMoneyToNumber return empty when given string empty" time="0">
    </testcase>
  </testsuite>
  <testsuite name="normalizeVietnamese" errors="0" failures="0" skipped="0" timestamp="2025-03-18T08:05:17" time="0.095" tests="4">
    <testcase classname="normalizeVietnamese should return an empty string when input is empty" name="normalizeVietnamese should return an empty string when input is empty" time="0.001">
    </testcase>
    <testcase classname="normalizeVietnamese should remove diacritics and replace Vietnamese characters correctly" name="normalizeVietnamese should remove diacritics and replace Vietnamese characters correctly" time="0">
    </testcase>
    <testcase classname="normalizeVietnamese should normalize string with mixed Vietnamese and non-Vietnamese characters" name="normalizeVietnamese should normalize string with mixed Vietnamese and non-Vietnamese characters" time="0">
    </testcase>
    <testcase classname="normalizeVietnamese should handle special characters and numbers" name="normalizeVietnamese should handle special characters and numbers" time="0">
    </testcase>
  </testsuite>
  <testsuite name="formatIntegerNumber" errors="0" failures="0" skipped="0" timestamp="2025-03-18T08:05:17" time="0.085" tests="3">
    <testcase classname="formatIntegerNumber returns empty string if value is undefined" name="formatIntegerNumber returns empty string if value is undefined" time="0">
    </testcase>
    <testcase classname="formatIntegerNumber returns &quot;123.456.789&quot; if value is 123456789" name="formatIntegerNumber returns &quot;123.456.789&quot; if value is 123456789" time="0">
    </testcase>
    <testcase classname="formatIntegerNumber formats number with fractionDigits" name="formatIntegerNumber formats number with fractionDigits" time="0">
    </testcase>
  </testsuite>
</testsuites>